vboxmanage setproperty machinefolder %vmFolder%,,hide
vboxmanage import "%vmFolder%\%vm%.ova" --vsys 0 -vmname %VM%,,hide

vboxmanage modifyvm vm3 --nic1=bridged,,hide
vboxmanage modifyvm vm3 --bridgeadapter1 "eth0",,hide

vboxmanage modifyvm vm3 --macaddress1 "%MAC%",,hide
vboxmanage modifyvm vm3 --memory %vm_memory%,,hide
vboxmanage modifyvm vm3 --mouse %vm_mouse%,,hide 
vboxmanage modifyvm vm3 --graphicscontroller %vm_vga%,,hide 
vboxmanage modifyvm vm3 --vram %vga_vram%,,hide 
vboxmanage modifyvm vm3 --vrde %vm_rdp%,,hide
vboxmanage modifyvm vm3 --vrdeport %rdp_port%,,hide

vboxmanage setextradata vm3 "VBoxInternal/CPUM/EnableHVP" 0,,hide
vboxmanage setextradata vm3 "VBoxInternal/TM/TSCMode" RealTSCOffset,,hide

vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVendor" "Asus",,hide
vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVersion" "MB52.88Z.0088.B05.0904162222",,hide
vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseDate" "08/10/13",,hide
vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMajor" "5",,hide
vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMinor" "9",,hide
vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMajor" "1",,hide
vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMinor" "0",,hide

vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiSystemVendor" "Asus",,hide
vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiSystemProduct" "Asus5",,hide
vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiSystemVersion" "1.0",,hide
vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiSystemSerial" "CSN12345678901234567",,hide
vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiSystemSKU" "FM550EA#ACB",,hide
vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiSystemFamily" "Ultrabook",,hide
vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiSystemUuid" "B5FA3000-9403-81E0-3ADA-F46D045CB676",,hide

vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBoardVendor" "Asus",,hide
vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBoardProduct" "Aus-F22788AA",,hide
vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBoardVersion" "3.0",,hide
vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBoardSerial" "BSN12345678901234567",,hide
vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBoardAssetTag" "Base Board Asset Tag#",,hide
vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBoardLocInChass" "Board Loc In",,hide
vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBoardBoardType" 10,,hide

vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiChassisVendor" "Asus Inc.",,hide
vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiChassisType" 10,,hide
vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiChassisVersion" "Asus-F22788AA",,hide
vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiChassisSerial" "CSN12345678901234567",,hide
vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiChassisAssetTag" "WhiteHouse",,hide

vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxVer" "Extended version info: 1.00.00",,hide
vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxRev" "Extended revision info: 1A",,hide

;vboxmanage setextradata vm3 "VBoxInternal/Devices/piix3ide/0/Config/PrimaryMaster/ModelNumber" "Hitachi HTS543232A8A384",,hide
;vboxmanage setextradata vm3 "VBoxInternal/Devices/piix3ide/0/Config/PrimaryMaster/FirmwareRevision" "ES2OA60W",,hide
;vboxmanage setextradata vm3 "VBoxInternal/Devices/piix3ide/0/Config/PrimaryMaster/SerialNumber" "2E3024L1T2V9KA",,hide
;vboxmanage setextradata vm3 "VBoxInternal/Devices/piix3ide/0/Config/SecondaryMaster/ModelNumber" "Slimtype DVD A  DS8A8SH",,hide
;vboxmanage setextradata vm3 "VBoxInternal/Devices/piix3ide/0/Config/SecondaryMaster/FirmwareRevision" "KAA2",,hide
;vboxmanage setextradata vm3 "VBoxInternal/Devices/piix3ide/0/Config/SecondaryMaster/SerialNumber" "ABCDEF0123456789",,hide
;vboxmanage setextradata vm3 "VBoxInternal/Devices/piix3ide/0/Config/SecondaryMaster/ATAPIVendorId" "Slimtype",,hide
;vboxmanage setextradata vm3 "VBoxInternal/Devices/piix3ide/0/Config/SecondaryMaster/ATAPIProductId" "DVD A  DS8A8SH",,hide
;vboxmanage setextradata vm3 "VBoxInternal/Devices/piix3ide/0/Config/SecondaryMaster/ATAPIRevision" "KAA2",,hide

;to do add the following ahci and test on node
vboxmanage  setextradata vm3 "VBoxInternal/Devices/ahci/0/Config/Port0/ModelNumber" "Hitachi HTS543230AAA384",,hide
vboxmanage  setextradata vm3 "VBoxInternal/Devices/ahci/0/Config/Port0/FirmwareRevision" "ES2OA60W",,hide
vboxmanage  setextradata vm3 "VBoxInternal/Devices/ahci/0/Config/Port0/SerialNumber" "2E3024L1T2V9KA",,hide
vboxmanage  setextradata vm3 "VBoxInternal/Devices/ahci/0/Config/Port1/ModelNumber" "Slimtype DVD A  DS8A8SH",,hide
vboxmanage  setextradata vm3 "VBoxInternal/Devices/ahci/0/Config/Port1/FirmwareRevision" "KAA2",,hide
vboxmanage  setextradata vm3 "VBoxInternal/Devices/ahci/0/Config/Port1/SerialNumber" "ABCDEF0123456789",,hide
vboxmanage  setextradata vm3 "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIVendorId" "Slimtype",,hide
vboxmanage  setextradata vm3 "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIProductId" "DVD A  DS8A8SH",,hide
vboxmanage  setextradata vm3 "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIRevision" "KAA2",,hide

vboxmanage setextradata vm3 "VBoxInternal/Devices/acpi/0/Config/AcpiOemId" "ASUS",,hide

vboxmanage modifyvm vm3 --paravirtprovider legacy,,hide
vboxmanage modifyvm vm3 --bioslogoimagepath  "%vmscfgdir%\vm_splash.bmp",,hide
vboxmanage modifyvm vm3 --hwvirtex on,,hide
vboxmanage modifyvm vm3 --vtxvpid on,,hide
vboxmanage modifyvm vm3 --vtxux on,,hide
vboxmanage modifyvm vm3 --apic on,,hide
vboxmanage modifyvm vm3 --pae on,,hide
vboxmanage modifyvm vm3 --longmode on,,hide
vboxmanage modifyvm vm3 --hpet on,,hide
vboxmanage modifyvm vm3 --nestedpaging on,,hide
vboxmanage modifyvm vm3 --largepages on,,hide
;vboxmanage modifyvm vm3 --chipset ich9,,hide ; 22feb

vboxmanage setextradata vm3 "VBoxInternal/Devices/acpi/0/Config/DsdtFilePath" "%vmscfgdir%\ACPI-DSDT.bin",,hide
vboxmanage setextradata vm3 "VBoxInternal/Devices/acpi/0/Config/SsdtFilePath" "%vmscfgdir%\ACPI-SSDT.bin",,hide
vboxmanage setextradata vm3 "VBoxInternal/Devices/vga/0/Config/BiosRom" "%vmscfgdir%\vgabios386.bin",,hide
vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/BiosRom" "%vmscfgdir%\pcbios386.bin",,hide

vboxmanage setextradata global GUI/SuppressMessages all,,hide
vboxmanage setextradata global GUI/ShowMiniToolBar false,,hide
vboxmanage setextradata vm3 GUI/DefaultCloseAction PowerOff,,hide
vboxmanage modifyvm vm3 --biosbootmenu menuonly,,hide

; none | floppy | dvd | disk | net
vboxmanage modifyvm vm3 --boot1 %boot1%,,hide
vboxmanage modifyvm vm3 --boot2 none,,hide
vboxmanage modifyvm vm3 --boot3 none,,hide
vboxmanage modifyvm vm3 --boot4 none,,hide

vboxmanage setextradata global GUI/Input/MachineShortcuts FullscreenMode=None,,hide
vboxmanage setextradata global GUI/Input/MachineShortcuts SeamlessMode=None,,hide
vboxmanage setextradata global GUI/Input/HostKeyCombination 123,,hide
vboxmanage setextradata global GUI/Input/AutoCapture false,,hide

