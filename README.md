# VirtualBox VM Configuration Script for Linux

This script has been converted from an AutoHotkey script to work on Linux systems. It configures a VirtualBox virtual machine with specific hardware settings, DMI information, and BIOS customizations.

## Files

- `install_vbox.sh` - Main bash script for VM configuration
- `vm_config.conf` - Configuration file with customizable variables
- `README.md` - This documentation file

## Prerequisites

1. **VirtualBox** must be installed on your Linux system
2. **VBoxManage** command-line tool must be available in your PATH
3. The script requires bash shell

### Installing VirtualBox on Linux

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install virtualbox
```

**CentOS/RHEL/Fedora:**
```bash
# For Fedora
sudo dnf install VirtualBox

# For CentOS/RHEL (enable EPEL repository first)
sudo yum install epel-release
sudo yum install VirtualBox
```

**Arch Linux:**
```bash
sudo pacman -S virtualbox
```

## Configuration

1. **Edit the configuration file** `vm_config.conf`:
   ```bash
   nano vm_config.conf
   ```

2. **Key settings to modify:**
   - `vmFolder`: Path to your VM folder
   - `vm`: Name of your OVA file (without .ova extension)
   - `VM`: Name for the imported VM
   - `vmscfgdir`: Path to custom BIOS/ACPI files
   - `bridge_adapter`: Your network interface name (find with `ip link show`)
   - `vm_memory`: RAM allocation in MB
   - `MAC`: MAC address for the VM

3. **Find your network interface:**
   ```bash
   ip link show
   # or
   ifconfig
   ```
   Common interface names: `eth0`, `enp0s3`, `wlan0`, `ens33`

## Usage

1. **Make the script executable:**
   ```bash
   chmod +x install_vbox.sh
   ```

2. **Run the script:**
   ```bash
   ./install_vbox.sh
   ```

## Key Differences from Windows Version

1. **Path separators**: Changed from `\` to `/`
2. **Variable syntax**: Changed from `%variable%` to `$variable` or `${variable}`
3. **Comments**: Changed from `;` to `#`
4. **Command execution**: Added error checking and logging
5. **Configuration**: Externalized variables to a config file

## Features

- **Hardware spoofing**: Configures DMI information to mimic ASUS hardware
- **Network setup**: Configures bridged networking
- **Custom BIOS**: Supports custom BIOS and ACPI files
- **Virtualization settings**: Enables various CPU virtualization features
- **GUI customization**: Configures VirtualBox GUI behavior
- **Error handling**: Includes basic error checking for VBoxManage commands

## Troubleshooting

1. **Permission issues**: Make sure your user is in the `vboxusers` group:
   ```bash
   sudo usermod -a -G vboxusers $USER
   # Log out and log back in
   ```

2. **Network interface not found**: Check available interfaces:
   ```bash
   VBoxManage list bridgedifs
   ```

3. **VBoxManage not found**: Ensure VirtualBox is properly installed and in PATH

4. **Custom BIOS files**: Make sure the paths in `vmscfgdir` are correct and files exist

## Security Note

This script configures the VM to spoof hardware information. Use this responsibly and in accordance with software licensing terms and local laws.

## License

This script is provided as-is for educational and legitimate virtualization purposes.
