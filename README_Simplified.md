# Simplified VirtualBox VM Scripts

These simplified scripts have all configuration built-in and automatically detect host system settings like network interfaces and MAC addresses. No separate configuration files needed!

## Files

- `install_vbox.sh` - Configure existing VM with hardware spoofing
- `create_vm_from_xml.sh` - Create VM matching XML configuration  
- `create_and_configure_vm.sh` - **NEW**: Create and configure VM in one step
- `README_Simplified.md` - This documentation

## Key Features

### ✅ **No Config Files Needed**
- All settings are in the script headers
- Just edit the variables at the top of each script

### ✅ **Dynamic Host Detection**
- **MAC Address**: Auto-generated with VirtualBox prefix (080027)
- **Network Interface**: Auto-detects your primary network adapter
- **Paths**: Uses sensible defaults with `$HOME` paths

### ✅ **Smart Fallbacks**
- If network detection fails, tries multiple methods
- Falls back to common interface names (eth0, etc.)
- Validates interfaces against VirtualBox's available adapters

## Quick Start

### Option 1: Create New VM (Recommended)
```bash
# Edit the configuration section in the script
nano create_and_configure_vm.sh

# Make executable and run
chmod +x create_and_configure_vm.sh
./create_and_configure_vm.sh
```

### Option 2: Configure Existing VM
```bash
# Edit the configuration section
nano install_vbox.sh

# Make executable and run
chmod +x install_vbox.sh
./install_vbox.sh
```

### Option 3: Create VM from XML Template
```bash
# Edit the configuration section
nano create_vm_from_xml.sh

# Make executable and run
chmod +x create_vm_from_xml.sh
./create_vm_from_xml.sh
```

## Configuration

Simply edit the **CONFIGURATION SECTION** at the top of each script:

```bash
# VM Basic Settings
VM_NAME="MyVM"                      # VM name
RAM_SIZE="4096"                     # Memory in MB
CPU_COUNT="2"                       # Number of CPU cores
VRAM_SIZE="256"                     # Video memory in MB

# Storage Settings
VDI_SIZE="20480"                    # VDI size in MB (20GB)
ISO_PATH="$HOME/Downloads/windows.iso"  # Path to ISO file
```

## What Gets Auto-Detected

### Network Interface
The script automatically finds your primary network interface:
1. Checks default route interface
2. Falls back to first active interface
3. Validates against VirtualBox bridged interfaces
4. Uses first available VirtualBox interface if needed

### MAC Address
Generates a random MAC with VirtualBox's official prefix:
- Format: `080027XXXXXX` (where X is random hex)
- Ensures no conflicts with existing VMs

### Paths
Uses logical default paths:
- VM Folder: `$HOME/VirtualBox VMs`
- VDI File: `$HOME/VirtualBox VMs/VMName/VMName.vdi`
- Config: `$HOME/vm-config/`

## Hardware Spoofing Features

All scripts include comprehensive DMI hardware spoofing:

- **BIOS**: ASUS MB52.88Z.0088.B05.0904162222
- **System**: ASUS Ultrabook (Asus5)
- **Board**: Aus-F22788AA v3.0
- **Chassis**: ASUS Inc. desktop
- **Storage**: Hitachi HDD + Slimtype DVD
- **ACPI**: ASUS OEM ID

## Example Output

```
Detecting host system configuration...
Generated MAC address: 080027A1B2C3
Detected network interface: enp0s3
Using network interface: enp0s3
VM configuration: MyVM (4096MB RAM, 256MB VRAM)

Creating VirtualBox VM: MyVM
========================================
Running: VBoxManage createvm --name MyVM --ostype Linux_64 --register
...
VM creation and configuration completed successfully!
```

## Troubleshooting

### Network Interface Issues
If the auto-detected interface doesn't work:
1. Check available interfaces: `VBoxManage list bridgedifs`
2. Edit the script and set `bridge_adapter="your_interface"` manually

### VM Already Exists
```bash
VBoxManage unregistervm "VMName" --delete
```

### Permission Issues
```bash
sudo usermod -a -G vboxusers $USER
# Log out and log back in
```

## Advantages Over Config File Approach

1. **Simpler**: Everything in one file
2. **Portable**: No external dependencies
3. **Self-contained**: Easy to share and modify
4. **Dynamic**: Adapts to different host systems
5. **Intelligent**: Auto-detects optimal settings

## Security Note

These scripts include hardware spoofing features. Use responsibly and in accordance with software licensing terms and applicable laws.
