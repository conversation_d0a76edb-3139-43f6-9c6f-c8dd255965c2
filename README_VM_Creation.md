# VirtualBox VM Creation from XML Configuration

This set of scripts creates a VirtualBox virtual machine that exactly matches the configuration from your provided XML file. The VM will have all the same settings, including DMI hardware spoofing, AHCI configuration, and custom BIOS settings.

## Files

- `create_vm_from_xml.sh` - Main script that creates the VM
- `vm_creation_config.conf` - Configuration file with customizable settings
- `validate_vm_creation.sh` - Validation script to check setup before creation
- `README_VM_Creation.md` - This documentation

## Quick Start

1. **Make scripts executable:**
   ```bash
   chmod +x create_vm_from_xml.sh validate_vm_creation.sh
   ```

2. **Edit configuration:**
   ```bash
   nano vm_creation_config.conf
   ```
   Update the paths for your system, especially:
   - `VDI_PATH` - Where to create/find the virtual hard disk
   - `ISO_PATH` - Path to your Windows ISO file
   - `BRIDGE_ADAPTER` - Your network interface name

3. **Validate setup:**
   ```bash
   ./validate_vm_creation.sh
   ```

4. **Create the VM:**
   ```bash
   ./create_vm_from_xml.sh
   ```

## Configuration Details

### Essential Settings to Update

**Storage Paths:**
```bash
VDI_PATH="/home/<USER>/VirtualBox VMs/vm3/windows.vdi"
ISO_PATH="/home/<USER>/Downloads/windows_10.iso"
```

**Network Adapter:**
```bash
# Find your adapter with:
VBoxManage list bridgedifs

# Common examples:
BRIDGE_ADAPTER="eth0"           # Linux wired
BRIDGE_ADAPTER="wlan0"          # Linux wireless  
BRIDGE_ADAPTER="enp0s3"         # Linux (newer naming)
```

### VM Specifications (from XML)

- **Name:** vm3
- **OS Type:** Linux_64
- **RAM:** 4096 MB (4 GB)
- **CPUs:** 2 cores
- **VRAM:** 16 MB
- **Graphics:** VBoxSVGA
- **Storage:** AHCI controller with HDD + DVD
- **Network:** Bridged adapter with specific MAC
- **Boot Order:** DVD first, then HDD

### Hardware Spoofing Features

The VM includes extensive DMI information spoofing to appear as ASUS hardware:

- **BIOS:** ASUS MB52.88Z.0088.B05.0904162222
- **System:** ASUS Ultrabook (Asus5)
- **Board:** Aus-F22788AA v3.0
- **Chassis:** ASUS Inc. desktop type
- **Storage:** Hitachi HDD + Slimtype DVD drive

## Advanced Features

### Custom BIOS Logo
Set `BIOS_LOGO_PATH` to display a custom splash screen during boot.

### Remote Desktop
The VM is configured with VRDE (VirtualBox Remote Desktop) on port 5389.

### USB Support
OHCI USB controller is enabled for USB device support.

### Virtualization Settings
- Hardware virtualization enabled
- Nested paging enabled
- Large pages support
- PAE and long mode enabled
- X2APIC support

## Troubleshooting

### Common Issues

1. **"VM already exists" error:**
   ```bash
   VBoxManage unregistervm "vm3" --delete
   ```

2. **Network adapter not found:**
   ```bash
   VBoxManage list bridgedifs
   # Update BRIDGE_ADAPTER in config file
   ```

3. **Permission denied on VDI creation:**
   ```bash
   # Ensure directory is writable
   mkdir -p "$(dirname "$VDI_PATH")"
   chmod 755 "$(dirname "$VDI_PATH")"
   ```

4. **ISO file not found:**
   - Verify the ISO path is correct
   - VM will still be created with empty DVD drive

### Validation Script

Always run the validation script first:
```bash
./validate_vm_creation.sh
```

This checks:
- VirtualBox installation
- Configuration file
- Storage paths
- Network adapters
- Existing VM conflicts

## Starting the VM

After creation, start the VM with:
```bash
VBoxManage startvm "vm3"
# or
VBoxManage startvm "vm3" --type headless  # for headless mode
```

Or use the VirtualBox GUI to manage the VM.

## Security Note

This VM configuration includes hardware spoofing features. Use responsibly and in accordance with software licensing terms and applicable laws.

## Customization

To modify the VM configuration:
1. Edit `vm_creation_config.conf` for basic settings
2. Edit `create_vm_from_xml.sh` for advanced VBoxManage commands
3. The script preserves all DMI and hardware spoofing settings from the original XML
