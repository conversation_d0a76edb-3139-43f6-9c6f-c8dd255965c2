#!/bin/bash

# VirtualBox VM Creation and Configuration Script
# Creates and configures a VM with dynamic host detection (no separate config files)

# =============================================================================
# CONFIGURATION SECTION - Edit these values as needed
# =============================================================================

# VM Basic Settings
VM_NAME="MyVM"                      # VM name (will also be used for OVA filename: MyVM.ova)
OS_TYPE="Linux_64"                  # OS type for the VM
RAM_SIZE="4096"                     # Memory in MB
CPU_COUNT="2"                       # Number of CPU cores
VRAM_SIZE="256"                     # Video memory in MB

# VM Hardware Settings
vm_mouse="usbtablet"                # Mouse type: ps2, usb, usbtablet
vm_vga="vmsvga"                     # Graphics controller: vboxvga, vmsvga, vboxsvga
vm_rdp="off"                        # Enable/disable RDP: on, off
rdp_port="3389"                     # RDP port number
boot1="disk"                        # Boot device: none, floppy, dvd, disk, net
enable_chipset_ich9="false"         # Set to "true" to enable ICH9 chipset

# Storage Settings
vmFolder="$HOME/VirtualBox VMs"     # VM folder path
VDI_SIZE="20480"                    # VDI size in MB (20GB)
ISO_PATH="$HOME/Downloads/windows.iso"  # Path to ISO file (optional)
vmscfgdir="$HOME/vm-config"         # Custom BIOS/ACPI files directory (optional)

# =============================================================================
# DYNAMIC HOST DETECTION
# =============================================================================

echo "=== VirtualBox VM Creation and Configuration ==="
echo "VM Name: $VM_NAME"
echo "Detecting host system configuration..."

# Generate random MAC address with VirtualBox prefix (080027)
MAC="080027$(openssl rand -hex 3 2>/dev/null | tr '[:lower:]' '[:upper:]' || printf "%06X" $((RANDOM * RANDOM % 16777216)))"
echo "Generated MAC address: $MAC"

# Auto-detect primary network interface
bridge_adapter=""
if command -v ip >/dev/null 2>&1; then
    # Try to find the default route interface
    bridge_adapter=$(ip route | grep '^default' | head -1 | sed 's/.*dev \([^ ]*\).*/\1/')
    if [ -z "$bridge_adapter" ]; then
        # Fallback: get first active interface (excluding loopback)
        bridge_adapter=$(ip link show | grep -E '^[0-9]+: [^l][^o]' | head -1 | sed 's/^[0-9]*: \([^:]*\):.*/\1/')
    fi
elif command -v ifconfig >/dev/null 2>&1; then
    # Fallback for systems without ip command
    bridge_adapter=$(route -n | grep '^0.0.0.0' | head -1 | awk '{print $8}')
    if [ -z "$bridge_adapter" ]; then
        bridge_adapter=$(ifconfig | grep -E '^[a-z]' | grep -v '^lo' | head -1 | cut -d: -f1)
    fi
fi

# Default fallback if detection fails
if [ -z "$bridge_adapter" ]; then
    bridge_adapter="eth0"
    echo "Warning: Could not detect network interface, using default: $bridge_adapter"
else
    echo "Detected network interface: $bridge_adapter"
fi

# Verify the detected interface exists in VirtualBox
if command -v VBoxManage >/dev/null 2>&1; then
    if ! VBoxManage list bridgedifs | grep -q "Name:.*$bridge_adapter" 2>/dev/null; then
        echo "Warning: Interface '$bridge_adapter' not found in VirtualBox bridged interfaces"
        # Try to find a working interface
        alt_adapter=$(VBoxManage list bridgedifs 2>/dev/null | grep "^Name:" | head -1 | sed 's/Name: *//')
        if [ -n "$alt_adapter" ]; then
            bridge_adapter="$alt_adapter"
            echo "Using first available interface: $bridge_adapter"
        fi
    fi
else
    echo "Warning: VBoxManage not found. Please install VirtualBox."
    exit 1
fi

# Set dynamic paths
VDI_PATH="$vmFolder/$VM_NAME/${VM_NAME}.vdi"

echo "Final configuration:"
echo "  VM Name: $VM_NAME"
echo "  RAM: ${RAM_SIZE}MB, CPUs: $CPU_COUNT, VRAM: ${VRAM_SIZE}MB"
echo "  Network: $bridge_adapter (MAC: $MAC)"
echo "  VDI: $VDI_PATH"
echo "  ISO: $ISO_PATH"
echo

# =============================================================================
# HELPER FUNCTION
# =============================================================================

# Function to run VBoxManage commands with error checking
run_vboxmanage() {
    echo "Running: VBoxManage $*"
    VBoxManage "$@"
    if [ $? -ne 0 ]; then
        echo "Error: Command failed: VBoxManage $*"
        exit 1
    fi
}

# =============================================================================
# VM CREATION
# =============================================================================

echo "Creating VirtualBox VM: $VM_NAME"
echo "========================================"

# Check if VM already exists
if VBoxManage list vms | grep -q "\"$VM_NAME\""; then
    echo "Error: VM '$VM_NAME' already exists!"
    echo "Remove it first with: VBoxManage unregistervm '$VM_NAME' --delete"
    exit 1
fi

# Create the VM
run_vboxmanage createvm --name "$VM_NAME" --ostype "$OS_TYPE" --register

# Basic VM settings
run_vboxmanage modifyvm "$VM_NAME" --memory "$RAM_SIZE"
run_vboxmanage modifyvm "$VM_NAME" --cpus "$CPU_COUNT"
run_vboxmanage modifyvm "$VM_NAME" --vram "$VRAM_SIZE"

# CPU settings
run_vboxmanage modifyvm "$VM_NAME" --pae on
run_vboxmanage modifyvm "$VM_NAME" --longmode on
run_vboxmanage modifyvm "$VM_NAME" --x2apic on
run_vboxmanage modifyvm "$VM_NAME" --largepages on

# Hardware virtualization
run_vboxmanage modifyvm "$VM_NAME" --hwvirtex on
run_vboxmanage modifyvm "$VM_NAME" --nestedpaging on
run_vboxmanage modifyvm "$VM_NAME" --vtxvpid on
run_vboxmanage modifyvm "$VM_NAME" --vtxux on

# Other hardware settings
run_vboxmanage modifyvm "$VM_NAME" --hpet on
run_vboxmanage modifyvm "$VM_NAME" --paravirtprovider legacy
run_vboxmanage modifyvm "$VM_NAME" --apic on

# Graphics and peripherals
run_vboxmanage modifyvm "$VM_NAME" --graphicscontroller "$vm_vga"
run_vboxmanage modifyvm "$VM_NAME" --mouse "$vm_mouse"

# Boot order
run_vboxmanage modifyvm "$VM_NAME" --boot1 "$boot1"
run_vboxmanage modifyvm "$VM_NAME" --boot2 none
run_vboxmanage modifyvm "$VM_NAME" --boot3 none
run_vboxmanage modifyvm "$VM_NAME" --boot4 none

# BIOS settings
run_vboxmanage modifyvm "$VM_NAME" --ioapic on
run_vboxmanage modifyvm "$VM_NAME" --biosbootmenu menuonly

# Network settings
run_vboxmanage modifyvm "$VM_NAME" --nic1 bridged
run_vboxmanage modifyvm "$VM_NAME" --bridgeadapter1 "$bridge_adapter"
run_vboxmanage modifyvm "$VM_NAME" --macaddress1 "$MAC"

# RDP settings
run_vboxmanage modifyvm "$VM_NAME" --vrde "$vm_rdp"
if [ "$vm_rdp" = "on" ]; then
    run_vboxmanage modifyvm "$VM_NAME" --vrdeport "$rdp_port"
fi

# Optional ICH9 chipset
if [ "$enable_chipset_ich9" = "true" ]; then
    run_vboxmanage modifyvm "$VM_NAME" --chipset ich9
fi

# Create SATA storage controller
run_vboxmanage storagectl "$VM_NAME" --name "SATA" --add sata --controller IntelAhci --portcount 2 --hostiocache on --bootable on

# Create and attach hard disk
echo "Creating VDI file: $VDI_PATH (${VDI_SIZE}MB)"
mkdir -p "$(dirname "$VDI_PATH")"
run_vboxmanage createhd --filename "$VDI_PATH" --size "$VDI_SIZE" --format VDI
run_vboxmanage storageattach "$VM_NAME" --storagectl "SATA" --port 0 --device 0 --type hdd --medium "$VDI_PATH"

# Attach ISO if it exists
if [ -f "$ISO_PATH" ]; then
    echo "Attaching ISO: $ISO_PATH"
    run_vboxmanage storageattach "$VM_NAME" --storagectl "SATA" --port 1 --device 0 --type dvddrive --medium "$ISO_PATH"
else
    echo "ISO not found, creating empty DVD drive"
    run_vboxmanage storageattach "$VM_NAME" --storagectl "SATA" --port 1 --device 0 --type dvddrive --medium emptydrive
fi

echo ""
echo "Basic VM creation completed. Now applying advanced configuration..."
echo "=================================================================="

# =============================================================================
# ADVANCED CONFIGURATION (DMI Hardware Spoofing)
# =============================================================================

# GUI settings
run_vboxmanage setextradata "$VM_NAME" GUI/DefaultCloseAction PowerOff

# CPU and virtualization extra settings
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/CPUM/EnableHVP" 0
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/TM/TSCMode" RealTSCOffset

# ACPI settings
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/acpi/0/Config/AcpiOemId" "ASUS"

# DMI BIOS settings
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVendor" "Asus"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVersion" "MB52.88Z.0088.B05.0904162222"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseDate" "08/10/13"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMajor" "5"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMinor" "9"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMajor" "1"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMinor" "0"

# DMI System settings
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemVendor" "Asus"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemProduct" "Asus5"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemVersion" "1.0"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemSerial" "CSN12345678901234567"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemSKU" "FM550EA#ACB"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemFamily" "Ultrabook"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemUuid" "B5FA3000-9403-81E0-3ADA-F46D045CB676"

# DMI Board settings
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardVendor" "Asus"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardProduct" "Aus-F22788AA"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardVersion" "3.0"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardSerial" "BSN12345678901234567"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardAssetTag" "Base Board Asset Tag#"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardLocInChass" "Board Loc In"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardBoardType" 10

# DMI Chassis settings
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisVendor" "Asus Inc."
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisType" 10
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisVersion" "Asus-F22788AA"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisSerial" "CSN12345678901234567"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisAssetTag" "WhiteHouse"

# DMI OEM settings
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxVer" "Extended version info: 1.00.00"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxRev" "Extended revision info: 1A"

# AHCI Port 0 settings (Hard Disk)
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port0/ModelNumber" "Hitachi HTS543230AAA384"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port0/FirmwareRevision" "ES2OA60W"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port0/SerialNumber" "2E3024L1T2V9KA"

# AHCI Port 1 settings (DVD Drive)
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port1/ModelNumber" "Slimtype DVD A  DS8A8SH"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port1/FirmwareRevision" "KAA2"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port1/SerialNumber" "ABCDEF0123456789"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIVendorId" "Slimtype"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIProductId" "DVD A  DS8A8SH"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIRevision" "KAA2"

# Custom BIOS files (if they exist)
if [ -f "$vmscfgdir/ACPI-DSDT.bin" ]; then
    run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/acpi/0/Config/DsdtFilePath" "$vmscfgdir/ACPI-DSDT.bin"
fi
if [ -f "$vmscfgdir/ACPI-SSDT.bin" ]; then
    run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/acpi/0/Config/SsdtFilePath" "$vmscfgdir/ACPI-SSDT.bin"
fi
if [ -f "$vmscfgdir/vgabios386.bin" ]; then
    run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/vga/0/Config/BiosRom" "$vmscfgdir/vgabios386.bin"
fi
if [ -f "$vmscfgdir/pcbios386.bin" ]; then
    run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/BiosRom" "$vmscfgdir/pcbios386.bin"
fi
if [ -f "$vmscfgdir/vm_splash.bmp" ]; then
    run_vboxmanage modifyvm "$VM_NAME" --bioslogoimagepath "$vmscfgdir/vm_splash.bmp"
fi

echo ""
echo "VM creation and configuration completed successfully!"
echo "===================================================="
echo "VM Name: $VM_NAME"
echo "RAM: ${RAM_SIZE}MB, CPUs: $CPU_COUNT, VRAM: ${VRAM_SIZE}MB"
echo "MAC Address: $MAC"
echo "Network: $bridge_adapter"
echo "Hard Disk: $VDI_PATH (${VDI_SIZE}MB)"
echo "DVD: $ISO_PATH"
echo ""
echo "You can now start the VM with:"
echo "  VBoxManage startvm \"$VM_NAME\""
echo "  VBoxManage startvm \"$VM_NAME\" --type headless"
echo ""
echo "Or use the VirtualBox GUI to manage the VM."
