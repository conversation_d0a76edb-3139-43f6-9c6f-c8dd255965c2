#!/bin/bash

# VirtualBox VM Creation Script
# Creates a VM based on the provided XML configuration with dynamic host detection

# =============================================================================
# CONFIGURATION SECTION - Edit these values as needed
# =============================================================================

# VM Basic Settings
VM_NAME="vm3"                       # VM name
VM_UUID="8581b929-4ccb-4a9f-bba4-1a9a3f2ff77d"  # VM UUID from XML
OS_TYPE="Linux_64"                  # OS type
RAM_SIZE="4096"                     # Memory in MB
CPU_COUNT="2"                       # Number of CPU cores
VRAM_SIZE="16"                      # Video memory in MB
VDI_SIZE="20480"                    # VDI size in MB (20GB)
VRDE_PORT="5389"                    # Remote desktop port

# Storage paths - Update these for your system
VDI_PATH="$HOME/VirtualBox VMs/$VM_NAME/$VM_NAME.vdi"
ISO_PATH="$HOME/Downloads/windows_10_iot_enterprise_ltsc_2021_x64.iso"
BIOS_LOGO_PATH="$HOME/vm-config/vm_splash.bmp"

# =============================================================================
# DYNAMIC HOST DETECTION
# =============================================================================

echo "Detecting host system configuration..."

# Generate random MAC address with VirtualBox prefix (080027)
MAC_ADDRESS="080027$(openssl rand -hex 3 2>/dev/null | tr '[:lower:]' '[:upper:]' || printf "%06X" $((RANDOM * RANDOM % 16777216)))"
echo "Generated MAC address: $MAC_ADDRESS"

# Auto-detect primary network interface
BRIDGE_ADAPTER=""
if command -v ip >/dev/null 2>&1; then
    # Try to find the default route interface
    BRIDGE_ADAPTER=$(ip route | grep '^default' | head -1 | sed 's/.*dev \([^ ]*\).*/\1/')
    if [ -z "$BRIDGE_ADAPTER" ]; then
        # Fallback: get first active interface (excluding loopback)
        BRIDGE_ADAPTER=$(ip link show | grep -E '^[0-9]+: [^l][^o]' | head -1 | sed 's/^[0-9]*: \([^:]*\):.*/\1/')
    fi
elif command -v ifconfig >/dev/null 2>&1; then
    # Fallback for systems without ip command
    BRIDGE_ADAPTER=$(route -n | grep '^0.0.0.0' | head -1 | awk '{print $8}')
    if [ -z "$BRIDGE_ADAPTER" ]; then
        BRIDGE_ADAPTER=$(ifconfig | grep -E '^[a-z]' | grep -v '^lo' | head -1 | cut -d: -f1)
    fi
fi

# Default fallback if detection fails
if [ -z "$BRIDGE_ADAPTER" ]; then
    BRIDGE_ADAPTER="eth0"
    echo "Warning: Could not detect network interface, using default: $BRIDGE_ADAPTER"
else
    echo "Detected network interface: $BRIDGE_ADAPTER"
fi

# Verify the detected interface exists in VirtualBox
if command -v VBoxManage >/dev/null 2>&1; then
    if ! VBoxManage list bridgedifs | grep -q "Name:.*$BRIDGE_ADAPTER"; then
        echo "Warning: Interface '$BRIDGE_ADAPTER' not found in VirtualBox bridged interfaces"
        echo "Available interfaces:"
        VBoxManage list bridgedifs | grep "^Name:" | head -3
        # Try to find a working interface
        alt_adapter=$(VBoxManage list bridgedifs | grep "^Name:" | head -1 | sed 's/Name: *//')
        if [ -n "$alt_adapter" ]; then
            BRIDGE_ADAPTER="$alt_adapter"
            echo "Using first available interface: $BRIDGE_ADAPTER"
        fi
    fi
fi

echo "Using network interface: $BRIDGE_ADAPTER"
echo "VM configuration: $VM_NAME (${RAM_SIZE}MB RAM, ${VRAM_SIZE}MB VRAM)"
echo

# Function to run VBoxManage commands with error checking
run_vboxmanage() {
    echo "Running: VBoxManage $*"
    VBoxManage "$@"
    if [ $? -ne 0 ]; then
        echo "Error: Command failed: VBoxManage $*"
        exit 1
    fi
}

echo "Creating VirtualBox VM: $VM_NAME"
echo "========================================"

# Create the VM
run_vboxmanage createvm --name "$VM_NAME" --ostype "$OS_TYPE" --register --uuid "$VM_UUID"

# Basic VM settings
run_vboxmanage modifyvm "$VM_NAME" --memory "$RAM_SIZE"
run_vboxmanage modifyvm "$VM_NAME" --cpus "$CPU_COUNT"
run_vboxmanage modifyvm "$VM_NAME" --vram "$VRAM_SIZE"

# CPU settings
run_vboxmanage modifyvm "$VM_NAME" --pae on
run_vboxmanage modifyvm "$VM_NAME" --longmode on
run_vboxmanage modifyvm "$VM_NAME" --x2apic on
run_vboxmanage modifyvm "$VM_NAME" --largepages on

# Hardware virtualization
run_vboxmanage modifyvm "$VM_NAME" --hwvirtex on
run_vboxmanage modifyvm "$VM_NAME" --nestedpaging on
run_vboxmanage modifyvm "$VM_NAME" --vtxvpid on
run_vboxmanage modifyvm "$VM_NAME" --vtxux on

# Other hardware settings
run_vboxmanage modifyvm "$VM_NAME" --hpet on
run_vboxmanage modifyvm "$VM_NAME" --paravirtprovider legacy
run_vboxmanage modifyvm "$VM_NAME" --apic on

# Graphics controller
run_vboxmanage modifyvm "$VM_NAME" --graphicscontroller vboxsvga

# Boot order
run_vboxmanage modifyvm "$VM_NAME" --boot1 dvd
run_vboxmanage modifyvm "$VM_NAME" --boot2 disk
run_vboxmanage modifyvm "$VM_NAME" --boot3 none
run_vboxmanage modifyvm "$VM_NAME" --boot4 none

# BIOS settings
run_vboxmanage modifyvm "$VM_NAME" --ioapic on
run_vboxmanage modifyvm "$VM_NAME" --biosbootmenu menuonly
run_vboxmanage modifyvm "$VM_NAME" --bioslogofadein on
run_vboxmanage modifyvm "$VM_NAME" --bioslogofadeout on
run_vboxmanage modifyvm "$VM_NAME" --bioslogodisplaytime 0
if [ -f "$BIOS_LOGO_PATH" ]; then
    run_vboxmanage modifyvm "$VM_NAME" --bioslogoimagepath "$BIOS_LOGO_PATH"
fi

# Network settings
run_vboxmanage modifyvm "$VM_NAME" --nic1 bridged
run_vboxmanage modifyvm "$VM_NAME" --bridgeadapter1 "$BRIDGE_ADAPTER"
run_vboxmanage modifyvm "$VM_NAME" --macaddress1 "$MAC_ADDRESS"
run_vboxmanage modifyvm "$VM_NAME" --nictype1 82540EM

# USB settings
run_vboxmanage modifyvm "$VM_NAME" --usb on
run_vboxmanage modifyvm "$VM_NAME" --usbohci on

# Audio settings
run_vboxmanage modifyvm "$VM_NAME" --audio dsound
run_vboxmanage modifyvm "$VM_NAME" --audioin off

# RTC settings
run_vboxmanage modifyvm "$VM_NAME" --rtcuseutc on

# Remote display settings
run_vboxmanage modifyvm "$VM_NAME" --vrde on
run_vboxmanage modifyvm "$VM_NAME" --vrdeport "$VRDE_PORT"

# Create AHCI storage controller
run_vboxmanage storagectl "$VM_NAME" --name "IDE" --add sata --controller IntelAhci --portcount 3 --hostiocache on --bootable on

# Attach hard disk (create if it doesn't exist)
if [ ! -f "$VDI_PATH" ]; then
    echo "Creating VDI file: $VDI_PATH (${VDI_SIZE}MB)"
    # Create directory if it doesn't exist
    VDI_DIR=$(dirname "$VDI_PATH")
    mkdir -p "$VDI_DIR"
    run_vboxmanage createhd --filename "$VDI_PATH" --size "$VDI_SIZE" --format VDI
fi
run_vboxmanage storageattach "$VM_NAME" --storagectl "IDE" --port 0 --device 0 --type hdd --medium "$VDI_PATH"

# Attach DVD (if ISO exists)
if [ -f "$ISO_PATH" ]; then
    run_vboxmanage storageattach "$VM_NAME" --storagectl "IDE" --port 1 --device 0 --type dvddrive --medium "$ISO_PATH"
else
    echo "Warning: ISO file not found: $ISO_PATH"
    echo "Creating empty DVD drive"
    run_vboxmanage storageattach "$VM_NAME" --storagectl "IDE" --port 1 --device 0 --type dvddrive --medium emptydrive
fi

echo ""
echo "Basic VM creation completed. Now applying advanced settings..."
echo "=============================================================="

# GUI settings
run_vboxmanage setextradata "$VM_NAME" GUI/DefaultCloseAction PowerOff

# CPU and virtualization extra settings
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/CPUM/EnableHVP" 0
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/TM/TSCMode" RealTSCOffset

# ACPI settings
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/acpi/0/Config/AcpiOemId" "ASUS"

# DMI BIOS settings
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVendor" "Asus"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVersion" "MB52.88Z.0088.B05.0904162222"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseDate" "08/10/13"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMajor" "5"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMinor" "9"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMajor" "1"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMinor" "0"

# DMI System settings
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemVendor" "Asus"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemProduct" "Asus5"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemVersion" "1.0"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemSerial" "CSN12345678901234567"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemSKU" "FM550EA#ACB"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemFamily" "Ultrabook"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemUuid" "B5FA3000-9403-81E0-3ADA-F46D045CB676"

# DMI Board settings
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardVendor" "Asus"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardProduct" "Aus-F22788AA"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardVersion" "3.0"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardSerial" "BSN12345678901234567"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardAssetTag" "Base Board Asset Tag#"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardLocInChass" "Board Loc In"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardBoardType" 10

# DMI Chassis settings
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisVendor" "Asus Inc."
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisType" 10
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisVersion" "Asus-F22788AA"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisSerial" "CSN12345678901234567"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisAssetTag" "WhiteHouse"

# DMI OEM settings
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxVer" "Extended version info: 1.00.00"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxRev" "Extended revision info: 1A"

# AHCI Port 0 settings (Hard Disk)
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port0/ModelNumber" "Hitachi HTS543230AAA384"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port0/FirmwareRevision" "ES2OA60W"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port0/SerialNumber" "2E3024L1T2V9KA"

# AHCI Port 1 settings (DVD Drive)
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port1/ModelNumber" "Slimtype DVD A  DS8A8SH"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port1/FirmwareRevision" "KAA2"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port1/SerialNumber" "ABCDEF0123456789"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIVendorId" "Slimtype"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIProductId" "DVD A  DS8A8SH"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIRevision" "KAA2"

# PIIX3IDE Primary Master settings (Hard Disk)
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/piix3ide/0/Config/PrimaryMaster/ModelNumber" "Hitachi HTS543232A8A384"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/piix3ide/0/Config/PrimaryMaster/FirmwareRevision" "ES2OA60W"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/piix3ide/0/Config/PrimaryMaster/SerialNumber" "2E3024L1T2V9KA"

# PIIX3IDE Secondary Master settings (DVD Drive)
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/piix3ide/0/Config/SecondaryMaster/ModelNumber" "Slimtype DVD A  DS8A8SH"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/piix3ide/0/Config/SecondaryMaster/FirmwareRevision" "KAA2"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/piix3ide/0/Config/SecondaryMaster/SerialNumber" "ABCDEF0123456789"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/piix3ide/0/Config/SecondaryMaster/ATAPIVendorId" "Slimtype"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/piix3ide/0/Config/SecondaryMaster/ATAPIProductId" "DVD A  DS8A8SH"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/piix3ide/0/Config/SecondaryMaster/ATAPIRevision" "KAA2"

echo ""
echo "VM creation completed successfully!"
echo "=================================="
echo "VM Name: $VM_NAME"
echo "UUID: $VM_UUID"
echo "OS Type: $OS_TYPE"
echo "RAM: ${RAM_SIZE}MB"
echo "CPUs: $CPU_COUNT"
echo "VRAM: ${VRAM_SIZE}MB"
echo "MAC Address: $MAC_ADDRESS"
echo "Hard Disk: $VDI_PATH"
echo "DVD Image: $ISO_PATH"
echo "Bridge Adapter: $BRIDGE_ADAPTER"
echo ""
echo "You can now start the VM with:"
echo "VBoxManage startvm \"$VM_NAME\""
echo ""
echo "Or use the VirtualBox GUI to manage the VM."
