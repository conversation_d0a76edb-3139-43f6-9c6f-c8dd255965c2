#!/bin/bash

# VirtualBox VM Creation Script
# Creates a VM based on the provided XML configuration

# Check if configuration file exists and source it
CONFIG_FILE="vm_creation_config.conf"
if [ -f "$CONFIG_FILE" ]; then
    echo "Loading configuration from $CONFIG_FILE"
    source "$CONFIG_FILE"
else
    echo "Configuration file $CONFIG_FILE not found, using default values"
fi

# Default configuration variables (will be overridden by config file if it exists)
VM_NAME="${VM_NAME:-vm3}"
VM_UUID="${VM_UUID:-8581b929-4ccb-4a9f-bba4-1a9a3f2ff77d}"
OS_TYPE="${OS_TYPE:-Linux_64}"
RAM_SIZE="${RAM_SIZE:-4096}"
CPU_COUNT="${CPU_COUNT:-2}"
VRAM_SIZE="${VRAM_SIZE:-16}"
MAC_ADDRESS="${MAC_ADDRESS:-E0D55E8749A3}"
VDI_SIZE="${VDI_SIZE:-20480}"
VRDE_PORT="${VRDE_PORT:-5389}"

# Storage paths (IMPORTANT: Update these in the config file)
VDI_PATH="${VDI_PATH:-/path/to/your/windows.vdi}"
ISO_PATH="${ISO_PATH:-/path/to/your/windows_iso_file.iso}"
BIOS_LOGO_PATH="${BIOS_LOGO_PATH:-/path/to/your/vm_splash.bmp}"

# Network adapter (update in config file to match your system)
BRIDGE_ADAPTER="${BRIDGE_ADAPTER:-eth0}"

# Function to run VBoxManage commands with error checking
run_vboxmanage() {
    echo "Running: VBoxManage $*"
    VBoxManage "$@"
    if [ $? -ne 0 ]; then
        echo "Error: Command failed: VBoxManage $*"
        exit 1
    fi
}

echo "Creating VirtualBox VM: $VM_NAME"
echo "========================================"

# Create the VM
run_vboxmanage createvm --name "$VM_NAME" --ostype "$OS_TYPE" --register --uuid "$VM_UUID"

# Basic VM settings
run_vboxmanage modifyvm "$VM_NAME" --memory "$RAM_SIZE"
run_vboxmanage modifyvm "$VM_NAME" --cpus "$CPU_COUNT"
run_vboxmanage modifyvm "$VM_NAME" --vram "$VRAM_SIZE"

# CPU settings
run_vboxmanage modifyvm "$VM_NAME" --pae on
run_vboxmanage modifyvm "$VM_NAME" --longmode on
run_vboxmanage modifyvm "$VM_NAME" --x2apic on
run_vboxmanage modifyvm "$VM_NAME" --largepages on

# Hardware virtualization
run_vboxmanage modifyvm "$VM_NAME" --hwvirtex on
run_vboxmanage modifyvm "$VM_NAME" --nestedpaging on
run_vboxmanage modifyvm "$VM_NAME" --vtxvpid on
run_vboxmanage modifyvm "$VM_NAME" --vtxux on

# Other hardware settings
run_vboxmanage modifyvm "$VM_NAME" --hpet on
run_vboxmanage modifyvm "$VM_NAME" --paravirtprovider legacy
run_vboxmanage modifyvm "$VM_NAME" --apic on

# Graphics controller
run_vboxmanage modifyvm "$VM_NAME" --graphicscontroller vboxsvga

# Boot order
run_vboxmanage modifyvm "$VM_NAME" --boot1 dvd
run_vboxmanage modifyvm "$VM_NAME" --boot2 disk
run_vboxmanage modifyvm "$VM_NAME" --boot3 none
run_vboxmanage modifyvm "$VM_NAME" --boot4 none

# BIOS settings
run_vboxmanage modifyvm "$VM_NAME" --ioapic on
run_vboxmanage modifyvm "$VM_NAME" --biosbootmenu menuonly
run_vboxmanage modifyvm "$VM_NAME" --bioslogofadein on
run_vboxmanage modifyvm "$VM_NAME" --bioslogofadeout on
run_vboxmanage modifyvm "$VM_NAME" --bioslogodisplaytime 0
if [ -f "$BIOS_LOGO_PATH" ]; then
    run_vboxmanage modifyvm "$VM_NAME" --bioslogoimagepath "$BIOS_LOGO_PATH"
fi

# Network settings
run_vboxmanage modifyvm "$VM_NAME" --nic1 bridged
run_vboxmanage modifyvm "$VM_NAME" --bridgeadapter1 "$BRIDGE_ADAPTER"
run_vboxmanage modifyvm "$VM_NAME" --macaddress1 "$MAC_ADDRESS"
run_vboxmanage modifyvm "$VM_NAME" --nictype1 82540EM

# USB settings
run_vboxmanage modifyvm "$VM_NAME" --usb on
run_vboxmanage modifyvm "$VM_NAME" --usbohci on

# Audio settings
run_vboxmanage modifyvm "$VM_NAME" --audio dsound
run_vboxmanage modifyvm "$VM_NAME" --audioin off

# RTC settings
run_vboxmanage modifyvm "$VM_NAME" --rtcuseutc on

# Remote display settings
run_vboxmanage modifyvm "$VM_NAME" --vrde on
run_vboxmanage modifyvm "$VM_NAME" --vrdeport "$VRDE_PORT"

# Create AHCI storage controller
run_vboxmanage storagectl "$VM_NAME" --name "IDE" --add sata --controller IntelAhci --portcount 3 --hostiocache on --bootable on

# Attach hard disk (create if it doesn't exist)
if [ ! -f "$VDI_PATH" ]; then
    echo "Creating VDI file: $VDI_PATH (${VDI_SIZE}MB)"
    # Create directory if it doesn't exist
    VDI_DIR=$(dirname "$VDI_PATH")
    mkdir -p "$VDI_DIR"
    run_vboxmanage createhd --filename "$VDI_PATH" --size "$VDI_SIZE" --format VDI
fi
run_vboxmanage storageattach "$VM_NAME" --storagectl "IDE" --port 0 --device 0 --type hdd --medium "$VDI_PATH"

# Attach DVD (if ISO exists)
if [ -f "$ISO_PATH" ]; then
    run_vboxmanage storageattach "$VM_NAME" --storagectl "IDE" --port 1 --device 0 --type dvddrive --medium "$ISO_PATH"
else
    echo "Warning: ISO file not found: $ISO_PATH"
    echo "Creating empty DVD drive"
    run_vboxmanage storageattach "$VM_NAME" --storagectl "IDE" --port 1 --device 0 --type dvddrive --medium emptydrive
fi

echo ""
echo "Basic VM creation completed. Now applying advanced settings..."
echo "=============================================================="

# GUI settings
run_vboxmanage setextradata "$VM_NAME" GUI/DefaultCloseAction PowerOff

# CPU and virtualization extra settings
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/CPUM/EnableHVP" 0
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/TM/TSCMode" RealTSCOffset

# ACPI settings
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/acpi/0/Config/AcpiOemId" "ASUS"

# DMI BIOS settings
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVendor" "Asus"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVersion" "MB52.88Z.0088.B05.0904162222"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseDate" "08/10/13"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMajor" "5"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMinor" "9"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMajor" "1"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMinor" "0"

# DMI System settings
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemVendor" "Asus"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemProduct" "Asus5"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemVersion" "1.0"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemSerial" "CSN12345678901234567"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemSKU" "FM550EA#ACB"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemFamily" "Ultrabook"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemUuid" "B5FA3000-9403-81E0-3ADA-F46D045CB676"

# DMI Board settings
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardVendor" "Asus"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardProduct" "Aus-F22788AA"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardVersion" "3.0"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardSerial" "BSN12345678901234567"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardAssetTag" "Base Board Asset Tag#"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardLocInChass" "Board Loc In"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardBoardType" 10

# DMI Chassis settings
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisVendor" "Asus Inc."
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisType" 10
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisVersion" "Asus-F22788AA"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisSerial" "CSN12345678901234567"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisAssetTag" "WhiteHouse"

# DMI OEM settings
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxVer" "Extended version info: 1.00.00"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxRev" "Extended revision info: 1A"

# AHCI Port 0 settings (Hard Disk)
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port0/ModelNumber" "Hitachi HTS543230AAA384"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port0/FirmwareRevision" "ES2OA60W"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port0/SerialNumber" "2E3024L1T2V9KA"

# AHCI Port 1 settings (DVD Drive)
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port1/ModelNumber" "Slimtype DVD A  DS8A8SH"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port1/FirmwareRevision" "KAA2"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port1/SerialNumber" "ABCDEF0123456789"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIVendorId" "Slimtype"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIProductId" "DVD A  DS8A8SH"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIRevision" "KAA2"

# PIIX3IDE Primary Master settings (Hard Disk)
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/piix3ide/0/Config/PrimaryMaster/ModelNumber" "Hitachi HTS543232A8A384"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/piix3ide/0/Config/PrimaryMaster/FirmwareRevision" "ES2OA60W"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/piix3ide/0/Config/PrimaryMaster/SerialNumber" "2E3024L1T2V9KA"

# PIIX3IDE Secondary Master settings (DVD Drive)
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/piix3ide/0/Config/SecondaryMaster/ModelNumber" "Slimtype DVD A  DS8A8SH"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/piix3ide/0/Config/SecondaryMaster/FirmwareRevision" "KAA2"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/piix3ide/0/Config/SecondaryMaster/SerialNumber" "ABCDEF0123456789"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/piix3ide/0/Config/SecondaryMaster/ATAPIVendorId" "Slimtype"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/piix3ide/0/Config/SecondaryMaster/ATAPIProductId" "DVD A  DS8A8SH"
run_vboxmanage setextradata "$VM_NAME" "VBoxInternal/Devices/piix3ide/0/Config/SecondaryMaster/ATAPIRevision" "KAA2"

echo ""
echo "VM creation completed successfully!"
echo "=================================="
echo "VM Name: $VM_NAME"
echo "UUID: $VM_UUID"
echo "OS Type: $OS_TYPE"
echo "RAM: ${RAM_SIZE}MB"
echo "CPUs: $CPU_COUNT"
echo "VRAM: ${VRAM_SIZE}MB"
echo "MAC Address: $MAC_ADDRESS"
echo "Hard Disk: $VDI_PATH"
echo "DVD Image: $ISO_PATH"
echo "Bridge Adapter: $BRIDGE_ADAPTER"
echo ""
echo "You can now start the VM with:"
echo "VBoxManage startvm \"$VM_NAME\""
echo ""
echo "Or use the VirtualBox GUI to manage the VM."
