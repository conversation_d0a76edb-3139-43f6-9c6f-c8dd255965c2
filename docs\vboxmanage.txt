VBoxManage
Introduction
VBoxManage is the CLI to Oracle VirtualBox. With it, you can control Oracle VirtualBox from the command line of the host operating system. VBoxManage supports all the features that the graphical user interface gives you access to, plus the features of the virtualization engine that can't be accessed from the GUI.

You need to use the command line to do the following:

Use a different user interface than the main GUI such as the VBoxHeadless server.

Control some more advanced and experimental configuration settings for a VM.

For more information, refer to the VBoxManage command Description and Examples.

Commands Overview
When running VBoxManage without parameters or when supplying an invalid command line, the following command syntax list is shown. Note that the output will be slightly different depending on the host platform. If in doubt, check the output of VBoxManage for the commands available on your particular host.

VBoxManage [‑q | ‑‑nologo] [‑‑settingspw=password] [‑‑settingspwfile=pw‑file] [@response‑file] [subcommand]
VBoxManage help [subcommand]
VBoxManage commands
VBoxManage [‑V | ‑‑version]
VBoxManage [‑‑dump‑build‑type]
VBoxManage adoptstate <uuid | vmname> <state‑filename>
VBoxManage bandwidthctl <uuid | vmname>add <bandwidth‑group‑name> <‑‑limit=bandwidth‑limit[k|m|g|K|M|G]> <‑‑type=disk | network>
VBoxManage bandwidthctl <uuid | vmname>list [‑‑machinereadable]
VBoxManage bandwidthctl <uuid | vmname>remove <bandwidth‑group‑name>
VBoxManage bandwidthctl <uuid | vmname>set <bandwidth‑group‑name> <‑‑limit=bandwidth‑limit[k|m|g|K|M|G]>
VBoxManage checkmediumpwd <uuid | filename> <password‑file>
VBoxManage clonemedium <uuid | source‑medium> <uuid | target‑medium> [disk | dvd | floppy] [‑‑existing] [‑‑format=VDI | VMDK | VHD | RAW | other] [‑‑variant=Standard|Fixed|Split2G|Stream|ESX...]
VBoxManage clonevm <vmname|uuid> [‑‑basefolder=basefolder] [‑‑groups=group,...] [‑‑mode=machine | ‑‑mode=machinechildren | ‑‑mode=all] [‑‑name=name] [‑‑options=option,...] [‑‑register] [‑‑snapshot=snapshot‑name] [‑‑uuid=uuid]
VBoxManage closemedium [disk | dvd | floppy] <uuid | filename> [‑‑delete]
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> list instances [‑‑state=string] [‑‑compartment‑id=string]
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> list images <‑‑compartment‑id=string> [‑‑state=string]
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> list vnicattachments <‑‑compartment‑id=string> [‑‑filter=instanceId | vnicId | availabilityDomain=value...]
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> instance create <‑‑domain‑name=name> <‑‑image‑id=id | ‑‑boot‑volume‑id=id> <‑‑display‑name=name> <‑‑shape=type> <‑‑subnet=id> [‑‑boot‑disk‑size=size in GB] [‑‑publicip=true | false] [‑‑privateip=IP address] [‑‑public‑ssh‑key=key string...] [‑‑launch‑mode=NATIVE | EMULATED | PARAVIRTUALIZED] [‑‑cloud‑init‑script‑path=path to a script]
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> instance info <‑‑id=unique id>
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> instance terminate <‑‑id=unique id>
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> instance start <‑‑id=unique id>
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> instance pause <‑‑id=unique id>
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> instance reset <‑‑id=unique id>
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> instance clone <‑‑id=unique id> [‑‑clone‑name=name for a clone instance]
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> instance metriclist <‑‑id=unique id>
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> instance metricdata <‑‑id=unique id> <‑‑metric‑name=metric name> <‑‑metric‑points=number of history metric points>
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> image create <‑‑display‑name=name> [‑‑bucket‑name=name] [‑‑object‑name=name] [‑‑instance‑id=unique id]
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> image info <‑‑id=unique id>
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> image delete <‑‑id=unique id>
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> image import <‑‑id=unique id> [‑‑bucket‑name=name] [‑‑object‑name=name]
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> image export <‑‑id=unique id> <‑‑display‑name=name> [‑‑bucket‑name=name] [‑‑object‑name=name]
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> network setup [‑‑gateway‑os‑name=string] [‑‑gateway‑os‑version=string] [‑‑gateway‑shape=string] [‑‑tunnel‑network‑name=string] [‑‑tunnel‑network‑range=string] [‑‑proxy=string] [‑‑compartment‑id=string]
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> network create <‑‑name=string> <‑‑network‑id=string> [‑‑enable | ‑‑disable]
VBoxManage cloud network update <‑‑name=string> [‑‑network‑id=string] [‑‑enable | ‑‑disable]
VBoxManage cloud network delete <‑‑name=string>
VBoxManage cloud network info <‑‑name=string>
VBoxManage cloudprofile <‑‑provider=name> <‑‑profile=name>add [‑‑clouduser=unique id] [‑‑fingerprint=MD5 string] [‑‑keyfile=path] [‑‑passphrase=string] [‑‑tenancy=unique id] [‑‑compartment=unique id] [‑‑region=string]
VBoxManage cloudprofile <‑‑provider=name> <‑‑profile=name>update [‑‑clouduser=unique id] [‑‑fingerprint=MD5 string] [‑‑keyfile=path] [‑‑passphrase=string] [‑‑tenancy=unique id] [‑‑compartment=unique id] [‑‑region=string]
VBoxManage cloudprofile <‑‑provider=name> <‑‑profile=name>delete
VBoxManage cloudprofile <‑‑provider=name> <‑‑profile=name>show
VBoxManage controlvm <uuid | vmname>pause
VBoxManage controlvm <uuid | vmname>resume
VBoxManage controlvm <uuid | vmname>reset
VBoxManage controlvm <uuid | vmname>poweroff
VBoxManage controlvm <uuid | vmname>savestate
VBoxManage controlvm <uuid | vmname>acpipowerbutton
VBoxManage controlvm <uuid | vmname>acpisleepbutton
VBoxManage controlvm <uuid | vmname>reboot
VBoxManage controlvm <uuid | vmname>shutdown [‑‑force]
VBoxManage controlvm <uuid | vmname>keyboardputscancode <hex> [hex...]
VBoxManage controlvm <uuid | vmname>keyboardputstring <string> [string...]
VBoxManage controlvm <uuid | vmname>keyboardputfile <filename>
VBoxManage controlvm <uuid | vmname>setlinkstateN <on | off>
VBoxManage controlvm <uuid | vmname>nicN <null | nat | bridged | intnet | hostonly | generic | natnetwork> [device‑name]
VBoxManage controlvm <uuid | vmname>nictraceN <on | off>
VBoxManage controlvm <uuid | vmname>nictracefileN <filename>
VBoxManage controlvm <uuid | vmname>nicpropertyN <prop‑name=prop‑value>
VBoxManage controlvm <uuid | vmname>nicpromiscN <deny | allow‑vms | allow‑all>
VBoxManage controlvm <uuid | vmname>natpfN <[rulename],<tcp|udp>,[host‑IP],hostport,[guest‑IP],guestport>
VBoxManage controlvm <uuid | vmname>natpfNdelete <rulename>
VBoxManage controlvm <uuid | vmname>guestmemoryballoon <balloon‑size>
VBoxManage controlvm <uuid | vmname>usbattach <uuid | address> [‑‑capturefile=filename]
VBoxManage controlvm <uuid | vmname>usbdetach <uuid | address>
VBoxManage controlvm <uuid | vmname>audioin <on | off>
VBoxManage controlvm <uuid | vmname>audioout <on | off>
VBoxManage controlvm <uuid | vmname>clipboard mode <disabled | hosttoguest | guesttohost | bidirectional>
VBoxManage controlvm <uuid | vmname>clipboard filetransfers <on | off>
VBoxManage controlvm <uuid | vmname>draganddrop <disabled | hosttoguest | guesttohost | bidirectional>
VBoxManage controlvm <uuid | vmname>vrde <on | off>
VBoxManage controlvm <uuid | vmname>vrdeport <port>
VBoxManage controlvm <uuid | vmname>vrdeproperty <prop‑name=prop‑value>
VBoxManage controlvm <uuid | vmname>vrdevideochannelquality <percentage>
VBoxManage controlvm <uuid | vmname>setvideomodehint <xres> <yres> <bpp> [display [<yes | no> [x‑originy‑origin]]]
VBoxManage controlvm <uuid | vmname>setscreenlayout <display> <on | primaryx‑originy‑originx‑resolutiony‑resolutionbpp | off>
VBoxManage controlvm <uuid | vmname>screenshotpng <filename> [display]
VBoxManage controlvm <uuid | vmname>recording <on | off> VBoxManage controlvm <uuid | vmname>recording start [‑‑wait] VBoxManage controlvm <uuid | vmname>recording stop VBoxManage controlvm <uuid | vmname>recording attach VBoxManage controlvm <uuid | vmname>recording screens <all | none | screen‑ID,screen‑ID...> VBoxManage controlvm <uuid | vmname>recording filename <filename> VBoxManage controlvm <uuid | vmname>recording videores <<width>x <height>> VBoxManage controlvm <uuid | vmname>recording videorate <rate> VBoxManage controlvm <uuid | vmname>recording videofps <fps> VBoxManage controlvm <uuid | vmname>recording maxtime <sec> VBoxManage controlvm <uuid | vmname>recording maxfilesize <MB> VBoxManage controlvm <uuid | vmname>recording opts <key= [value]>
VBoxManage controlvm <uuid | vmname>setcredentials <username>‑‑passwordfile=<filename | password> <domain‑name>‑‑allowlocallogon=<yes | no>
VBoxManage controlvm <uuid | vmname>teleport <‑‑host=host‑name> <‑‑port=port‑name> [‑‑maxdowntime=msec] [‑‑passwordfile=filename | ‑‑password=password]
VBoxManage controlvm <uuid | vmname>plugcpu <ID>
VBoxManage controlvm <uuid | vmname>unplugcpu <ID>
VBoxManage controlvm <uuid | vmname>cpuexecutioncap <num>
VBoxManage controlvm <uuid | vmname>vm‑process‑priority <default | flat | low | normal | high>
VBoxManage controlvm <uuid | vmname>webcam attach [pathname [settings]]
VBoxManage controlvm <uuid | vmname>webcam detach [pathname]
VBoxManage controlvm <uuid | vmname>webcam list
VBoxManage controlvm <uuid | vmname>addencpassword <ID> <password‑file | ‑> [‑‑removeonsuspend=yes | no]
VBoxManage controlvm <uuid | vmname>removeencpassword <ID>
VBoxManage controlvm <uuid | vmname>removeallencpasswords
VBoxManage controlvm <uuid | vmname>changeuartmodeNdisconnected | serverpipe‑name | clientpipe‑name | tcpserverport | tcpclienthostname:port | filefilename | device‑name
VBoxManage controlvm <uuid | vmname>autostart‑enabledNon | off
VBoxManage controlvm <uuid | vmname>autostart‑delay <seconds>
VBoxManage convertfromraw <inputfile> <outputfile> [‑‑format=VDI | VMDK | VHD] [‑‑uuid=uuid] [‑‑variant=Standard|Fixed|Split2G|Stream|ESX...]
VBoxManage convertfromraw stdin <outputfile> <bytes> [‑‑format=VDI | VMDK | VHD] [‑‑uuid=uuid] [‑‑variant=Standard|Fixed|Split2G|Stream|ESX...]
VBoxManage createmedium [disk | dvd | floppy] <‑‑filename=filename> [‑‑size=megabytes | ‑‑sizebyte=bytes] [‑‑diffparent=UUID | filename] [‑‑format=VDI | VMDK | VHD] [‑‑variant=Standard|Fixed|Split2G|Stream|ESX|Formatted|RawDisk...] [‑‑property=name=value...] [‑‑property‑file=name=/path/to/file/with/value...]
VBoxManage createvm <‑‑name=name> <‑‑platform‑architecture=x86 | arm> [‑‑basefolder=basefolder] [‑‑default] [‑‑groups=group‑ID [,...]] [‑‑ostype=ostype] [‑‑register] [‑‑uuid=uuid] [‑‑cipher=cipher] [‑‑password‑id=password‑id] [‑‑password=file]
VBoxManage debugvm <uuid | vmname>dumpvmcore <‑‑filename=name>
VBoxManage debugvm <uuid | vmname>info <item> [args...]
VBoxManage debugvm <uuid | vmname>injectnmi
VBoxManage debugvm <uuid | vmname>log [‑‑release | ‑‑debug] [group‑settings...]
VBoxManage debugvm <uuid | vmname>logdest [‑‑release | ‑‑debug] [destinations...]
VBoxManage debugvm <uuid | vmname>logflags [‑‑release | ‑‑debug] [flags...]
VBoxManage debugvm <uuid | vmname>osdetect
VBoxManage debugvm <uuid | vmname>osinfo
VBoxManage debugvm <uuid | vmname>osdmesg [‑‑lines=lines]
VBoxManage debugvm <uuid | vmname>getregisters [‑‑cpu=id] [reg‑set.reg‑name...]
VBoxManage debugvm <uuid | vmname>setregisters [‑‑cpu=id] [reg‑set.reg‑name=value...]
VBoxManage debugvm <uuid | vmname>show [‑‑human‑readable | ‑‑sh‑export | ‑‑sh‑eval | ‑‑cmd‑set] [settings‑item...]
VBoxManage debugvm <uuid | vmname>stack [‑‑cpu=id]
VBoxManage debugvm <uuid | vmname>statistics [‑‑reset] [‑‑descriptions] [‑‑pattern=pattern]
VBoxManage debugvm <uuid | vmname>guestsample [‑‑filename=filename] [‑‑sample‑interval‑us=interval] [‑‑sample‑time‑us=time]
VBoxManage dhcpserver add <‑‑network=netname | ‑‑interface=ifname> <‑‑server‑ip=address> <‑‑netmask=mask> <‑‑lower‑ip=address> <‑‑upper‑ip=address> <‑‑enable | ‑‑disable> [[‑‑global] [‑‑set‑opt=dhcp‑opt‑no value...] [‑‑set‑opt‑hex=dhcp‑opt‑no hexstring...] [‑‑force‑opt=dhcp‑opt‑no...] [‑‑suppress‑opt=dhcp‑opt‑no...] [‑‑min‑lease‑time=seconds] [‑‑default‑lease‑time=seconds] [‑‑max‑lease‑time=seconds]...] [<‑‑group=name> [‑‑set‑opt=dhcp‑opt‑no value...] [‑‑set‑opt‑hex=dhcp‑opt‑no hexstring...] [‑‑force‑opt=dhcp‑opt‑no...] [‑‑suppress‑opt=dhcp‑opt‑no...] [‑‑incl‑mac=address...] [‑‑excl‑mac=address...] [‑‑incl‑mac‑wild=pattern...] [‑‑excl‑mac‑wild=pattern...] [‑‑incl‑vendor=string...] [‑‑excl‑vendor=string...] [‑‑incl‑vendor‑wild=pattern...] [‑‑excl‑vendor‑wild=pattern...] [‑‑incl‑user=string...] [‑‑excl‑user=string...] [‑‑incl‑user‑wild=pattern...] [‑‑excl‑user‑wild=pattern...] [‑‑min‑lease‑time=seconds] [‑‑default‑lease‑time=seconds] [‑‑max‑lease‑time=seconds]...] [<‑‑vm=name|uuid> [‑‑nic=1‑N] [‑‑set‑opt=dhcp‑opt‑no value...] [‑‑set‑opt‑hex=dhcp‑opt‑no hexstring...] [‑‑force‑opt=dhcp‑opt‑no...] [‑‑suppress‑opt=dhcp‑opt‑no...] [‑‑min‑lease‑time=seconds] [‑‑default‑lease‑time=seconds] [‑‑max‑lease‑time=seconds] [‑‑fixed‑address=address]...] [<‑‑mac‑address=address> [‑‑set‑opt=dhcp‑opt‑no value...] [‑‑set‑opt‑hex=dhcp‑opt‑no hexstring...] [‑‑force‑opt=dhcp‑opt‑no...] [‑‑suppress‑opt=dhcp‑opt‑no...] [‑‑min‑lease‑time=seconds] [‑‑default‑lease‑time=seconds] [‑‑max‑lease‑time=seconds] [‑‑fixed‑address=address]...]
VBoxManage dhcpserver modify <‑‑network=netname | ‑‑interface=ifname> [‑‑server‑ip=address] [‑‑lower‑ip=address] [‑‑upper‑ip=address] [‑‑netmask=mask] [‑‑enable | ‑‑disable] [[‑‑global] [‑‑del‑opt=dhcp‑opt‑no...] [‑‑set‑opt=dhcp‑opt‑no value...] [‑‑set‑opt‑hex=dhcp‑opt‑no hexstring...] [‑‑force‑opt=dhcp‑opt‑no...] [‑‑unforce‑opt=dhcp‑opt‑no...] [‑‑suppress‑opt=dhcp‑opt‑no...] [‑‑unsuppress‑opt=dhcp‑opt‑no...] [‑‑min‑lease‑time=seconds] [‑‑default‑lease‑time=seconds] [‑‑max‑lease‑time=seconds] [‑‑remove‑config]...] [<‑‑group=name> [‑‑set‑opt=dhcp‑opt‑no value...] [‑‑set‑opt‑hex=dhcp‑opt‑no hexstring...] [‑‑force‑opt=dhcp‑opt‑no...] [‑‑unforce‑opt=dhcp‑opt‑no...] [‑‑suppress‑opt=dhcp‑opt‑no...] [‑‑unsuppress‑opt=dhcp‑opt‑no...] [‑‑del‑mac=address...] [‑‑incl‑mac=address...] [‑‑excl‑mac=address...] [‑‑del‑mac‑wild=pattern...] [‑‑incl‑mac‑wild=pattern...] [‑‑excl‑mac‑wild=pattern...] [‑‑del‑vendor=string...] [‑‑incl‑vendor=string...] [‑‑excl‑vendor=string...] [‑‑del‑vendor‑wild=pattern...] [‑‑incl‑vendor‑wild=pattern...] [‑‑excl‑vendor‑wild=pattern...] [‑‑del‑user=string...] [‑‑incl‑user=string...] [‑‑excl‑user=string...] [‑‑del‑user‑wild=pattern...] [‑‑incl‑user‑wild=pattern...] [‑‑excl‑user‑wild=pattern...] [‑‑zap‑conditions] [‑‑min‑lease‑time=seconds] [‑‑default‑lease‑time=seconds] [‑‑max‑lease‑time=seconds] [‑‑remove‑config]...] [<‑‑vm=name|uuid> [‑‑nic=1‑N] [‑‑del‑opt=dhcp‑opt‑no...] [‑‑set‑opt=dhcp‑opt‑no value...] [‑‑set‑opt‑hex=dhcp‑opt‑no hexstring...] [‑‑force‑opt=dhcp‑opt‑no...] [‑‑unforce‑opt=dhcp‑opt‑no...] [‑‑suppress‑opt=dhcp‑opt‑no...] [‑‑unsuppress‑opt=dhcp‑opt‑no...] [‑‑min‑lease‑time=seconds] [‑‑default‑lease‑time=seconds] [‑‑max‑lease‑time=seconds] [‑‑fixed‑address=address] [‑‑remove‑config]...] [<‑‑mac‑address=address> [‑‑del‑opt=dhcp‑opt‑no...] [‑‑set‑opt=dhcp‑opt‑no value...] [‑‑set‑opt‑hex=dhcp‑opt‑no hexstring...] [‑‑force‑opt=dhcp‑opt‑no...] [‑‑unforce‑opt=dhcp‑opt‑no...] [‑‑suppress‑opt=dhcp‑opt‑no...] [‑‑unsuppress‑opt=dhcp‑opt‑no...] [‑‑min‑lease‑time=seconds] [‑‑default‑lease‑time=seconds] [‑‑max‑lease‑time=seconds] [‑‑fixed‑address=address] [‑‑remove‑config]...]
VBoxManage dhcpserver remove <‑‑network=netname | ‑‑interface=ifname>
VBoxManage dhcpserver start <‑‑network=netname | ‑‑interface=ifname>
VBoxManage dhcpserver restart <‑‑network=netname | ‑‑interface=ifname>
VBoxManage dhcpserver stop <‑‑network=netname | ‑‑interface=ifname>
VBoxManage dhcpserver findlease <‑‑network=netname | ‑‑interface=ifname> <‑‑mac‑address=mac>
VBoxManage discardstate <uuid | vmname>
VBoxManage encryptmedium <uuid | filename> [‑‑cipher=cipher‑ID] [‑‑newpassword=password] [‑‑newpasswordid=password‑ID] [‑‑oldpassword=password]
VBoxManage encryptvm <uuid | vmname>setencryption‑‑old‑passwordfile‑‑ciphercipher‑identifier‑‑new‑passwordfile‑‑new‑password‑idpassword‑identifier‑‑force
VBoxManage encryptvm <uuid | vmname>checkpassword <file>
VBoxManage encryptvm <uuid | vmname>addpassword‑‑passwordfile‑‑password‑idpassword‑identifier
VBoxManage encryptvm <uuid | vmname>removepassword <password‑identifier>
VBoxManage export <machines> <‑‑output=name> [‑‑legacy09 | ‑‑ovf09 | ‑‑ovf10 | ‑‑ovf20] [‑‑manifest] [‑‑options=manifest | iso | nomacs | nomacsbutnat...] [‑‑vsys=virtual‑system‑number] [‑‑description=description‑info] [‑‑eula=license‑text] [‑‑eulafile=filename] [‑‑product=product‑name] [‑‑producturl=product‑URL] [‑‑vendor=vendor‑name] [‑‑vendorurl=vendor‑URL] [‑‑version=version‑info] [‑‑vmname=vmname]
VBoxManage export <machine> <‑‑output=cloud‑service‑provider> [‑‑opc10] [‑‑vmname=vmname] [‑‑cloud=virtual‑system‑number] [‑‑cloudprofile=cloud‑profile‑name] [‑‑cloudshape=cloud‑shape‑name] [‑‑clouddomain=cloud‑domain] [‑‑clouddisksize=disk‑size‑in‑GB] [‑‑cloudbucket=bucket‑name] [‑‑cloudocivcn=OCI‑VCN‑ID] [‑‑cloudocisubnet=OCI‑subnet‑ID] [‑‑cloudkeepobject=true | false] [‑‑cloudlaunchinstance=true | false] [‑‑cloudlaunchmode=EMULATED | PARAVIRTUALIZED] [‑‑cloudpublicip=true | false]
VBoxManage extpack install [‑‑replace] [‑‑accept‑license=sha256] <tarball>
VBoxManage extpack uninstall [‑‑force] <name>
VBoxManage extpack cleanup
VBoxManage getextradata <global | uuid | vmname> <keyword | enumerate>
VBoxManage guestcontrol <uuid | vmname>run [‑‑arg0=argument 0] [‑‑domain=domainname] [‑‑dos2unix] [‑‑exe=filename] [‑‑ignore‑orphaned‑processes] [‑‑no‑wait‑stderr | ‑‑wait‑stderr] [‑‑no‑wait‑stdout | ‑‑wait‑stdout] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑profile] [‑‑putenv=var‑name=[value]] [‑‑quiet] [‑‑timeout=msec] [‑‑unix2dos] [‑‑unquoted‑args] [‑‑username=username] [‑‑cwd=directory] [‑‑verbose] <‑‑[argument...]>
VBoxManage guestcontrol <uuid | vmname>start [‑‑arg0=argument 0] [‑‑domain=domainname] [‑‑exe=filename] [‑‑ignore‑orphaned‑processes] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑profile] [‑‑putenv=var‑name=[value]] [‑‑quiet] [‑‑timeout=msec] [‑‑unquoted‑args] [‑‑username=username] [‑‑cwd=directory] [‑‑verbose] <‑‑[argument...]>
VBoxManage guestcontrol <uuid | vmname>copyfrom [‑‑dereference] [‑‑domain=domainname] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑quiet] [‑‑no‑replace] [‑‑recursive] [‑‑target‑directory=host‑destination‑dir] [‑‑update] [‑‑username=username] [‑‑verbose] <guest‑source0>guest‑source1[...] <host‑destination>
VBoxManage guestcontrol <uuid | vmname>copyto [‑‑dereference] [‑‑domain=domainname] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑quiet] [‑‑no‑replace] [‑‑recursive] [‑‑target‑directory=guest‑destination‑dir] [‑‑update] [‑‑username=username] [‑‑verbose] <host‑source0>host‑source1[...]
VBoxManage guestcontrol <uuid | vmname>mkdir [‑‑domain=domainname] [‑‑mode=mode] [‑‑parents] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑quiet] [‑‑username=username] [‑‑verbose] <guest‑directory...>
VBoxManage guestcontrol <uuid | vmname>rmdir [‑‑domain=domainname] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑quiet] [‑‑recursive] [‑‑username=username] [‑‑verbose] <guest‑directory...>
VBoxManage guestcontrol <uuid | vmname>rm [‑‑domain=domainname] [‑‑force] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑quiet] [‑‑username=username] [‑‑verbose] <guest‑directory...>
VBoxManage guestcontrol <uuid | vmname>mv [‑‑domain=domainname] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑quiet] [‑‑username=username] [‑‑verbose] <source...> <destination‑directory>
VBoxManage guestcontrol <uuid | vmname>mktemp [‑‑directory] [‑‑domain=domainname] [‑‑mode=mode] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑quiet] [‑‑secure] [‑‑tmpdir=directory‑name] [‑‑username=username] [‑‑verbose] <template‑name>
VBoxManage guestcontrol <uuid | vmname>mount [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑username=username] [‑‑verbose]
VBoxManage guestcontrol <uuid | vmname>fsinfo [‑‑domain=domainname] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑human‑readable] [‑‑quiet] [‑‑total] [‑‑username=username] [‑‑verbose] <path>
VBoxManage guestcontrol <uuid | vmname>stat [‑‑domain=domainname] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑quiet] [‑‑username=username] [‑‑verbose] <filename>
VBoxManage guestcontrol <uuid | vmname>list <all | files | processes | sessions> [‑‑quiet] [‑‑verbose]
VBoxManage guestcontrol <uuid | vmname>closeprocess [‑‑session‑id=ID | ‑‑session‑name=name‑or‑pattern] [‑‑quiet] [‑‑verbose] <PID...>
VBoxManage guestcontrol <uuid | vmname>closesession [‑‑all | ‑‑session‑id=ID | ‑‑session‑name=name‑or‑pattern] [‑‑quiet] [‑‑verbose]
VBoxManage guestcontrol <uuid | vmname>updatega [‑‑quiet] [‑‑verbose] [‑‑source=guest‑additions.ISO] [‑‑wait‑start] [‑‑[argument...]]
VBoxManage guestcontrol <uuid | vmname>watch [‑‑quiet] [‑‑verbose]
VBoxManage guestproperty get <uuid | vmname> <property‑name> [‑‑verbose]
VBoxManage guestproperty enumerate <uuid | vmname> [‑‑no‑timestamp] [‑‑no‑flags] [‑‑relative] [‑‑old‑format] [patterns...]
VBoxManage guestproperty set <uuid | vmname> <property‑name> [property‑value [‑‑flags=flags]]
VBoxManage guestproperty unset <uuid | vmname> <property‑name>
VBoxManage guestproperty wait <uuid | vmname> <patterns> [‑‑timeout=msec] [‑‑fail‑on‑timeout]
VBoxManage hostonlyif ipconfig <ifname> [‑‑dhcp | ‑‑ip=IPv4‑address‑‑netmask=IPv4‑netmask | ‑‑ipv6=IPv6‑address‑‑netmasklengthv6=length]
VBoxManage hostonlyif create
VBoxManage hostonlyif remove <ifname>
VBoxManage hostonlynet add <‑‑name=netname> [‑‑id=netid] <‑‑netmask=mask> <‑‑lower‑ip=address> <‑‑upper‑ip=address> [‑‑enable | ‑‑disable]
VBoxManage hostonlynet modify <‑‑name=netname | ‑‑id=netid> [‑‑lower‑ip=address] [‑‑upper‑ip=address] [‑‑netmask=mask] [‑‑enable | ‑‑disable]
VBoxManage hostonlynet remove <‑‑name=netname | ‑‑id=netid>
VBoxManage import <ovfname | ovaname> [‑‑dry‑run] [‑‑options=keepallmacs | keepnatmacs | importtovdi] [‑‑vsys=n] [‑‑ostype=ostype] [‑‑vmname=name] [‑‑settingsfile=filename] [‑‑basefolder=folder] [‑‑group=group] [‑‑memory=MB] [‑‑cpus=n] [‑‑description=text] [‑‑eula=show | accept] [‑‑unit=n] [‑‑ignore] [‑‑scsitype=BusLogic | LsiLogic] [‑‑disk=path] [‑‑controller=index] [‑‑port=n]
VBoxManage import OCI://‑‑cloud [‑‑ostype=ostype] [‑‑vmname=name] [‑‑basefolder=folder] [‑‑memory=MB] [‑‑cpus=n] [‑‑description=text] <‑‑cloudprofile=profile> <‑‑cloudinstanceid=id> [‑‑cloudbucket=bucket]
VBoxManage list [‑‑long] [‑‑platform‑arch=x86 | arm] [‑‑sorted] [bridgedifs | cloudnets | cloudprofiles | cloudproviders | cpu‑profiles | dhcpservers | dvds | extpacks | floppies | groups | hddbackends | hdds | hostcpuids | hostdrives | hostdvds | hostfloppies | hostinfo | hostonlyifs | hostonlynets | intnets | natnets | ostypes | ossubtypes | runningvms | screenshotformats | systemproperties | usbfilters | usbhost | vms | webcams]
VBoxManage mediumio <‑‑disk=uuid|filename | ‑‑dvd=uuid|filename | ‑‑floppy=uuid|filename> [‑‑password‑file=‑|filename]formatfat [‑‑quick]
VBoxManage mediumio <‑‑disk=uuid|filename | ‑‑dvd=uuid|filename | ‑‑floppy=uuid|filename> [‑‑password‑file=‑|filename]cat [‑‑hex] [‑‑offset=byte‑offset] [‑‑size=bytes] [‑‑output=‑|filename]
VBoxManage mediumio <‑‑disk=uuid|filename | ‑‑dvd=uuid|filename | ‑‑floppy=uuid|filename> [‑‑password‑file=‑|filename]stream [‑‑format=image‑format] [‑‑variant=image‑variant] [‑‑output=‑|filename]
VBoxManage mediumproperty [disk | dvd | floppy]set <uuid | filename> <property‑name> <property‑value>
VBoxManage mediumproperty [disk | dvd | floppy]get <uuid | filename> <property‑name>
VBoxManage mediumproperty [disk | dvd | floppy]delete <uuid | filename> <property‑name>
VBoxManage metrics collect [‑‑detach] [‑‑list] [‑‑period=seconds] [‑‑samples=count] [* | host | vmnamemetric‑list]
VBoxManage metrics disable [‑‑list] [* | host | vmnamemetric‑list]
VBoxManage metrics enable [‑‑list] [* | host | vmnamemetric‑list]
VBoxManage metrics list [* | host | vmnamemetric‑list]
VBoxManage metrics query [* | host | vmnamemetric‑list]
VBoxManage metrics setup [‑‑list] [‑‑periodseconds] [‑‑samplescount] [* | host | vmnamemetric‑list]
VBoxManage modifymedium [disk | dvd | floppy] <uuid | filename> [‑‑autoreset=on | off] [‑‑compact] [‑‑description=description] [‑‑move=pathname] [‑‑property=name=[value]] [‑‑resize=megabytes | ‑‑resizebyte=bytes] [‑‑setlocation=pathname] [‑‑type=normal | writethrough | immutable | shareable | readonly | multiattach]
VBoxManage modifynvram <uuid | vmname>inituefivarstore
VBoxManage modifynvram <uuid | vmname>enrollmssignatures
VBoxManage modifynvram <uuid | vmname>enrollorclpk
VBoxManage modifynvram <uuid | vmname>enrollpk [‑‑platform‑key=filename] [‑‑owner‑uuid=uuid]
VBoxManage modifynvram <uuid | vmname>enrollmok [‑‑mok=filename] [‑‑owner‑uuid=uuid]
VBoxManage modifynvram <uuid | vmname>secureboot <‑‑enable | ‑‑disable>
VBoxManage modifynvram <uuid | vmname>listvars
VBoxManage modifynvram <uuid | vmname>queryvar [‑‑name=name] [‑‑filename=filename]
VBoxManage modifynvram <uuid | vmname>deletevar [‑‑name=name] [‑‑owner‑uuid=uuid]
VBoxManage modifynvram <uuid | vmname>changevar [‑‑name=name] [‑‑filename=filename]
VBoxManage modifyvm <uuid | vmname> [‑‑name=name] [‑‑groups=group [,group...]] [‑‑description=description] [‑‑os‑type=OS‑type] [‑‑icon‑file=filename] [‑‑memory=size‑in‑MB] [‑‑page‑fusion=on | off] [‑‑vram=size‑in‑MB] [‑‑acpi=on | off] [‑‑ioapic=on | off] [‑‑hardware‑uuid=UUID] [‑‑cpus=CPU‑count] [‑‑cpu‑hotplug=on | off] [‑‑plug‑cpu=CPU‑ID] [‑‑unplug‑cpu=CPU‑ID] [‑‑cpu‑execution‑cap=number] [‑‑x86‑pae=on | off] [‑‑x86‑long‑mode=on | off] [‑‑ibpb‑on‑vm‑exit=on | off] [‑‑ibpb‑on‑vm‑entry=on | off] [‑‑spec‑ctrl=on | off] [‑‑l1d‑flush‑on‑sched=on | off] [‑‑l1d‑flush‑on‑vm‑entry=on | off] [‑‑mds‑clear‑on‑sched=on | off] [‑‑mds‑clear‑on‑vm‑entry=on | off] [‑‑cpu‑profile=host | Intel 8086 | Intel 80286 | Intel 80386] [‑‑x86‑hpet=on | off] [‑‑hwvirtex=on | off] [‑‑triple‑fault‑reset=on | off] [‑‑apic=on | off] [‑‑x86‑x2apic=on | off] [‑‑arm‑gic‑its=on | off] [‑‑paravirt‑provider=none | default | legacy | minimal | hyperv | kvm] [‑‑paravirt‑debug=key=value[,key=value...]] [‑‑nested‑paging=on | off] [‑‑large‑pages=on | off] [‑‑x86‑vtx‑vpid=on | off] [‑‑x86‑vtx‑ux=on | off] [‑‑nested‑hw‑virt=on | off] [‑‑virt‑vmsave‑vmload=on | off] [‑‑accelerate‑3d=on | off] [‑‑chipset=ich9 | piix3 | armv8virtual] [‑‑iommu=none | automatic | amd | intel] [‑‑tpm‑type=none | 1.2 | 2.0 | host | swtpm] [‑‑tpm‑location=location] [‑‑firmware‑logo‑fade‑in=on | off] [‑‑firmware‑logo‑fade‑out=on | off] [‑‑firmware‑logo‑display‑time=msec] [‑‑firmware‑logo‑image‑path=pathname] [‑‑firmware‑boot‑menu=disabled | menuonly | messageandmenu] [‑‑firmware‑apic=disabled | apic | x2apic] [‑‑firmware‑system‑time‑offset=msec] [‑‑firmware‑pxe‑debug=on | off] [‑‑system‑uuid‑le=on | off] [‑‑bootX=none | floppy | dvd | disk | net] [‑‑rtc‑use‑utc=on | off] [‑‑graphicscontroller=none | vboxvga | vmsvga | vboxsvga | qemuramfb] [‑‑snapshot‑folder=default | pathname] [‑‑firmware=bios | efi | efi32 | efi64] [‑‑guest‑memory‑balloon=size‑in‑MB] [‑‑default‑frontend=default | name] [‑‑vm‑process‑priority=default | flat | low | normal | high] [‑‑vm‑execution‑engine=default | hm | hwvirt | nem | native‑api | interpreter | recompiler]
VBoxManage modifyvm <uuid | vmname> [‑‑nicN=none | null | nat | bridged | intnet | hostonly | hostonlynet | generic | natnetwork | cloud] [‑‑nic‑typeN=Am79C970A | Am79C973 | 82540EM | 82543GC | 82545EM | virtio | usbnet] [‑‑cable‑connectedN=on | off] [‑‑nic‑traceN=on | off] [‑‑nic‑trace‑fileN=filename] [‑‑nic‑propertyN=name= [value]] [‑‑nic‑speedN=kbps] [‑‑nic‑boot‑prioN=priority] [‑‑nic‑promiscN=deny | allow‑vms | allow‑all] [‑‑nic‑bandwidth‑groupN=none | name] [‑‑bridge‑adapterN=none | device‑name] [‑‑cloud‑networkN=network‑name] [‑‑host‑only‑adapterN=none | device‑name] [‑‑host‑only‑netN=network‑name] [‑‑intnetN=network‑name] [‑‑nat‑networkN=network‑name] [‑‑nic‑generic‑drvN=driver‑name] [‑‑mac‑addressN=auto | MAC‑address]
VBoxManage modifyvm <uuid | vmname> [‑‑nat‑netN=network | default] [‑‑nat‑pfN=[rule‑name],tcp | udp,[host‑IP],hostport,[guest‑IP],guestport] [‑‑nat‑pfN=delete=rule‑name] [‑‑nat‑tftp‑prefixN=prefix] [‑‑nat‑tftp‑fileN=filename] [‑‑nat‑tftp‑serverN=IP‑address] [‑‑nat‑bind‑ipN=IP‑address] [‑‑nat‑dns‑pass‑domainN=on | off] [‑‑nat‑localhostreachableN=on | off] [‑‑nat‑settingsN=[mtu]] [‑‑nat‑forward‑broadcastN=on | off] [‑‑nat‑enable‑tftpN=on | off]
VBoxManage modifyvm <uuid | vmname> [‑‑mouse=ps2 | usb | usbtablet | usbmultitouch | usbmtscreenpluspad] [‑‑keyboard=ps2 | usb] [‑‑uartN=off | IO‑baseIRQ] [‑‑uart‑modeN=disconnected | serverpipe | clientpipe | tcpserverport | tcpclienthostname:port | filefilename | device‑name] [‑‑uart‑typeN=16450 | 16550A | 16750] [‑‑lpt‑modeN=device‑name] [‑‑lptN=off | IO‑baseIRQ] [‑‑audio‑controller=ac97 | hda | sb16] [‑‑audio‑codec=stac9700 | ad1980 | stac9221 | sb16] [‑‑audio‑driver=none | default | null | dsound | was | oss | alsa | pulse | coreaudio] [‑‑audio‑enabled=on | off] [‑‑audio‑in=on | off] [‑‑audio‑out=on | off] [‑‑clipboard‑mode=disabled | hosttoguest | guesttohost | bidirectional] [‑‑clipboard‑file‑transfers=enabled | disabled] [‑‑drag‑and‑drop=disabled | hosttoguest | guesttohost | bidirectional] [‑‑monitor‑count=number] [‑‑usb‑ehci=on | off] [‑‑usb‑ohci=on | off] [‑‑usb‑xhci=on | off] [‑‑usb‑rename=old‑namenew‑name]
VBoxManage modifyvm <uuid | vmname> [‑‑recording=on | off] [‑‑recording‑screens=all | none | screen‑ID[,screen‑ID...]] [‑‑recording‑file=filename] [‑‑recording‑max‑size=MB] [‑‑recording‑max‑time=seconds] [‑‑recording‑opts=key=value[,key=value...]] [‑‑recording‑video‑fps=fps] [‑‑recording‑video‑rate=rate] [‑‑recording‑video‑res=widthxheight]
VBoxManage modifyvm <uuid | vmname> [‑‑vrde=on | off] [‑‑vrde‑property=property‑name= [property‑value]] [‑‑vrde‑extpack=default | name] [‑‑vrde‑port=port] [‑‑vrde‑address=hostip] [‑‑vrde‑auth‑type=null | external | guest] [‑‑vrde‑auth‑library=default | name] [‑‑vrde‑multi‑con=on | off] [‑‑vrde‑reuse‑con=on | off] [‑‑vrde‑video‑channel=on | off] [‑‑vrde‑video‑channel‑quality=percent]
VBoxManage modifyvm <uuid | vmname> [‑‑teleporter=on | off] [‑‑teleporter‑port=port] [‑‑teleporter‑address=address | empty] [‑‑teleporter‑password=password] [‑‑teleporter‑password‑file=filename | stdin] [‑‑cpuid‑portability‑level=level] [‑‑cpuid‑set=leaf [:subleaf]eax ebx ecx edx] [‑‑cpuid‑remove=leaf [:subleaf]] [‑‑cpuid‑remove‑all]
VBoxManage modifyvm <uuid | vmname> [‑‑tracing‑enabled=on | off] [‑‑tracing‑config=string] [‑‑tracing‑allow‑vm‑access=on | off]
VBoxManage modifyvm <uuid | vmname> [‑‑usb‑card‑reader=on | off]
VBoxManage modifyvm <uuid | vmname> [‑‑autostart‑enabled=on | off] [‑‑autostart‑delay=seconds]
VBoxManage modifyvm <uuid | vmname> [‑‑guest‑debug‑provider=none | native | gdb | kd] [‑‑guest‑debug‑io‑provider=none | tcp | udp | ipc] [‑‑guest‑debug‑address=IP‑Address | path] [‑‑guest‑debug‑port=port]
VBoxManage modifyvm <uuid | vmname> [‑‑pci‑attach=host‑PCI‑address [@guest‑PCI‑bus‑address]] [‑‑pci‑detach=host‑PCI‑address]
VBoxManage modifyvm <uuid | vmname> [‑‑testing‑enabled=on | off] [‑‑testing‑mmio=on | off] [‑‑testing‑cfg‑dwordidx=value]
VBoxManage movevm <uuid | vmname> [‑‑type=basic] [‑‑folder=folder‑name]
VBoxManage natnetwork add [‑‑disable | ‑‑enable] <‑‑netname=name> <‑‑network=network> [‑‑dhcp=on|off] [‑‑ipv6=on|off] [‑‑loopback‑4=rule] [‑‑loopback‑6=rule] [‑‑port‑forward‑4=rule] [‑‑port‑forward‑6=rule]
VBoxManage natnetwork list [filter‑pattern]
VBoxManage natnetwork modify [‑‑dhcp=on|off] [‑‑disable | ‑‑enable] <‑‑netname=name> <‑‑network=network> [‑‑ipv6=on|off] [‑‑loopback‑4=rule] [‑‑loopback‑6=rule] [‑‑port‑forward‑4=rule] [‑‑port‑forward‑6=rule]
VBoxManage natnetwork remove <‑‑netname=name>
VBoxManage natnetwork start <‑‑netname=name>
VBoxManage natnetwork stop <‑‑netname=name>
VBoxManage objtracker ifaces
VBoxManage objtracker objlist <‑‑ifacename=VirtualBox interface name>
VBoxManage objtracker objinfo <‑‑ifacename=VirtualBox interface name> <‑‑id=Unique object Id>
VBoxManage registervm <filename>‑‑passwordfile
VBoxManage setextradata <global | uuid | vmname> <keyword> [value]
VBoxManage setproperty <property‑name> <property‑value>
VBoxManage sharedfolder add <global | uuid | vmname> <‑‑name=share‑name> <‑‑hostpath=hostpath> [‑‑readonly] [‑‑transient] [‑‑automount] [‑‑auto‑mount‑point=path]
VBoxManage sharedfolder remove <global | uuid | vmname> <‑‑name=share‑name> [‑‑transient]
VBoxManage sharedfolder modify <uuid | vmname> <‑‑name=share‑name> <‑‑readonly=true | false> <‑‑automount=true | false> <‑‑auto‑mount‑point=path> <‑‑symlink‑policy=forbidden | subtree | relative | any>
VBoxManage showmediuminfo [disk | dvd | floppy] <uuid | filename>
VBoxManage showvminfo <uuid | vmname> [‑‑details] [‑‑machinereadable] [‑‑password‑id] [‑‑password]
VBoxManage showvminfo <uuid | vmname> <‑‑log=index> [‑‑password‑idid] [‑‑passwordfile|‑]
VBoxManage signova <ova> <‑‑certificate=file> <‑‑private‑key=file> [‑‑private‑key‑password‑file=password‑file | ‑‑private‑key‑password=password] [‑‑digest‑type=type] [‑‑pkcs7 | ‑‑no‑pkcs7] [‑‑intermediate‑cert=file] [‑‑force] [‑‑verbose] [‑‑quiet] [‑‑dry‑run]
VBoxManage snapshot <uuid | vmname>
VBoxManage snapshot <uuid | vmname>take <snapshot‑name> [‑‑description=description] [‑‑live] [‑‑uniquename Number,Timestamp,Space,Force]
VBoxManage snapshot <uuid | vmname>delete <snapshot‑name>
VBoxManage snapshot <uuid | vmname>restore <snapshot‑name>
VBoxManage snapshot <uuid | vmname>restorecurrent
VBoxManage snapshot <uuid | vmname>edit <snapshot‑name | ‑‑current> [‑‑description=description] [‑‑name=new‑name]
VBoxManage snapshot <uuid | vmname>list [‑‑details | ‑‑machinereadable]
VBoxManage snapshot <uuid | vmname>showvminfo <snapshot‑name>
VBoxManage startvm [‑‑putenv=name[=value]] [‑‑type=<gui|headless|sdl|separate>] [‑‑password=file] [‑‑password‑id=password‑identifier] <uuid | vmname...>
VBoxManage storageattach <uuid | vmname> <‑‑storagectl=name> [‑‑bandwidthgroup=name | none] [‑‑comment=text] [‑‑device=number] [‑‑discard=on | off] [‑‑encodedlun=lun] [‑‑forceunmount] [‑‑hotpluggable=on | off] [‑‑initiator=initiator] [‑‑intnet] [‑‑lun=lun] [‑‑medium=none | emptydrive | additions | uuid | filename | host:drive | iscsi] [‑‑mtype=normal | writethrough | immutable | shareable | readonly | multiattach] [‑‑nonrotational=on | off] [‑‑passthrough=on | off] [‑‑passwordfile=file] [‑‑password=password] [‑‑port=number] [‑‑server=name | ip] [‑‑setparentuuid=uuid] [‑‑setuuid=uuid] [‑‑target=target] [‑‑tempeject=on | off] [‑‑tport=port] [‑‑type=dvddrive | fdd | hdd] [‑‑username=username]
VBoxManage storagectl <uuid | vmname> <‑‑name=controller‑name> [‑‑add=floppy | ide | pcie | sas | sata | scsi | usb] [‑‑controller=BusLogic | I82078 | ICH6 | IntelAhci | LSILogic | LSILogicSAS | NVMe | PIIX3 | PIIX4 | USB | VirtIO] [‑‑bootable=on | off] [‑‑hostiocache=on | off] [‑‑portcount=count] [‑‑remove] [‑‑rename=new‑controller‑name]
VBoxManage unattended detect <‑‑iso=install‑iso> [‑‑machine‑readable]
VBoxManage unattended install <uuid | vmname> <‑‑iso=install‑iso> [‑‑user=login] [‑‑user‑password=password] [‑‑user‑password‑file=file] [‑‑admin‑password=password] [‑‑admin‑password‑file=file] [‑‑full‑user‑name=name] [‑‑key=product‑key] [‑‑install‑additions] [‑‑no‑install‑additions] [‑‑additions‑iso=add‑iso] [‑‑install‑txs] [‑‑no‑install‑txs] [‑‑validation‑kit‑iso=testing‑iso] [‑‑locale=ll_CC] [‑‑country=CC] [‑‑time‑zone=tz] [‑‑proxy=url] [‑‑hostname=fqdn] [‑‑package‑selection‑adjustment=keyword] [‑‑dry‑run] [‑‑auxiliary‑base‑path=path] [‑‑image‑index=number] [‑‑script‑template=file] [‑‑post‑install‑template=file] [‑‑post‑install‑command=command] [‑‑extra‑install‑kernel‑parameters=params] [‑‑language=lang] [‑‑start‑vm=session‑type]
VBoxManage unregistervm <uuid | vmname> [‑‑delete] [‑‑delete‑all]
VBoxManage updatecheck perform [‑‑machine‑readable]
VBoxManage updatecheck list [‑‑machine‑readable]
VBoxManage updatecheck modify [‑‑disable | ‑‑enable] [‑‑channel=stable | withbetas | all] [‑‑frequency=days]
VBoxManage usbdevsource add <source‑name> <‑‑backend=backend> <‑‑address=address>
VBoxManage usbdevsource remove <source‑name>
VBoxManage usbfilter add <index,0‑N> <‑‑target=<uuid | vmname | global>> <‑‑name=string> <‑‑action=ignore | hold> [‑‑active=yes | no] [‑‑vendorid=XXXX] [‑‑productid=XXXX] [‑‑revision=IIFF] [‑‑manufacturer=string] [‑‑product=string] [‑‑port=hex] [‑‑remote=yes | no] [‑‑serialnumber=string] [‑‑maskedinterfaces=XXXXXXXX]
VBoxManage usbfilter modify <index,0‑N> <‑‑target=<uuid | vmname | global>> [‑‑name=string] [‑‑action=ignore | hold] [‑‑active=yes | no] [‑‑vendorid=XXXX| ""] [‑‑productid=XXXX| ""] [‑‑revision=IIFF| ""] [‑‑manufacturer=string| ""] [‑‑product=string| ""] [‑‑port=hex] [‑‑remote=yes | no] [‑‑serialnumber=string| ""] [‑‑maskedinterfaces=XXXXXXXX]
VBoxManage usbfilter remove <index,0‑N> <‑‑target=<uuid | vmname | global>>
Each time VBoxManage is invoked, only one command can be executed. However, a command might support several subcommands which then can be invoked in one single call. The following sections provide detailed reference information on the different commands.

VBoxManage
Oracle VirtualBox command-line interface

Synopsis
VBoxManage [‑q | ‑‑nologo] [‑‑settingspw=password] [‑‑settingspwfile=pw‑file] [@response‑file] [subcommand]
VBoxManage help [subcommand]
VBoxManage commands
VBoxManage [‑V | ‑‑version]
VBoxManage [‑‑dump‑build‑type]
Description
The VBoxManage command is the command-line interface (CLI) for the Oracle VirtualBox software. The CLI supports all the features that are available with the Oracle VirtualBox graphical user interface (GUI). In addition, you can use the VBoxManage command to manage the features of the virtualization engine that cannot be managed by the GUI.

Each time you invoke the VBoxManage command, only one command is executed. Note that some VBoxManage subcommands invoke several subcommands.

Run the VBoxManage command from the command line of the host operating system (OS) to control Oracle VirtualBox software.

The VBoxManage command is stored in the following locations on the host system:

Linux: /usr/bin/VBoxManage

Mac OS X: /Applications/VirtualBox.app/Contents/MacOS/VBoxManage

Oracle Solaris: /opt/VirtualBox/bin/VBoxManage

Windows: C:\Program Files\Oracle\VirtualBox\VBoxManage.exe

The VBoxManage command performs particular tasks by using subcommands, such as list, createvm, and startvm. See the associated information for each VBoxManage subcommand.

If required, specify the VM by its name or by its Universally Unique Identifier (UUID).

Use the VBoxManage list vms command to obtain information about all currently registered VMs, including the VM names and associated UUIDs.

Note that VM names which contain spaces or special characters must be enclosed in quotes.

General Options
--nologo
Suppresses the output of the logo information, which is useful for scripts.

The short version of this option is -q.

--settingspw=[password]
Specifies the settings password. You can optionally specify the password as an argument to this option. If you do not specify the password in this way, the VBoxManage command prompts you for the password.

The settings password is a security feature that encrypts stored settings, which are stored as plain text by default.

Settings which have been encrypted are done so using a one-way encryption algorithm so cannot be decrypted. Thus, if the settings are encrypted you must include the --settingspw or --settingspwfile option every time VBoxManage is used.

Only the iSCSI secret is encrypted at this time.

--settingspwfile=pw-filename
Specifies the file that contains the settings password.

--version
Shows version information about the VBoxManage command.

The short version of this option is -V.

@response-file
Loads arguments from the specified Bourne shell response file.

subcommand
Specifies one of the VBoxManage subcommands, such as controlvm, createvm, list, modifyvm, showvminfo, startvm, storageattach, and storagectl.

Each subcommand is described in its own command topic, some of which are shown in See Also sections.

Examples
The following command creates a virtual machine called Win8 and registers it with Oracle VirtualBox by using the --register option.

$ VBoxManage createvm --name "Win8" --register
Virtual machine 'Win8' is created.
UUID: UUID-string
Settings file: '/home/<USER>/VirtualBox VMs/Win8/Win8.vbox'
The command output shows that the Win8 VM is assigned a UUID and an XML machine settings file.

You can use the VBoxManage showvminfo command to view the configuration information of a VM.

The following example uses the VBoxManage modifyvm command to change the amount of memory for the Windows XP VM to be 1024 megabytes:

$ VBoxManage modifyvm "Windows XP" --memory 1024
Note that you can use the VBoxManage modifyvm command only for VMs that are powered off.

You can use the VBoxManage storagectl command or the VBoxManage storageattach command to modify the storage configuration for a VM. For example, to create a SATA storage controller called sata01 and add it to the ol7 VM:

$ VBoxManage storagectl ol7 --name "sata01" --add sata
Use the VBoxManage startvm command to start a VM that is currently powered off. For example, to start the win7 VM:

$ VBoxManage startvm win7
Use the VBoxManage controlvm command to pause or save a VM that is currently running. You can also use this command to modify settings for the VM. For example, to enable audio input for the ol6u9 VM.

$ VBoxManage controlvm ol6u9 audioin on
See Also
VBoxManage controlvm, VBoxManage createvm, VBoxManage list, VBoxManage modifyvm, VBoxManage showvminfo, VBoxManage startvm, VBoxManage storageattach, VBoxManage storagectl

VBoxManage adoptstate
Change a virtual machine's state based on a saved state file

Synopsis
VBoxManage adoptstate <uuid | vmname> <state‑filename>
Description
The VBoxManage adoptstate command enables you to change the state of a virtual machine (VM) to a state described in a saved state file (.sav). This action is referred to as a VM adopting a saved state file. The saved state file must be separate from the VM configuration.

When you start the VM after adopting the saved state, the VM restores its state from the saved state file.

Only use this command for custom deployments.

uuid | vmname
Specifies the Universally Unique Identifier (UUID) or name of the VM.

state-filename
Specifies the name of the saved state file.

Examples
The following command adopts a saved state file called mystate.sav by a VM called vm2. A subsequent start of the VM called vm2 restores the state from the saved state file mystate.sav.

$ VBoxManage adoptstate vm2 /home/<USER>/mystate.sav
See Also
VBoxManage discardstate

VBoxManage bandwidthctl
Manage bandwidth groups

Synopsis
VBoxManage bandwidthctl <uuid | vmname>add <bandwidth‑group‑name> <‑‑limit=bandwidth‑limit[k|m|g|K|M|G]> <‑‑type=disk | network>
VBoxManage bandwidthctl <uuid | vmname>list [‑‑machinereadable]
VBoxManage bandwidthctl <uuid | vmname>remove <bandwidth‑group‑name>
VBoxManage bandwidthctl <uuid | vmname>set <bandwidth‑group‑name> <‑‑limit=bandwidth‑limit[k|m|g|K|M|G]>
Description
The VBoxManage bandwidthctl command enables you to manage bandwidth groups for virtual machines (VMs). A bandwidth group specifies the bandwidth limit for the disks or for the network adapters of a VM.

Note that a network bandwidth limit applies only to the outbound traffic from the VM. The inbound traffic is unlimited.

Create a Bandwidth Group
VBoxManage bandwidthctl <uuid | vmname>add <bandwidth‑group‑name> <‑‑limit=bandwidth‑limit[k|m|g|K|M|G]> <‑‑type=disk | network>

The VBoxManage bandwidthctl add command creates a bandwidth group for the specified VM. You must specify whether the bandwidth group is for disks or for networks and specify the bandwidth limit.

uuid | vmname
Specifies the Universally Unique Identifier (UUID) or the name of the VM.

bandwidth-group-name
Specifies the name of the bandwidth group.

--type=disk|network
Specifies the type of the bandwidth group: disk or network. For more information, see Limiting Bandwidth for Disk Images or Limiting Bandwidth for Network Input/Output.

--limit=bandwidth-limit[k|m|g|K|M|G]
Specifies the bandwidth limit for a bandwidth group. The default unit is megabytes per second. You can modify this value while the VM is running.

You can change the unit by appending one of the following unit specifiers to the bandwidth limit:

k – kilobits per second

m – megabits per second

g – gigabits per second

K – kilobytes per second

M – megabytes per second

G – gigabytes per second

List Bandwidth Groups
VBoxManage bandwidthctl <uuid | vmname>list [‑‑machinereadable]

The VBoxManage bandwidthctl list command lists the all the bandwidth groups that have been defined for the specified VM. Use the --machinereadable option to produce the output in a machine-readable format, which uses name-value pairs.

uuid | vmname
Specifies the UUID or the name of the VM.

--machinereadable
Outputs the information about the bandwidth groups in name-value pairs.

Remove a Bandwidth Group
VBoxManage bandwidthctl <uuid | vmname>remove <bandwidth‑group‑name>

The VBoxManage bandwidthctl remove command removes a bandwidth group.

Note:
To successfully remove a bandwidth group, ensure that it is not referenced by any disk or adapter in the running VM.

uuid | vmname
Specifies the UUID or the name of the VM.

bandwidth-group-name
Specifies the name of the bandwidth group.

Modify the Bandwidth Limit of a Bandwidth Group
VBoxManage bandwidthctl <uuid | vmname>set <bandwidth‑group‑name> <‑‑limit=bandwidth‑limit[k|m|g|K|M|G]>

The VBoxManage bandwidthctl set command modifies the bandwidth limit for a bandwidth group.

uuid | vmname
Specifies the UUID or the name of the VM.

bandwidth-group-name
Specifies the name of the bandwidth group.

--limit=bandwidth-limit[k|m|g|K|M|G]
Specifies the bandwidth limit for a bandwidth group. The default unit is megabytes per second. You can modify this value while the VM is running.

You can change the unit by appending one of the following unit specifiers to the bandwidth limit:

k – kilobits per second

m – megabits per second

g – gigabits per second

K – kilobytes per second

M – megabytes per second

G – gigabytes per second

Examples
The following example shows how to use the VBoxManage bandwidthctl command to create the Limit bandwidth group and set the limit to 20 Mbps. Then use the VBoxManage modifyvm command to assign this bandwidth group to the first and second adapters of the vm1 VM.

$ VBoxManage bandwidthctl "vm1" add Limit --type network --limit 20m
$ VBoxManage modifyvm "vm1" --nicbandwidthgroup1 Limit
$ VBoxManage modifyvm "vm1" --nicbandwidthgroup2 Limit
You can dynamically modify the limit of a bandwidth group while the VM is running. The following example shows how to modify the limit for the Limit bandwidth group from 20 Mbps to 100 kbps:

$ VBoxManage bandwidthctl "vm1" set Limit --limit 100k
The following command disables shaping for all adapters in the Limit bandwidth group by specifying a limit of zero (0):

$ VBoxManage bandwidthctl "vm1" set Limit --limit 0
VBoxManage checkmediumpwd
Check encryption password on a DEK-encrypted medium or a disk image

Synopsis
VBoxManage checkmediumpwd <uuid | filename> <password‑file>
Description
The VBoxManage checkmediumpwd command checks the current encryption password on a DEK-encrypted medium or a disk image. See Encrypting Disk Images.

The command response indicates if the specified password is correct.

uuid | filename
Specifies the Universally Unique Identifier (UUID) or the absolute path name of the medium or image.

password-file
Specifies the password to check. The password can be the absolute path name of a password file on the host OS or the dash character (-) to prompt you for the password on the command line.

Examples
The following example checks the encryption password for the ol7u4-1.vdi disk image. The password is contained in a file called pwfile.

The command returns a message indicating that the specified password is correct.

$ VBoxManage checkmediumpwd "$HOME/VirtualBox VMs/ol7u4/ol7u4-1.vdi" /home/<USER>/pwfile
  The given password is correct
See Also
VBoxManage encryptmedium

VBoxManage clonemedium
Create a clone of a medium

Synopsis
VBoxManage clonemedium <uuid | source‑medium> <uuid | target‑medium> [disk | dvd | floppy] [‑‑existing] [‑‑format=VDI | VMDK | VHD | RAW | other] [‑‑variant=Standard|Fixed|Split2G|Stream|ESX...]
Description
The VBoxManage clonemedium command enables you to clone an existing medium (virtual disk, DVD, or floppy), which is typically an image file. Only the Universally Unique Identifier (UUID) differs between the original image and the cloned image.

You can use the Virtual Media Manager to transfer the cloned image to another host system or reimport it into Oracle VirtualBox. See The Virtual Media Manager and Cloning Disk Images.

uuid | source-medium
Specifies the UUID or the absolute or relative pathname of the source medium to clone. You can specify the UUID of the medium only if it is registered. Use the VBoxManage list hdds command to list registered images.

uuid | target-medium
Specifies the UUID or the absolute or relative pathname of the target (clone) medium. You can specify the UUID of the target medium only if it is registered. Use the VBoxManage list hdds command to list registered images.

disk | dvd | floppy
Specifies the type of the medium to clone. Valid values are disk, dvd, and floppy. The default value is disk.

--existing
Performs the clone operation by overwriting an existing target medium. The result is that only the portion of the source medium that fits into the existing target medium is copied.

If the target medium is smaller than the source, only the portion of the source medium up to the size of the target medium is copied.

If the target medium is larger than the source, the remaining part of the target medium is unchanged.

--format
Specifies the file format of the target medium if it differs from the format of the source medium. Valid values are VDI, VMDK, VHD, RAW, and other.

--variant=Standard | Fixed | Split2G | Stream | ESX [,...]
Specifies the file format variant for the target medium, which is a comma-separated list of variants. Following are the valid values:

Standard is the default disk image type, which has a dynamically allocated file size.

Fixed uses a disk image that has a fixed file size.

Split2G indicates that the disk image is split into 2GB segments. This value is for VMDK only.

Stream optimizes the disk image for downloading. This value is for VMDK only.

ESX is used for some VMWare products. This value is for VMDK only.

Note that not all variant combinations are valid. Specifying incompatible variant values in the list will produce an error message.

Note:
For compatibility with earlier versions of Oracle VirtualBox, you can use the clonevdi and clonehd commands instead of the clonemedium command.

Examples
The following command creates a clone of the disk01.vdi disk image file. The clone is called disk02.vdi.

$ VBoxManage clonemedium disk01.vdi disk02.vdi
The following command creates a clone of the disk01.vdi disk image file. The clone is in VMDK format and is called disk02.vmdk.

$ VBoxManage clonemedium disk01.vdi disk02.vmdk --format VMDK
See Also
VBoxManage list

VBoxManage clonevm
Create a clone of an existing virtual machine

Synopsis
VBoxManage clonevm <vmname|uuid> [‑‑basefolder=basefolder] [‑‑groups=group,...] [‑‑mode=machine | ‑‑mode=machinechildren | ‑‑mode=all] [‑‑name=name] [‑‑options=option,...] [‑‑register] [‑‑snapshot=snapshot‑name] [‑‑uuid=uuid]
Description
The VBoxManage clonevm command creates a clone of an existing virtual machine (VM). The clone can be a full copy of the VM or a linked copy of a VM.

You must specify the name or the universal unique identifier (UUID) of the VM you want to clone.

Command Operand and Options
The following list describes the operand and the options that you can use with the VBoxManage clonevm command:

vmname|uuid
Specifies the name or UUID of the VM to clone.

--basefolder=basefolder
Specifies the name of the folder in which to save the configuration for the new VM.

--groups=group,...
Assigns the clone to the specified group or groups. If you specify more than one group, separate each group name with a comma.

Note that each group is identified by a group ID that starts with a slash character (/) so that groups can be nested. By default, a clone is always assigned membership to the / group.

--mode=machine|machineandchildren|all
Specifies which of the following cloning modes to use:

machine mode clones the current state of the existing VM without any snapshots. This is the default mode.

machineandchildren mode clones the snapshot specified by by the --snapshot option and all child snapshots.

all mode clones all snapshots and the current state of the existing VM.

--name=name
Specifies a new name for the new VM. The default value is original-name Clone where original-name is the original name of the VM.

--options=option,...
Specifies how to create the new clone.

The --options argument can be used multiple times to enable multiple options, or the options can be given as a comma separated list. The options are case insensitive.

The following options (case-insensitive) are recognized:

Link
Creates a linked clone from a snapshot only.

KeepAllMACs
Specifies that the new clone reuses the MAC addresses of each virtual network card from the existing VM.

If you do not specify this option or the --options=keepnatmacs option, the default behavior is to reinitialize the MAC addresses of each virtual network card.

KeepNATMACs
Specifies that the new clone reuses the MAC addresses of each virtual network card from the existing VM when the network type is NAT.

If you do not specify this option or the KeepAllMACs option, the default behavior is to reinitialize the MAC addresses of each virtual network card.

KeepDiskNames
Specifies that the new clone reuses the disk image names from the existing VM. By default, disk images are renamed.

KeepHwUUIDs
Specifies that the new clone reuses the hardware IDs from the existing VM. By default, new UUIDs are used.

--register
Automatically registers the new clone in this Oracle VirtualBox installation. You can manually register the new VM later by using the VBoxManage registervm command. See VBoxManage registervm.

--snapshot=snapshot-name
Specifies the snapshot on which to base the new VM. By default, the clone is created from the current state of the specified VM.

--uuid=uuid
Specifies the UUID for the new VM. Ensure that this ID is unique for the Oracle VirtualBox instance if you decide to register this new VM. By default, Oracle VirtualBox provides a new UUID.

Examples
The following command creates and registers an exact clone of the ol7 VM. The clone is called ol7-dev-001.

The new clone includes all of the source VM's snapshots. The new VM also reuses all network interface MAC addresses, disk names, and UUIDs from the source VM.

$ VBoxManage clonevm ol7 --name="ol7-dev-001" --register --mode=all \
    --options=keepallmacs --options=keepdisknames --options=keephwuuids
The following command creates and registers a clone of the Snapshot 1 snapshot of the ol7 VM. The clone is called ol7-dev-002.

$ VBoxManage clonevm ol7 --name="ol7-dev-002" --register --snapshot="Snapshot 1"
See Also
VBoxManage registervm

VBoxManage closemedium
Remove a hard disk, DVD, or floppy image from the media registry

Synopsis
VBoxManage closemedium [disk | dvd | floppy] <uuid | filename> [‑‑delete]
Description
The VBoxManage closemedium command removes a hard disk, DVD, or floppy image from the list of known media used by Oracle VirtualBox. The image is then unavailable for selection in the Virtual Media Manager.

To use this command, the image must not be attached to any VMs.

Optionally, you can request that the image be deleted.

disk|dvd|floppy
Specifies the type of medium. Valid values are disk (hard drive), dvd, or floppy.

uuid|filename
Specifies the Universally Unique Identifier (UUID) or absolute path name of the medium or image.

--delete
Deletes the image file.

Examples
The following command removes the disk image file called disk01.vdi from the registry.

$ VBoxManage closemedium disk01.vdi
The following command removes the disk image file called disk01.vdi from the registry and deletes the image file.

$ VBoxManage closemedium disk01.vdi --delete
VBoxManage cloud
Manage the cloud entities

Synopsis
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> list instances [‑‑state=string] [‑‑compartment‑id=string]
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> list images <‑‑compartment‑id=string> [‑‑state=string]
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> list vnicattachments <‑‑compartment‑id=string> [‑‑filter=instanceId | vnicId | availabilityDomain=value...]
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> instance create <‑‑domain‑name=name> <‑‑image‑id=id | ‑‑boot‑volume‑id=id> <‑‑display‑name=name> <‑‑shape=type> <‑‑subnet=id> [‑‑boot‑disk‑size=size in GB] [‑‑publicip=true | false] [‑‑privateip=IP address] [‑‑public‑ssh‑key=key string...] [‑‑launch‑mode=NATIVE | EMULATED | PARAVIRTUALIZED] [‑‑cloud‑init‑script‑path=path to a script]
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> instance info <‑‑id=unique id>
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> instance terminate <‑‑id=unique id>
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> instance start <‑‑id=unique id>
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> instance pause <‑‑id=unique id>
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> instance reset <‑‑id=unique id>
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> instance clone <‑‑id=unique id> [‑‑clone‑name=name for a clone instance]
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> instance metriclist <‑‑id=unique id>
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> instance metricdata <‑‑id=unique id> <‑‑metric‑name=metric name> <‑‑metric‑points=number of history metric points>
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> image create <‑‑display‑name=name> [‑‑bucket‑name=name] [‑‑object‑name=name] [‑‑instance‑id=unique id]
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> image info <‑‑id=unique id>
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> image delete <‑‑id=unique id>
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> image import <‑‑id=unique id> [‑‑bucket‑name=name] [‑‑object‑name=name]
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> image export <‑‑id=unique id> <‑‑display‑name=name> [‑‑bucket‑name=name] [‑‑object‑name=name]
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> network setup [‑‑gateway‑os‑name=string] [‑‑gateway‑os‑version=string] [‑‑gateway‑shape=string] [‑‑tunnel‑network‑name=string] [‑‑tunnel‑network‑range=string] [‑‑proxy=string] [‑‑compartment‑id=string]
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> network create <‑‑name=string> <‑‑network‑id=string> [‑‑enable | ‑‑disable]
VBoxManage cloud network update <‑‑name=string> [‑‑network‑id=string] [‑‑enable | ‑‑disable]
VBoxManage cloud network delete <‑‑name=string>
VBoxManage cloud network info <‑‑name=string>
Description
Common options
The word cloud is an umbrella term for all commands related to intercommunication with the Cloud. The following common options must be placed after the cloud argument and before the following sub-commands:

--provider=name
Short cloud provider name.

--profile=name
Cloud profile name.

cloud list instances
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> list instances [‑‑state=string] [‑‑compartment‑id=string]

Displays a list of the cloud instances for a specified compartment.

--state=running|paused|terminated
The state of cloud instance. The possible states are running, paused, and terminated. If a state isn't provided a list of instances with all possible states is returned.

--compartment-id
A compartment is the logical container used to organize and isolate cloud resources. Different cloud providers may use different names for this entity.

cloud list images
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> list images <‑‑compartment‑id=string> [‑‑state=string]

Displays the list of the images for a specified compartment.

--state=available|disabled|deleted
The state of the cloud image. The possible states are available, disabled and deleted. If a state isn't provided a list of images with all possible states is returned.

--compartment-id
A compartment is the logical container used to organize and isolate cloud resources. Different cloud providers may use different names for this entity.

cloud list vnic attachments
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> list vnicattachments <‑‑compartment‑id=string> [‑‑filter=instanceId | vnicId | availabilityDomain=value...]

Displays the list of the vnic attachments for a specified compartment.

--filter={instanceId|vnicId|domainName}=string
Filters are used to narrow down the set of Vnic attachments of interest. This parameter can be specified multiple times. The possible filter types are instanceId, vnicId, or availabilityDomain.

Filters have a syntax of type=[value] as seen in the following examples:

instanceId=ocid1.instance.oc1.iad.anuwcl...js6

vnicId=ocid1.vnic.oc1.iad.abuwcl...jsm

domainName=ergw:US-ASHBURN-AD-2

If a filter isn't provided the entire list of vnic attachments for a specified compartment is returned.

--compartment-id
A compartment is the logical container used to organize and isolate cloud resources. Different cloud providers may use different names for this entity.

cloud instance create
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> instance create <‑‑domain‑name=name> <‑‑image‑id=id | ‑‑boot‑volume‑id=id> <‑‑display‑name=name> <‑‑shape=type> <‑‑subnet=id> [‑‑boot‑disk‑size=size in GB] [‑‑publicip=true | false] [‑‑privateip=IP address] [‑‑public‑ssh‑key=key string...] [‑‑launch‑mode=NATIVE | EMULATED | PARAVIRTUALIZED] [‑‑cloud‑init‑script‑path=path to a script]

Creates new instance in the Cloud. There are two standard ways to create an instance in the Cloud:

Create an instance from an existing custom image.

Create an instance from an existing bootable volume. This bootable volume shouldn't be attached to any instance.

The first approach requires the following two options: image-id, boot-disk-size. The second approach requires the following option: boot-volume-id.

The following options are common to both cases: display-name, launch-mode, subnet-id, publicIP, privateIP, shape, domain.

--domain-name
Cloud domain where the new instance is to be created.

--image-id
Unique identifier which fully identifies a custom image in the Cloud.

--boot-volume-id
Unique identifier which fully identifies a boot volume in the Cloud.

--display-name
Name for the new instance in the Cloud.

--shape
The shape of the instance which defines the number of CPUs and memory (RAM).

--subnet
Unique identifier which fully identifies an existing subnet in the Cloud which will be used by the instance.

--boot-disk-size
The size of the bootable image in GB. Default is 50GB.

--publicip
Public IP address for the created instance.

--privateip
Private IP address for the created instance.

--public-ssh-key
Public SSH key to use to connect to the instance via SSH. This parameter may be repeated if using more than one key: --public-ssh-key=firstSSHKey --public-ssh-key=secondSSHKey.

--launch-mode
Supported values are EMULATED, NATIVE, and PARAVIRTUALIZED.

--cloud-init-script-path
Absolute path to the cloud-init script.

cloud instance info
Display information about a cloud instance with a specified ID.

--id
Unique identifier which fully identifies the instance in the Cloud.

cloud instance termination
Delete a cloud instance with a specified ID.

--id
Unique identifier which fully identifies the instance in the Cloud.

cloud instance start
Start a cloud instance with a specified ID.

--id
Unique identifier which fully identifies the instance in the Cloud.

cloud instance pause
Pause a cloud instance with a specified ID.

--id
Unique identifier which fully identifies the instance in the Cloud.

cloud instance reset
Force reset a cloud instance with a specified ID.

--id
Unique identifier which fully identifies the instance in the Cloud.

cloud instance clone
Clone a cloud instance with the specified ID. Only works for the instances accessible through Oracle VirtualBox, i.e., not every instance in the cloud may be cloned.

--id
Unique identifier which fully identifies the instance in the Cloud.

--clone-name
Name for the clone instance

available list of metrics for cloud instances
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> instance metriclist <‑‑id=unique id>

Displays the list of the available metrics for the instance. The returned names must be used with the command VBoxManage cloud instance metricdata.

--id
Unique identifier which fully identifies the instance in the Cloud.

Displays cloud instance metric data
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> instance metricdata <‑‑id=unique id> <‑‑metric‑name=metric name> <‑‑metric‑points=number of history metric points>

Displays the metric data with the resolution of 1 minute for the requested cloud instances. The timestamps are returned in the format described in RFC2822.

--id
Unique identifier which fully identifies the instance in the Cloud.

--metric-name
Metric name

--metric-points
Metric points begin at the current time, which has a value of one, and continue into the past with the values increasing. If only the most recent metric point is desired supply the value 1. If the most recent value and the preceding value are desired then supply a value of 2.

cloud image create
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> image create <‑‑display‑name=name> [‑‑bucket‑name=name] [‑‑object‑name=name] [‑‑instance‑id=unique id]

Creates new image in the Cloud. There are two standard ways to create an image in the Cloud:

Create an image from an object in the Cloud Storage.

Create an image from an existing cloud instance.

For the first approach the following three options are required: bucket-name, object-name, and display-name.

For the second approach the following two options are required: instance-id, display-name

--display-name
Name for new image in the Cloud.

--bucket-name
The name of the Cloud bucket where the image (object) is located..

--object-name
Name of object in the bucket.

--instance-id
Unique identifier which fully identifies the instance in the Cloud.

cloud image info
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> image info <‑‑id=unique id>

Display information about a cloud image with a specified ID.

--id
Unique identifier which fully identifies the image in the Cloud.

cloud image delete
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> image delete <‑‑id=unique id>

Delete an image with a specified ID from the Cloud.

--id
Unique identifier which fully identifies the image in the Cloud.

cloud image import
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> image import <‑‑id=unique id> [‑‑bucket‑name=name] [‑‑object‑name=name]

Import an image with the specified ID from the Cloud to a local host. The result is an object in the local "temp" folder on the local host. There are two possible approaches when importing from the Cloud:

Create an object from an image in the Cloud Storage.

Download the object to the local host.

--id
Unique identifier which fully identifies the image in the Cloud.

--bucket-name
The name of the Cloud bucket where the object will be created.

--object-name
Name of the created object in the bucket or the name of the downloaded object. If no --object-name option is supplied a default image name is used.

cloud image export
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> image export <‑‑id=unique id> <‑‑display‑name=name> [‑‑bucket‑name=name] [‑‑object‑name=name]

Export an existing VBox image with the specified uuid from a local host to the Cloud. The result is new image in the Cloud. There are two possible approaches when exporting to the Cloud:

Upload a VBox image to the Cloud Storage.

Create an image from the uploaded object.

--id
Unique identifier of the image in Oracle VirtualBox.

--display-name
The name of the new image in the Cloud.

--bucket-name
The name of the Cloud bucket where the image (object) will be uploaded.

--object-name
Name of the object in the bucket.

cloud network setup
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> network setup [‑‑gateway‑os‑name=string] [‑‑gateway‑os‑version=string] [‑‑gateway‑shape=string] [‑‑tunnel‑network‑name=string] [‑‑tunnel‑network‑range=string] [‑‑proxy=string] [‑‑compartment‑id=string]

Set up a cloud network environment for the specified cloud profile.

--gateway-os-name
The name of OS to use for the cloud gateway.

--gateway-os-version
The version of the OS to use for the cloud gateway.

--gateway-shape
The instance shape to use for the cloud gateway.

--tunnel-network-name
The name of the VCN/subnet to use for tunneling.

--tunnel-network-range
The IP address range to use for tunneling.

--proxy
The proxy URL to be used in a local gateway installation.

--compartment-id
The compartment to create the tunnel network in.

cloud network create
VBoxManage cloud <‑‑provider=name> <‑‑profile=name> network create <‑‑name=string> <‑‑network‑id=string> [‑‑enable | ‑‑disable]

Create a new cloud network descriptor associated with an existing cloud subnet.

--name
The name to assign to the cloud network descriptor.

--network-id
The unique identifier of an existing subnet in the cloud.

--enable, --disable
Whether to enable the network descriptor or disable it. If not specified, the network will be enabled.

cloud network update
VBoxManage cloud network update <‑‑name=string> [‑‑network‑id=string] [‑‑enable | ‑‑disable]

Modify an existing cloud network descriptor.

--name
The name of an existing cloud network descriptor.

--network-id
The unique identifier of an existing subnet in the Cloud.

--enable, --disable
Whether to enable the network descriptor or disable it.

cloud network delete
VBoxManage cloud network delete <‑‑name=string>

Delete an existing cloud network descriptor.

--name
The name of an existing cloud network descriptor.

cloud network info
VBoxManage cloud network info <‑‑name=string>

Display information about a cloud network descriptor.

--name
The name of an existing cloud network descriptor.

VBoxManage cloudprofile
Manage the cloud profiles

Synopsis
VBoxManage cloudprofile <‑‑provider=name> <‑‑profile=name>add [‑‑clouduser=unique id] [‑‑fingerprint=MD5 string] [‑‑keyfile=path] [‑‑passphrase=string] [‑‑tenancy=unique id] [‑‑compartment=unique id] [‑‑region=string]
VBoxManage cloudprofile <‑‑provider=name> <‑‑profile=name>update [‑‑clouduser=unique id] [‑‑fingerprint=MD5 string] [‑‑keyfile=path] [‑‑passphrase=string] [‑‑tenancy=unique id] [‑‑compartment=unique id] [‑‑region=string]
VBoxManage cloudprofile <‑‑provider=name> <‑‑profile=name>delete
VBoxManage cloudprofile <‑‑provider=name> <‑‑profile=name>show
Description
Common options
The subcommands of cloudprofile implement the standard Create, Read, Update, and Delete (CRUD) operations for a cloud profile. The following common options must be placed after the cloudprofile argument and before the following sub-commands:

--provider=name
Short cloud provider name.

--profile=name
Cloud profile name.

cloudprofile add
VBoxManage cloudprofile <‑‑provider=name> <‑‑profile=name>add [‑‑clouduser=unique id] [‑‑fingerprint=MD5 string] [‑‑keyfile=path] [‑‑passphrase=string] [‑‑tenancy=unique id] [‑‑compartment=unique id] [‑‑region=string]

Add a new cloud profile for the specified cloud provider.

--clouduser=unique id
The name which fully identifies the user in the specified cloud provider.

--fingerprint=MD5 string
Fingerprint of the key pair being used.

--keyfile=path
Full path and filename of the private key.

--passphrase=string
Passphrase used for the key if it is encrypted.

--tenancy=unique id
ID of your tenancy.

--compartment=unique id
ID of your compartment.

--region=string
Region name. Region is where you plan to deploy an application.

cloudprofile show
VBoxManage cloudprofile <‑‑provider=name> <‑‑profile=name>show

Display information about a cloud profile for the specified cloud provider.

cloudprofile update
VBoxManage cloudprofile <‑‑provider=name> <‑‑profile=name>update [‑‑clouduser=unique id] [‑‑fingerprint=MD5 string] [‑‑keyfile=path] [‑‑passphrase=string] [‑‑tenancy=unique id] [‑‑compartment=unique id] [‑‑region=string]

Modify a cloud profile for the specified cloud provider.

--clouduser=unique id
The name which fully identifies the user in the specified cloud provider.

--fingerprint=MD5 string
Fingerprint for the key pair being used.

--keyfile=path
Full path and filename of the private key.

--passphrase=string
Passphrase used for the key if it is encrypted.

--tenancy=unique id
ID of the tenancy.

--compartment=unique id
ID of the compartment.

--region=string
Region name. Region is where you plan to deploy an application.

cloudprofile delete
VBoxManage cloudprofile <‑‑provider=name> <‑‑profile=name>delete

Delete a cloud profile for a specified cloud provider.

VBoxManage controlvm
Change state and settings for a running virtual machine

Synopsis
VBoxManage controlvm <uuid | vmname>pause
VBoxManage controlvm <uuid | vmname>resume
VBoxManage controlvm <uuid | vmname>reset
VBoxManage controlvm <uuid | vmname>poweroff
VBoxManage controlvm <uuid | vmname>savestate
VBoxManage controlvm <uuid | vmname>acpipowerbutton
VBoxManage controlvm <uuid | vmname>acpisleepbutton
VBoxManage controlvm <uuid | vmname>reboot
VBoxManage controlvm <uuid | vmname>shutdown [‑‑force]
VBoxManage controlvm <uuid | vmname>keyboardputscancode <hex> [hex...]
VBoxManage controlvm <uuid | vmname>keyboardputstring <string> [string...]
VBoxManage controlvm <uuid | vmname>keyboardputfile <filename>
VBoxManage controlvm <uuid | vmname>setlinkstateN <on | off>
VBoxManage controlvm <uuid | vmname>nicN <null | nat | bridged | intnet | hostonly | generic | natnetwork> [device‑name]
VBoxManage controlvm <uuid | vmname>nictraceN <on | off>
VBoxManage controlvm <uuid | vmname>nictracefileN <filename>
VBoxManage controlvm <uuid | vmname>nicpropertyN <prop‑name=prop‑value>
VBoxManage controlvm <uuid | vmname>nicpromiscN <deny | allow‑vms | allow‑all>
VBoxManage controlvm <uuid | vmname>natpfN <[rulename],<tcp|udp>,[host‑IP],hostport,[guest‑IP],guestport>
VBoxManage controlvm <uuid | vmname>natpfNdelete <rulename>
VBoxManage controlvm <uuid | vmname>guestmemoryballoon <balloon‑size>
VBoxManage controlvm <uuid | vmname>usbattach <uuid | address> [‑‑capturefile=filename]
VBoxManage controlvm <uuid | vmname>usbdetach <uuid | address>
VBoxManage controlvm <uuid | vmname>audioin <on | off>
VBoxManage controlvm <uuid | vmname>audioout <on | off>
VBoxManage controlvm <uuid | vmname>clipboard mode <disabled | hosttoguest | guesttohost | bidirectional>
VBoxManage controlvm <uuid | vmname>clipboard filetransfers <on | off>
VBoxManage controlvm <uuid | vmname>draganddrop <disabled | hosttoguest | guesttohost | bidirectional>
VBoxManage controlvm <uuid | vmname>vrde <on | off>
VBoxManage controlvm <uuid | vmname>vrdeport <port>
VBoxManage controlvm <uuid | vmname>vrdeproperty <prop‑name=prop‑value>
VBoxManage controlvm <uuid | vmname>vrdevideochannelquality <percentage>
VBoxManage controlvm <uuid | vmname>setvideomodehint <xres> <yres> <bpp> [display [<yes | no> [x‑originy‑origin]]]
VBoxManage controlvm <uuid | vmname>setscreenlayout <display> <on | primaryx‑originy‑originx‑resolutiony‑resolutionbpp | off>
VBoxManage controlvm <uuid | vmname>screenshotpng <filename> [display]
VBoxManage controlvm <uuid | vmname>recording <on | off> VBoxManage controlvm <uuid | vmname>recording start [‑‑wait] VBoxManage controlvm <uuid | vmname>recording stop VBoxManage controlvm <uuid | vmname>recording attach VBoxManage controlvm <uuid | vmname>recording screens <all | none | screen‑ID,screen‑ID...> VBoxManage controlvm <uuid | vmname>recording filename <filename> VBoxManage controlvm <uuid | vmname>recording videores <<width>x <height>> VBoxManage controlvm <uuid | vmname>recording videorate <rate> VBoxManage controlvm <uuid | vmname>recording videofps <fps> VBoxManage controlvm <uuid | vmname>recording maxtime <sec> VBoxManage controlvm <uuid | vmname>recording maxfilesize <MB> VBoxManage controlvm <uuid | vmname>recording opts <key= [value]>
VBoxManage controlvm <uuid | vmname>setcredentials <username>‑‑passwordfile=<filename | password> <domain‑name>‑‑allowlocallogon=<yes | no>
VBoxManage controlvm <uuid | vmname>teleport <‑‑host=host‑name> <‑‑port=port‑name> [‑‑maxdowntime=msec] [‑‑passwordfile=filename | ‑‑password=password]
VBoxManage controlvm <uuid | vmname>plugcpu <ID>
VBoxManage controlvm <uuid | vmname>unplugcpu <ID>
VBoxManage controlvm <uuid | vmname>cpuexecutioncap <num>
VBoxManage controlvm <uuid | vmname>vm‑process‑priority <default | flat | low | normal | high>
VBoxManage controlvm <uuid | vmname>webcam attach [pathname [settings]]
VBoxManage controlvm <uuid | vmname>webcam detach [pathname]
VBoxManage controlvm <uuid | vmname>webcam list
VBoxManage controlvm <uuid | vmname>addencpassword <ID> <password‑file | ‑> [‑‑removeonsuspend=yes | no]
VBoxManage controlvm <uuid | vmname>removeencpassword <ID>
VBoxManage controlvm <uuid | vmname>removeallencpasswords
VBoxManage controlvm <uuid | vmname>changeuartmodeNdisconnected | serverpipe‑name | clientpipe‑name | tcpserverport | tcpclienthostname:port | filefilename | device‑name
VBoxManage controlvm <uuid | vmname>autostart‑enabledNon | off
VBoxManage controlvm <uuid | vmname>autostart‑delay <seconds>
Description
The VBoxManage controlvm command enables you to change the state of a running virtual machine (VM). The following sections describe the subcommands that you can use:

Pause a Virtual Machine
VBoxManage controlvm <uuid | vmname>pause

The VBoxManage controlvm vmname pause command temporarily stops the execution of a VM. When paused, the VM's state is not permanently changed.

The VM window appears as gray and the title bar of the window indicates that the VM is currently Paused. This action is equivalent to selecting Pause from the Machine menu of the GUI.

Resume a Paused Virtual Machine
VBoxManage controlvm <uuid | vmname>resume

The VBoxManage controlvm vmname resume command restarts the execution of a paused VM. This action is equivalent to selecting Resume from the Machine menu of the GUI.

Reset a Virtual Machine
VBoxManage controlvm <uuid | vmname>reset

The VBoxManage controlvm vmname reset command performs a cold reset the VM. This command has the same effect on a VM as pressing the Reset button on a physical computer.

A cold reboot immediately restarts and reboots the guest operating system (OS). The state of the VM is not saved prior to the reset, so data might be lost. This action is equivalent to selecting Reset from the Machine menu of the GUI.

Power Off a Virtual Machine
VBoxManage controlvm <uuid | vmname>poweroff

The VBoxManage controlvm vmname poweroff command powers off the VM. This command has the same effect on a VM as pulling the power cable on a physical computer.

The state of the VM is not saved prior to poweroff, so data might be lost. This action is equivalent to selecting Close from the Machine menu of the GUI or to clicking the VM window's Close button, and then selecting Power Off the Machine.

The poweroff subcommand places a VM into the Powered Off state from which you can restart the VM using VBoxManage startvm.

Save the State of a Virtual Machine
VBoxManage controlvm <uuid | vmname>savestate

The VBoxManage controlvm vmname savestate command saves the current state of the VM to disk and then stops the VM.

This action is equivalent to selecting Close from the Machine menu of the GUI or to clicking the VM window's Close button, and then selecting Save the Machine State.

The savestate subcommand places a VM into the Saved state from which you can restart the VM using VBoxManage startvm. The VM will resume from the exact point at which it was saved.

Send an APCI Shutdown Signal to a Virtual Machine
VBoxManage controlvm <uuid | vmname>acpipowerbutton

The VBoxManage controlvm vmname acpipowerbutton command sends an ACPI shutdown signal to the VM. This command has the same effect on a VM as pressing the Power button on a physical computer.

So long as the VM runs a guest OS that provides appropriately configured ACPI support, this command triggers an operating system shutdown from within the VM.

Send an APCI Sleep Signal to a Virtual Machine
VBoxManage controlvm <uuid | vmname>acpisleepbutton

The VBoxManage controlvm vmname acpisleepbutton command sends an ACPI sleep signal to the VM.

So long as the VM runs a guest OS that provides appropriately configured ACPI support, this command triggers a sleep mechanism from within the VM.

Reboot the guest OS
VBoxManage controlvm <uuid | vmname>reboot

The VBoxManage controlvm vmname reboot command asks the guest OS to reboot itself.

This command requires the Oracle VirtualBox Guest Additions to be installed in the VM.

Shut down the guest OS
VBoxManage controlvm <uuid | vmname>shutdown [‑‑force]

The VBoxManage controlvm vmname shutdown command asks the guest OS to halt + shutdown, optionally forcing the shutdown.

This command requires the Oracle VirtualBox Guest Additions to be installed in the VM.

Send Keyboard Scancodes to a Virtual Machine
VBoxManage controlvm <uuid | vmname>keyboardputscancode <hex> [hex...]

The VBoxManage controlvm vmname keyboardputscancode command sends keyboard scancode commands to the VM.

For information about keyboard scancodes, see http://www.win.tue.nl/~aeb/linux/kbd/scancodes-1.html.

Send Keyboard Strings to a Virtual Machine
VBoxManage controlvm <uuid | vmname>keyboardputstring <string> [string...]

The VBoxManage controlvm vmname keyboardputstring command sends keyboard strings to the VM.

Send a File to a Virtual Machine
VBoxManage controlvm <uuid | vmname>keyboardputfile <filename>

The VBoxManage controlvm vmname keyboardputfile command sends a file to the VM.

Set the Link State for a Virtual Machine
VBoxManage controlvm <uuid | vmname>setlinkstateN <on | off>

VBoxManage controlvm vmname setlinkstateN command enables you to connect or disconnect the virtual network cable from the network interface instance (N). Valid values are on and off. The default value is on.

Set the Type of Networking to Use for a Virtual Machine
VBoxManage controlvm <uuid | vmname>nicN <null | nat | bridged | intnet | hostonly | generic | natnetwork> [device‑name]

The VBoxManage controlvm vmname nicN command specifies the type of networking to use on the specified VM's virtual network card. N numbering begins with 1.

The following valid network types are also described in Introduction to Networking Modes:

null specifies that the VM is is not connected to the host system.

nat specifies that the VM uses Network Address Translation (NAT).

bridged specifies that the VM uses bridged networking.

intnet specifies that the VM communicates with other VMs by using internal networking.

hostonly specifies that the VM uses host-only networking.

natnetwork specifies that the VM uses NAT networking.

generic specifies that the VM has access to rarely used submodes

Trace the Network Traffic of a Virtual Machine
VBoxManage controlvm <uuid | vmname>nictraceN <on | off>

The VBoxManage controlvm vmname nictraceN command enables you to trace the network traffic on the specified virtual network card (N). N numbering begins with 1. Valid values are on and off. The default value is off.

If you do not configure a file name for the trace file then a default one is used, placing it in the VM subdirectory.

Specify the Network Traffic Trace Log File for a Virtual Machine
VBoxManage controlvm <uuid | vmname>nictracefileN <filename>

The VBoxManage controlvm vmname nictracefileN command enables you to specify the name of the network traffic trace log file for the specified virtual network card (N). N numbering begins with 1.

Specify the Promiscuous Mode to Use for a Virtual Machine
VBoxManage controlvm <uuid | vmname>nicpromiscN <deny | allow‑vms | allow‑all>

The VBoxManage controlvm vmname nicpromiscN command enables you to specify how to handle promiscuous mode for a bridged network. The default value of deny hides any traffic that is not intended for this VM. The allow-vms value hides all host traffic from this VM but enables the VM to see traffic to and from other VMs. The allow-all value removes this restriction completely.

Specify the Network Backend Property Values for a Virtual Machine
VBoxManage controlvm <uuid | vmname>nicpropertyN <prop‑name=prop‑value>

The VBoxManage controlvm vmname nicpropertyN prop-name=prop-value command, in combination with nicgenericdrv, enables you to pass property values to rarely-used network backends.

Those properties are backend engine-specific and are different between UDP Tunnel and the Virtual Distributed Ethernet (VDE) backend drivers. See UDP Tunnel Networking.

Specify a NAT Port Forwarding Rule for a Virtual Machine
VBoxManage controlvm <uuid | vmname>natpfN <[rulename],<tcp|udp>,[host‑IP],hostport,[guest‑IP],guestport>

The VBoxManage controlvm vmname natpfN command specifies a NAT port-forwarding rule. See Configuring Port Forwarding with NAT.

Delete a NAT Port Forwarding Rule for a Virtual Machine
VBoxManage controlvm <uuid | vmname>natpfNdelete <rulename>

The VBoxManage controlvm vmname natpfN delete command deletes the specified NAT port-forwarding rule. See Configuring Port Forwarding with NAT.

Change Size of a Virtual Machine's Guest Memory Balloon
VBoxManage controlvm <uuid | vmname>guestmemoryballoon <balloon‑size>

The VBoxManage controlvm vmname guestmemoryballoon command changes the size of the guest memory balloon. The guest memory balloon is the memory allocated by the Oracle VirtualBox Guest Additions from the guest OS and returned to the hypervisor for reuse by other VMs. The value you specify is in megabytes. See Memory Ballooning.

Make a Host System USB Device Visible to a Virtual Machine
VBoxManage controlvm <uuid | vmname>usbattach <uuid | address> [‑‑capturefile=filename]

The VBoxManage controlvm vmname usbattach command dynamically attaches a host USB device to the VM, which makes it visible. You do not need to create a filter.

Specify a USB device by its Universally Unique Identifier (UUID) or by its address on the host system. Use the VBoxManage list usbhost command to obtain information about USB devices on the host system.

Use the --capturefile option to specify the absolute path of a file in which to write logging data.

Make a Host System USB Device Invisible to a Virtual Machine
VBoxManage controlvm <uuid | vmname>usbdetach <uuid | address>

The VBoxManage controlvm vmname usbdetach command dynamically detaches a host USB device from the VM, which makes it invisible. You do not need to create a filter.

Specify a USB device by its UUID or by its address on the host system. Use the VBoxManage list usbhost command to obtain information about USB devices on the host system.

Enable or Disable Audio Capture From the Host System
VBoxManage controlvm <uuid | vmname>audioin <on | off>

The VBoxManage controlvm vmname audioin command specifies whether to enable or disable audio capture from the host system. Valid values are on, which enables audio capture and off, which disables audio capture. The default value is off.

Enable or Disable Audio Playback From a Virtual Machine
VBoxManage controlvm <uuid | vmname>audioout <on | off>

The VBoxManage controlvm vmname audioout command specifies whether to enable or disable audio playback from the guest VM. Valid values are on, which enables audio playback and off, which disables audio playback. The default value is off.

Specify How to Share the Host OS or Guest OS Clipboard
VBoxManage controlvm <uuid | vmname>clipboard mode <disabled | hosttoguest | guesttohost | bidirectional>

The VBoxManage controlvm vmname clipboard mode command specifies how to share the guest or host OS's clipboard with the host system or VM. Valid values are disabled, hosttoguest, guesttohost, and bidirectional. The default value is disabled. See General Settings.

This feature requires the Oracle VirtualBox Guest Additions to be installed in the VM.

Specify If Files Can Be Transferred Through the Clipboard
VBoxManage controlvm <uuid | vmname>clipboard filetransfers <on | off>

The VBoxManage controlvm vmname clipboard filetransfers command specifies if it is possible to transfer files through the clipboard between the host and VM in the direction(s) configured for the clipboard mode. Valid values are off and on. The default value is off.

This feature requires the Oracle VirtualBox Guest Additions to be installed in the VM.

Set the Drag and Drop Mode Between the Host System and a Virtual Machine
VBoxManage controlvm <uuid | vmname>draganddrop <disabled | hosttoguest | guesttohost | bidirectional>

The VBoxManage controlvm vmname draganddrop command specifies the current drag and drop mode to use between the host system and the VM. Valid values are disabled, hosttoguest, guesttohost, and bidirectional. The default value is disabled. See Drag and Drop.

This feature requires the Oracle VirtualBox Guest Additions to be installed in the VM.

Enable or Disable the VRDE Server
VBoxManage controlvm <uuid | vmname>vrde <on | off>

The VBoxManage controlvm vmname vrde command enables or disables the VirtualBox Remote Desktop Extension (VRDE) server, if installed. Valid values are on and off. The default value is off.

Specify VRDE Server Ports
VBoxManage controlvm <uuid | vmname>vrdeport <port>

The VBoxManage controlvm vmname vrdeport command specifies the port or range of ports to which the VRDE server can bind. The default value is default or 0, which uses the standard RDP port, 3389.

See also the --vrde-port option description in VBoxManage modifyvm.

Specify VRDE Server Port Numbers and IP Addresses
VBoxManage controlvm <uuid | vmname>vrdeproperty <prop‑name=prop‑value>

The VBoxManage controlvm vmname vrdeproperty command specifies the port numbers and IP address on the VM to which the VRDE server can bind.

TCP/Ports specifies a port or a range of ports to which the VRDE server can bind. The default value is default or 0, which is the standard RDP port, 3389.

See also the --vrde-port option description in VBoxManage modifyvm.

TCP/Address specifies the IP address of the host network interface to which the VRDE server binds. When specified, the server accepts connections only on the specified host network interface.

See also the --vrde-address option description in VBoxManage modifyvm.

VideoChannel/Enabled specifies whether to enable the VirtualBox Remote Desktop Protocol (VRDP) video channel. Valid values are 1 to enable the video channel and 0 to disable the video channel. The default value is 0. See VRDP Video Redirection.

VideoChannel/Quality specifies the JPEG compression level on the VRDE server video channel. Valid values are between 10% and 100%, inclusive. Lower values mean lower quality but higher compression. The default value is 100. See VRDP Video Redirection.

VideoChannel/DownscaleProtection specifies whether to enable the video channel downscale protection feature. Specify 1 to enable the feature. This feature is disabled by default.

When enabled, if the video's size equals the shadow buffer size, the video is shown in full-screen mode. If the video's size is between full-screen mode and the downscale threshold, the video is not shown as it might be an application window that is unreadable when downscaled. When disabled, the downscale protection feature always attempts to show videos.

Client/DisableDisplay specifies whether to disable the VRDE server display feature. Valid values are 1 to disable the feature and an empty string ("") to enable the feature. The default value is an empty string. See VRDP Customization.

Client/DisableInput specifies whether to disable the VRDE server input feature. Valid values are 1 to disable the feature and an empty string ("") to enable the feature. The default value is 1. See VRDP Customization.

Client/DisableAudio specifies whether to disable the VRDE server audio feature. Valid values are 1 to disable the feature and an empty string ("") to enable the feature. The default value is 1. See VRDP Customization.

Client/DisableUSB specifies whether to disable the VRDE server USB feature. Valid values are 1 to disable the feature and an empty string ("") to enable the feature. The default value is 1. See VRDP Customization.

Client/DisableClipboard specifies whether to disable the VRDE clipboard feature. Valid values are 1 to disable the feature and an empty string ("") to enable the feature. To re-enable the feature, use Client/DisableClipboard=. The default value is 1. See VRDP Customization.

Client/DisableUpstreamAudio specifies whether to disable the VRDE upstream audio feature. Valid values are 1 to disable the feature and an empty string ("") to enable the feature. To re-enable the feature, use Client/DisableUpstreamAudio=. The default value is 1. See VRDP Customization.

Client/DisableRDPDR specifies whether to disable the RDP Device Redirection For Smart Cards feature on the VRDE server. Valid values are 1 to disable the feature and an empty string ("") to enable the feature. The default value is 1. See VRDP Customization.

H3DRedirect/Enabled specifies whether to enable the VRDE server 3D redirection feature. Valid values are 1 to enable the feature and an empty string ("") to disable the feature. See VRDP Customization.

Security/Method specifies the security method to use for a connection. See RDP Encryption.

Negotiate accepts both enhanced (TLS) and standard RDP security connections. The security method is negotiated with the client. This is the default value.

RDP accepts only standard RDP security connections.

TLS accepts only enhanced RDP security connections. The client must support TLS.

Security/ServerCertificate specifies the absolute path of the server certificate to use for a connection. See RDP Encryption.

Security/ServerPrivateKey specifies the absolute path of the server private key. See RDP Encryption.

Security/CACertificate specifies the absolute path of the CA self-signed certificate. See RDP Encryption.

Audio/RateCorrectionMode specifies the rate correction mode to use.

VRDP_AUDIO_MODE_VOID indicates that no mode is specified. Use this value to unset any audio mode that is already set.

VRDP_AUDIO_MODE_RC specifies to use the rate correction mode.

VRDP_AUDIO_MODE_LPF specifies to use the low pass filter mode.

VRDP_AUDIO_MODE_CS specifies to use the client sync mode to prevent underflow or overflow of the client queue.

Audio/LogPath specifies the absolute path of the audio log file.

Specify the Image Quality for VRDP Video Redirection
VBoxManage controlvm <uuid | vmname>vrdevideochannelquality <percentage>

The VBoxManage controlvm vmname vrdevideochannelquality command sets the image quality, as a JPEG compression level value, for video redirection. Valid values are between 10% and 100%, inclusive. Lower values mean lower quality but higher compression. See VRDP Video Redirection.

Specify the Video Mode for the Guest VM
VBoxManage controlvm <uuid | vmname>setvideomodehint <xres> <yres> <bpp> [display [<yes | no> [x‑originy‑origin]]]

The VBoxManage controlvm vmname setvideomodehint command specifies the video mode for the guest VM to use. You must have the Oracle VirtualBox Guest Additions installed. Note that this feature does not work for all guest systems.

Specify the Screen Layout for a Display on the Guest VM
VBoxManage controlvm <uuid | vmname>setscreenlayout <display> <on | primaryx‑originy‑originx‑resolutiony‑resolutionbpp | off>

The VBoxManage controlvm vmname setscreenlayout command can be used to configure multiscreen displays. The specified screen on the guest VM can be enabled or disabled, or a custom screen layout can be configured.

Take a Screen Shot of the Virtual Machine Display
VBoxManage controlvm <uuid | vmname>screenshotpng <filename> [display]

The VBoxManage controlvm vmname screenshotpng command takes a screenshot of the guest display and saves it as PNG in the specified file.

filename specifies the name of the PNG file to create.

display specifies the display number for the screen shot. For a single monitor guest display, this is 0.

Recording of a Virtual Machine Session
VBoxManage controlvm <uuid | vmname>recording <on | off> VBoxManage controlvm <uuid | vmname>recording start [‑‑wait] VBoxManage controlvm <uuid | vmname>recording stop VBoxManage controlvm <uuid | vmname>recording attach VBoxManage controlvm <uuid | vmname>recording screens <all | none | screen‑ID,screen‑ID...> VBoxManage controlvm <uuid | vmname>recording filename <filename> VBoxManage controlvm <uuid | vmname>recording videores <<width>x <height>> VBoxManage controlvm <uuid | vmname>recording videorate <rate> VBoxManage controlvm <uuid | vmname>recording videofps <fps> VBoxManage controlvm <uuid | vmname>recording maxtime <sec> VBoxManage controlvm <uuid | vmname>recording maxfilesize <MB> VBoxManage controlvm <uuid | vmname>recording opts <key= [value]>

The VBoxManage controlvm vmname recording command enables or disables the recording of a VM session into a WebM/VP8 file. Valid values are on, which begins recording when the VM session starts and off, which disables recording. The default value is off.

The VBoxManage controlvm vmname recording start command starts the recording of a VM session into a WebM/VP8 file.

The VBoxManage controlvm vmname recording stop command stops the recording of a VM session.

The VBoxManage controlvm vmname recording attach command attaches to a running recording of a VM session.

The VBoxManage controlvm vmname recording screens command enables you to specify which VM screens to record. The recording for each screen that you specify is saved to its own file in the machine folder. You cannot modify this setting while a screen recording is in progress.

all specifies that you record all VM screens.

none specifies that you do not record any VM screens.

screen-ID specifies one or more VM screens to record.

The VBoxManage controlvm vmname recording filename command specifies the file in which to save the recording. You cannot modify this setting while a screen recording is in progress.

The default setting is to store a recording in the machine folder, using the VM name as the file name, with a webm file name extension.

The VBoxManage controlvm vmname recording videores command specifies the resolution of the recorded video in pixels. You cannot modify this setting while a screen recording is in progress.

The Recording settings in the GUI which are located in the Display page show the current video recording settings which are based on the resolution (frame size). The Frame Size field shows the default value for the recording video resolution.

Specify the resolution as widthxheight:

width specifies the width in pixels.

height specifies the height in pixels.

The VBoxManage controlvm vmname recording videorate command specifies the bit-rate, of the video in kilobits per second. Increasing this value improves the appearance of the video at the cost of an increased file size. You cannot modify this setting while a VM is running with recording enabled.

The Recording settings in the GUI which are located in the Display page show the current video recording settings which are based on the resolution (frame size). The Video Quality field shows the default value for the recording video quality.

The VBoxManage controlvm vmname recording videofps command specifies the maximum frequency of the video to record. Video frequency is measured in frames per second (FPS). The recording skips any frames that have a frequency higher than the specified maximum. Increasing the frequency reduces the number of skipped frames and increases the file size. You cannot modify this setting while a VM is running with recording enabled.

The Recording settings in the GUI which are located in the Display page show the current video recording settings which are based on the resolution (frame size). The Frame Rate field shows the default value for the recording frame rate.

The VBoxManage controlvm vmname recording maxtime command specifies the maximum amount time to record in seconds. The recording stops after the specified number of seconds elapses. If this value is zero, the recording continues until you stop the VM or stop the recording.

The VBoxManage controlvm vmname recording maxfilesize command specifies the maximum size of the recorded video file in megabytes. The recording stops when the file reaches the specified size. If this value is zero, the recording continues until you stop the VM or stop the recording. You cannot modify this setting while a screen recording is in progress.

The VBoxManage controlvm vmname recording opts command specifies additional recording options in a comma-separated keyword-value format. For example, foo=bar,a=b. You cannot modify this setting while a screen recording is in progress.

Use this option if you are an advanced user only. For information about keywords, see Oracle VirtualBox Programming Guide and Reference.

Specify Credentials for Remote Logins on Windows Virtual Machines
VBoxManage controlvm <uuid | vmname>setcredentials <username>‑‑passwordfile=<filename | password> <domain‑name>‑‑allowlocallogon=<yes | no>

The setcredentials command enables you to specify the credentials for remotely logging in to Windows VMs. See Automated Guest Logins.

username specifies the user name with which to log in to the Windows VM.

--passwordfile=filename specifies the file from which to obtain the password for username.

The --passwordfile option is mutually exclusive with the --password option.

--password=password specifies the password for username.

The --password option is mutually exclusive with the --passwordfile option.

--allowlocallogon specifies whether to enable or disable local logins. Valid values are on to enable local logins and off to disable local logins.

Configure a Virtual Machine Target for Teleporting
VBoxManage controlvm <uuid | vmname>teleport <‑‑host=host‑name> <‑‑port=port‑name> [‑‑maxdowntime=msec] [‑‑passwordfile=filename | ‑‑password=password]

The VBoxManage controlvm vmname teleport command initiates a teleporting operation between the specified VM and the specified host system. See Teleporting.

If you specify a password, it must match the password you specified when you issued the VBoxManage modifyvm command for the target machine.

--host=hostname
Specifies the name of the VM.

--port=port
Specifies the port on the VM that should listen for a teleporting request from other VMs. The port number can be any free TCP/IP port number, such as 6000.

--maxdowntime=msec
Specifies the maximum downtime, in milliseconds, for the teleporting target VM.

--password=password
Specifies the password that the source machine uses for the teleporting request. The request succeeds only if the source machine specifies the same password.

The --password option is mutually exclusive with the --passwordfile option.

--passwordfile=filename
Specifies the file from which to obtain the password that the source machine uses for the teleporting request. The request succeeds only if the source machine specifies the same password.

When you specify a file name of stdin, you can read the password from standard input.

The --passwordfile option is mutually exclusive with the --password option.

Add a Virtual CPU to a Virtual Machine
VBoxManage controlvm <uuid | vmname>plugcpu <ID>

The VBoxManage controlvm vmname plugcpu command adds a virtual CPU to the specified VM if CPU hot-plugging is enabled. ID specifies the index of the virtual CPU to be added and must be a number from 0 to the maximum number of CPUs configured.

Remove a Virtual CPU From a Virtual Machine
VBoxManage controlvm <uuid | vmname>unplugcpu <ID>

The VBoxManage controlvm vmname unplugcpu command removes a virtual CPU from the specified VM if CPU hot-plugging is enabled. ID specifies the index of the virtual CPU to be removed and must be a number from 0 to the maximum number of CPUs configured. You cannot remove CPU 0.

Set the Maximum Amount of Physical CPU Time Used by a Virtual CPU
VBoxManage controlvm <uuid | vmname>cpuexecutioncap <num>

The VBoxManage controlvm vmname cpuexecutioncap command specifies the maximum amount of physical CPU time used by a virtual CPU. Valid values are a percentage between 1 and 100. A value of 50 specifies that a single virtual CPU can use up to 50% of a physical CPU. The default value is 100.

Use this feature with caution, it can have unexpected results including timekeeping problems and lower performance than specified. If you want to limit the resource usage of a VM it is more reliable to pick an appropriate number of VCPUs.

Change the Priority of a VM Process
VBoxManage controlvm <uuid | vmname>vm‑process‑priority <default | flat | low | normal | high>

The VBoxManage controlvm vmname vm-process-priority command specifies the priority scheme of the VM process to use when starting the specified VM and while the VM runs.

Valid values are:

default – Default process priority determined by the OS.

flat – Uses a scheduling policy which puts the process at the default priority and with all threads at the same priority.

low – Uses a scheduling policy which puts the process mostly below the default priority of the host OS.

normal – Uses a scheduling policy which shares the CPU resources fairly with other processes running with the default priority of the host OS.

high – Uses a scheduling policy which puts the task above the default priority of the host OS. This policy might easily cause other tasks in the system to starve.

Attach a Webcam to a Virtual Machine
VBoxManage controlvm <uuid | vmname>webcam attach [pathname [settings]]

The VBoxManage controlvm vmname webcam attach command attaches a webcam to a running VM. Specify the webcam as the absolute path of the webcam on the host OS or as an alias. Use the VBoxManage list webcams command to obtain the webcam alias.

Note that the .0 alias is the default video input device on the host OS. .1 is the first video input device, .2 is the second video input device, and so on. The order of the devices is specific to the host system.

You can specify optional settings in the form of semicolon-separated (;) name-value pairs. These properties enable you to configure the emulated webcam device.

The following settings are supported:

MaxFramerate
Specifies the highest rate at which to send video frames to the VM. The rate is in frames per second. Higher frame rates increase CPU load, so you can use this setting to reduce CPU load. The default value is no maximum limit. This value enables the VM to use any frame rate supported by the webcam.

MaxPayloadTransferSize
Specifies the maximum number of bytes that the VM receives from the emulated webcam in one buffer. The default setting is 3060 bytes, which is used by some webcams. If the VM is able to use larger buffers, higher values might reduce CPU load slightly. Note that some guest OSes might not support higher MaxPayloadTransferSize values.

Detach a Webcam From a Virtual Machine
VBoxManage controlvm <uuid | vmname>webcam detach [pathname]

The VBoxManage controlvm vmname webcam detach command detaches a webcam from a running VM. Specify the webcam as the absolute path of the webcam on the host OS or as an alias. Use the VBoxManage list webcams command to obtain the webcam alias.

When a webcam device is detached from the host, the host OS determines how the emulated webcam behaves.

Windows hosts: The emulated webcam device is detached from the VM automatically.

Mac OS X hosts that run at least OS X 10.7: The emulated webcam device remains attached to the VM and you must detach it manually by using the VBoxManage controlvm webcam detach command.

Linux hosts: The emulated webcam device is detached from the VM automatically only if the webcam is actively streaming video. If the emulated webcam is inactive, manually detach it by using the VBoxManage controlvm vmname webcam detach command.

List the Webcams Attached to a Virtual Machine
VBoxManage controlvm <uuid | vmname>webcam list

The VBoxManage controlvm vmname webcam list command lists webcams that are attached to the running VM. The output shows a list of absolute paths or aliases of the webcams which were attached to the VM using the VBoxManage controlvm vmname webcam attach command.

Starting a Virtual Machine with Encrypted Images
VBoxManage controlvm <uuid | vmname>addencpassword <ID> <password‑file | ‑> [‑‑removeonsuspend=yes | no]

You can encrypt the data stored in the hard disk images used by the VM. Oracle VirtualBox uses the AES algorithm in XTS mode and supports 128-bit or 256-bit data encryption keys (DEK). The encrypted DEK is stored in the medium properties and is decrypted during VM startup when you provide the encryption password.

Use the VBoxManage encryptmedium command to create a DEK encrypted medium. See Encrypting Disk Images.

When a VM which contains one or more encrypted disk images is started using the Oracle VirtualBox GUI a dialog will open to prompt the user for the password of each encrypted disk attached to the VM. If the VBoxHeadless frontend is used, the VM will be paused as soon as the guest tries to access an encrypted disk. The user needs to provide the passwords by using addencpassword subcommand.

The VBoxManage controlvm vmname addencpassword command provides the encryption password to the VM named vmname which contains encrypted disks that has been started using the VBoxHeadless frontend. Specify the absolute path of a password file on the host system. If filename is -, VBoxManage prompts for the encryption password.

Use the --removeonsuspend option to specify whether to save the passsword or clear it from VM memory when the VM is suspended.

If the VM is suspended and the password is cleared, use the VBoxManage controlvm vmname addencpassword to provide the password to resume execution on the VM. Use this feature when you do not want to store the password in VM memory when the VM is suspended by a host suspend event.

The following is one way to perform a headless start of VM which contains encrypted disk images:

          $ VBoxManage startvm vmname --type headless
        
The following command provides the encryption password to the VM so that it can resume starting up:

          $ VBoxManage vmname controlvm addencpassword vmname -
          Password: encryption-password
        
Disable an Encryption Password for a Virtual Machine
VBoxManage controlvm <uuid | vmname>removeencpassword <ID>

The VBoxManage controlvm vmname removeencpassword command disables a specific encryption password for all encrypted media attached to the VM.

ID is the password identifier for the encryption password that you want to disable.

Disable All Encryption Passwords for a Virtual Machine
VBoxManage controlvm <uuid | vmname>removeallencpasswords

The VBoxManage controlvm vmname removeallencpasswords command disables all encryption passwords for all encrypted media attached to the VM.

Change the Connection Mode for a Virtual Serial Port on a Virtual Machine
VBoxManage controlvm <uuid | vmname>changeuartmodeNdisconnected | serverpipe‑name | clientpipe‑name | tcpserverport | tcpclienthostname:port | filefilename | device‑name

The VBoxManage controlvm vmname changeuartmode command changes the connection mode for the specified virtual serial port. Valid serial port values are integers that start from 1.

disconnected
Disconnects the device.

server pipe-name
Specifies the pipe name of the server.

client pipe-name
Specifies the pipe name of the client.

tcpserver port
Specifies the port number of the TCP server.

tcpclient hostname:port
Specifies the host name and port number of the TCP client.

file filename
Specifies the name of the file.

device-name
Specifies the name of the device.

Enabling autostart the VM during host system boot
VBoxManage controlvm <uuid | vmname>autostart‑enabledNon | off

The VBoxManage controlvm vmname autostart-enabled command specifies whether to enable or disable automatically starting of the VM at host system boot-up. You must do some host system configuration before you can use this feature. See Starting Virtual Machines During System Boot. Valid values are on, which enables the autostart feature for the VM and off, which disables it. The default value is off.

Setting the delay of starting the VM on host system boot
VBoxManage controlvm <uuid | vmname>autostart‑delay <seconds>

The VBoxManage controlvm vmname autostart-delay command specifies the delay in seconds before the VM starts on host system boot-up. See Starting Virtual Machines During System Boot.

Examples
The following command temporarily stops the execution of the ol7 VM.

$ VBoxManage controlvm ol7 pause
The following command configures shared clipboard operation for the ol7 VM. Copying of clipboard data is allowed in both directions between the host and guest.

$ VBoxManage controlvm ol7 clipboard mode bidirectional
See Also
VBoxManage list, VBoxManage modifyvm, VBoxManage startvm

VBoxManage convertfromraw
Convert a raw disk image to a virtual disk image

Synopsis
VBoxManage convertfromraw <inputfile> <outputfile> [‑‑format=VDI | VMDK | VHD] [‑‑uuid=uuid] [‑‑variant=Standard|Fixed|Split2G|Stream|ESX...]
VBoxManage convertfromraw stdin <outputfile> <bytes> [‑‑format=VDI | VMDK | VHD] [‑‑uuid=uuid] [‑‑variant=Standard|Fixed|Split2G|Stream|ESX...]
Description
The VBoxManage convertfromraw command enables you to convert a raw disk image to an Oracle VirtualBox virtual disk image (VDI).

Note:
For compatibility with earlier versions of Oracle VirtualBox, you can use the VBoxManage convertdd command instead of the VBoxManage convertfromraw command.

Convert a Raw Disk File to a Virtual Disk Image File
VBoxManage convertfromraw <inputfile> <outputfile> [‑‑format=VDI | VMDK | VHD] [‑‑uuid=uuid] [‑‑variant=Standard|Fixed|Split2G|Stream|ESX...]

The VBoxManage convertfromraw command converts the specified raw disk image input file to an Oracle VirtualBox VDI file.

inputfile
Specifies the name of the raw disk image file to convert.

outputfile
Specifies the name of the file in which to write the VDI output.

--format=VDI | VMDK | VHD
Specifies the format of the disk image to create. Valid values are VDI, VMDK, and VHD. The default format is VDI.

--uuid=uuid
Specifies the Universally Unique Identifier (UUID) of the output file.

--variant=Standard | Fixed | Split2G | Stream | ESX[,...]
Specifies any required file format variants for the output file. This is a comma-separated list of variant values. Following are the valid values:

Standard is the default disk image type, which has a dynamically allocated file size.

Fixed uses a disk image that has a fixed file size.

Split2G indicates that the disk image is split into 2GB segments. This value is for VMDK only.

Stream optimizes the disk image for downloading. This value is for VMDK only.

ESX is used for some VMWare products. This value is for VMDK only.

Note that not all variant combinations are valid. Specifying incompatible variant values in the list will produce an error message.

Convert Raw Data From Standard Input to a Virtual Disk Image File
VBoxManage convertfromraw stdin <outputfile> <bytes> [‑‑format=VDI | VMDK | VHD] [‑‑uuid=uuid] [‑‑variant=Standard|Fixed|Split2G|Stream|ESX...]

The VBoxManage convertfromraw stdin command reads the content of the disk image from standard input. Consider using this form of the command in a pipe sequence.

outputfile
Specifies the name of the file in which to write the disk image output.

bytes
Specifies the capacity of the target image name. This needs to be given explicitly, because generally pipes do not support querying the overall size of the data stream.

--format=VDI | VMDK | VHD
Specifies the format of the disk image to create. Valid values are VDI, VMDK, and VHD. The default format is VDI.

--uuid=uuid
Specifies the UUID of the output file.

--variant=Standard,Fixed,Split2G,Stream,ESX
Specifies any required file format variants for the output file. This is a comma-separated list of variant values. The list of valid values is:

Standard is the default disk image type, which has a dynamically allocated file size.

Fixed uses a disk image that has a fixed file size.

Split2G indicates that the disk image is split into 2GB segments. This value is for VMDK only.

Stream optimizes the disk image for downloading. This value is for VMDK only.

ESX is used for some VMWare products. This value is for VMDK only.

Note that not all variant combinations are valid. Specifying incompatible variant values in the list will produce an error message.

Examples
The following command converts the raw disk image input file disk01.raw. The output file is a VDI disk image called disk02.vdi.

$ VBoxManage convertfromraw disk01.raw disk02.vdi
The following command converts the raw disk image input file disk01.raw. The output file is a VMDK disk image called disk02.vmdk.

$ VBoxManage convertfromraw disk01.raw disk02.vmdk --format VMDK
The following command reads from disk /dev/sda using a pipe and therefore needs the exact disk size in bytes as an additional parameter, which is assumed to be 10737418240. The output file is a VDI disk image called disk.vdi.

$ dd if=/dev/sda bs=512 | VBoxManage convertfromraw stdin disk.vdi 10737418240
VBoxManage createmedium
Create a new medium

Synopsis
VBoxManage createmedium [disk | dvd | floppy] <‑‑filename=filename> [‑‑size=megabytes | ‑‑sizebyte=bytes] [‑‑diffparent=UUID | filename] [‑‑format=VDI | VMDK | VHD] [‑‑variant=Standard|Fixed|Split2G|Stream|ESX|Formatted|RawDisk...] [‑‑property=name=value...] [‑‑property‑file=name=/path/to/file/with/value...]
Description
The VBoxManage createmedium command creates a new medium, such as a disk image file.

Note:
For compatibility with earlier versions of Oracle VirtualBox, you can use the createvdi and createhd commands instead of the createmedium command.

disk | dvd | floppy
Specifies the media type. The default value is disk.

--filename=filename
Specifies the absolute path name to a file on the host file system.

--size=megabytes
Specifies the image capacity in one megabyte units.

--sizebyte=bytes
Specifies the image capacity in one byte units.

--diffparent=UUID | filename
Specifies the Universally Unique Identifier (UUID) or absolute path name of a differencing image parent file on the host file system.

Use this file to share a base box disk image among VMs.

--format=VDI | VMDK | VHD
Specifies the file format of the output file. Valid formats are VDI, VMDK, and VHD. The default format is VDI.

--variant=Standard | Fixed | Split2G | Stream | ESX | Formatted | RawDisk [,...]
Specifies the file format variant for the target medium, which is a comma-separated list of variants. Following are the valid values:

Standard is the default disk image type, which has a dynamically allocated file size.

Fixed uses a disk image that has a fixed file size.

Split2G indicates that the disk image is split into 2GB segments. This value is valid for VMDK disk images only.

Stream optimizes the disk image for downloading. This value is valid for VMDK disk images only.

ESX is used for some VMWare products. This value is valid for VMDK disk images only.

Formatted formats the medium automatically. This value is valid for floppy images only.

RawDisk is used for creating a VMDK image which provides direct access to the hard disk on the host using its raw interface. This value is valid for VMDK disk images only. For detailed information about raw disk access, see Advanced Storage Configuration.

Note that not all variant combinations are valid. Specifying incompatible variant values in the list will produce an error message.

--property=name=value
Specifies any required file format dependent parameters in key=value form. Optional.

--property-file=name=/path/to/file/with/value
Specifies any required file format dependent parameters in key=file/with/value form. The value is taken from the file. Optional.

Examples
The following command creates a new disk image file named disk01.vdi. The file size is 1024 megabytes.

$ VBoxManage createmedium --filename disk01.vdi --size 1024
The following command creates a new floppy disk image file named floppy01.vdi. The file size is 1 megabyte.

$ VBoxManage createmedium floppy --filename floppy01.img --size 1
The following command creates a raw disk image of an entire physical disk on a Linux host.

$ VBoxManage createmedium disk --filename=/path/to/rawdisk.vmdk --variant=RawDisk --format=VMDK --property RawDrive=/dev/sda
VBoxManage createvm
Create a new virtual machine

Synopsis
VBoxManage createvm <‑‑name=name> <‑‑platform‑architecture=x86 | arm> [‑‑basefolder=basefolder] [‑‑default] [‑‑groups=group‑ID [,...]] [‑‑ostype=ostype] [‑‑register] [‑‑uuid=uuid] [‑‑cipher=cipher] [‑‑password‑id=password‑id] [‑‑password=file]
Description
The VBoxManage createvm command creates a new XML virtual machine (VM) definition file.

You must specify the name of the VM by using --name name. This name is used by default as the name of the settings file that has the .vbox extension and the machine folder, which is a subfolder of the $HOME/VirtualBox VMs directory.

The actual file name may not correspond directly to the VM name if it violates the host OS file name requirements (such as using the path separator or other reserved characters, they will be substituted with a placeholder). If you later rename the VM, the file and folder names will be updated to match the new name automatically.

Also, the intended platform architecture for the VM must be specified by using --platform-architecturearchitecture .

Command Options
In addition to specifying the name or UUID of the VM and the platform architecture, which is required, you can specify any of the following options:

--basefolder=basefolder
Specifies the name of the folder in which to save the machine configuration file for the new VM.

Note that the names of the file and the folder do not change if you rename the VM.

--default
Applies a default hardware configuration for the specified guest OS. By default, the VM is created with minimal hardware.

--groups=group-ID[,...]
Assigns the VM to the specified groups. If you specify more than one group, separate each group name with a comma.

Note that each group is identified by a group ID that starts with a slash character (/) so that groups can be nested. By default, a VM is always assigned membership to the / group.

--ostype=ostype
Specifies the guest OS to run in the VM. Run the VBoxManage list ostypes command to see the available OS types.

--register
Registers the VM with your Oracle VirtualBox installation. By default, the VBoxManage createvm command creates only the XML configuration for the VM but does not register the VM. If you do not register the VM at creation, you can run the VBoxManage registervm command after you create the VM.

--uuid=uuid
Specifies the Universally Unique Identifier (UUID) of the VM. Ensure that this UUID is unique within the Oracle VirtualBox namespace of the host or of its VM group memberships if you decide to register the VM. By default, Oracle VirtualBox provides the UUID.

--cipher=cipher
Specifies the cipher to use for encryption. Valid values are AES-128 or AES-256.

This option enables you to set up encryption on VM.

--password-id=password-id
Specifies a new password identifier that is used for correct identification when supplying multiple passwords for the VM.

This option enables you to set up encryption on VM.

--password=file
Use the --password to supply the encryption password of the VM. Either specify the absolute pathname of a password file on the host operating system, or - to prompt you for the password on the command line.

This option enables you to set up encryption on VM.

Examples
The following command creates a VM called vm2 where you plan to run a 64-bit version of Oracle Linux.

$ VBoxManage createvm --name "vm2" --ostype "Oracle_64"
The following command creates and registers a VM called vm3.

$ VBoxManage createvm --name "vm3" --register
See Also
VBoxManage list, VBoxManage registervm

VBoxManage debugvm
Introspection and guest debugging

Synopsis
VBoxManage debugvm <uuid | vmname>dumpvmcore <‑‑filename=name>
VBoxManage debugvm <uuid | vmname>info <item> [args...]
VBoxManage debugvm <uuid | vmname>injectnmi
VBoxManage debugvm <uuid | vmname>log [‑‑release | ‑‑debug] [group‑settings...]
VBoxManage debugvm <uuid | vmname>logdest [‑‑release | ‑‑debug] [destinations...]
VBoxManage debugvm <uuid | vmname>logflags [‑‑release | ‑‑debug] [flags...]
VBoxManage debugvm <uuid | vmname>osdetect
VBoxManage debugvm <uuid | vmname>osinfo
VBoxManage debugvm <uuid | vmname>osdmesg [‑‑lines=lines]
VBoxManage debugvm <uuid | vmname>getregisters [‑‑cpu=id] [reg‑set.reg‑name...]
VBoxManage debugvm <uuid | vmname>setregisters [‑‑cpu=id] [reg‑set.reg‑name=value...]
VBoxManage debugvm <uuid | vmname>show [‑‑human‑readable | ‑‑sh‑export | ‑‑sh‑eval | ‑‑cmd‑set] [settings‑item...]
VBoxManage debugvm <uuid | vmname>stack [‑‑cpu=id]
VBoxManage debugvm <uuid | vmname>statistics [‑‑reset] [‑‑descriptions] [‑‑pattern=pattern]
VBoxManage debugvm <uuid | vmname>guestsample [‑‑filename=filename] [‑‑sample‑interval‑us=interval] [‑‑sample‑time‑us=time]
Description
The "debugvm" commands are for experts who want to tinker with the exact details of virtual machine execution. Like the VM debugger described in The Built-In VM Debugger, these commands are only useful if you are very familiar with the details of the PC architecture and how to debug software.

Common options
The subcommands of debugvm all operate on a running virtual machine:

uuid | vmname
Either the UUID or the name (case sensitive) of a VM.

debugvm dumpvmcore
VBoxManage debugvm <uuid | vmname>dumpvmcore <‑‑filename=name>

Creates a system dump file of the specified VM. This file will have the standard ELF core format (with custom sections); see VM Core Format.

This corresponds to the writecore command in the debugger.

--filename=filename
The name of the output file. This option is required.

debugvm info
VBoxManage debugvm <uuid | vmname>info <item> [args...]

Displays info items relating to the VMM, device emulations and associated drivers.

This corresponds to the info command in the debugger.

item
Name of the info item to display. The special name help will list all the available info items and hints about optional arguments.

args
Optional argument string for the info item handler. Most info items do not take any extra arguments. Arguments not recognized are generally ignored.

debugvm injectnmi
VBoxManage debugvm <uuid | vmname>injectnmi

Causes a non-maskable interrupt (NMI) to be injected into the guest. This might be useful for certain debugging scenarios. What happens exactly is dependent on the guest operating system, but an NMI can crash the whole guest operating system. Do not use unless you know what you're doing.

debugvm log
VBoxManage debugvm <uuid | vmname>log [‑‑release | ‑‑debug] [group‑settings...]

Changes the group settings for either the debug (--debug) or release (--release) logger of the VM process.

The group-settings are typically strings of the form em.e.f.l, hm=~0 and -em.f. Basic wildcards are supported for group matching. The all group is an alias for all the groups.

Please keep in mind that the group settings are applied as modifications to the current values.

This corresponds to the log command in the debugger.

debugvm logdest
VBoxManage debugvm <uuid | vmname>logdest [‑‑release | ‑‑debug] [destinations...]

Changes the destination settings for either the debug (--debug) or release (--release) logger of the VM process. For details on the destination format, the best source is src/VBox/Runtime/common/log/log.cpp.

The destinations argument is one or more mnemonics, optionally prefixed by "no" to disable them. Some of these take values after a ":" or "=" separator. Multiple mnemonics can be separated by whitespace or given as separate arguments on the command line.

List of available destinations:

file[=file], nofile
Specifies a log file. If no filename is given, one will be generated based on the current UTC time and VM process name and placed in the current directory of the VM process. Note that this will not have any effect if the log file has already been opened.

dir=directory, nodir
Specifies the output directory for log files. Note that this will not have any effect if the log file has already been opened.

history=count, nohistory
A non-zero value enables log rotation, with the value specifying how many old log files to keep.

histsize=bytes
The max size of a log file before it is rotated. Default is infinite.

histtime=seconds
The max age (in seconds) of a log file before it is rotated. Default is infinite.

ringbuffer, noringbuffer
Only log to the log buffer until an explicit flush (e.g. via an assertion) occurs. This is fast and saves diskspace.

stdout, nostdout
Write the log content to standard output.

stdout, nostdout
Write the log content to standard error.

debugger, nodebugger
Write the log content to the debugger, if supported by the host OS.

com, nocom
Writes logging to the COM port. This is only applicable for raw-mode and ring-0 logging.

user, nouser
Custom destination which has no meaning to VM processes.

This corresponds to the logdest command in the debugger.

debugvm logflags
VBoxManage debugvm <uuid | vmname>logflags [‑‑release | ‑‑debug] [flags...]

Changes the flags for either the debug (--debug) or release (--release) logger of the VM process. Please note that the modifications are applied to the existing values, they do not replace them.

The flags are a list of flag mnemonics, optionally prefixed by a "no", "!", "~" or "-" to negate their meaning. The "+" prefix can be used to undo a previous negation or as a separator, although it is better to use whitespace or separate arguments for that.

List of log flag mnemonics, with their opposite value where applicable (an asterisk indicates the default value):

enabled*, disabled
Enables or disables logging.

buffered, unbuffered*
Enables buffering of log output before it hits the destinations.

writethrough(/writethru)
Whether to open the destination file with writethru buffering settings or not.

flush
Enables flushing of the output file (to disk) after each log statement.

lockcnts
Prefix each log line with lock counts for the current thread.

cpuid
Prefix each log line with the ID of the current CPU.

pid
Prefix each log line with the current process ID.

flagno
Prefix each log line with the numeric flags corresponding to the log statement.

flag
Prefix each log line with the flag mnemonics corresponding to the log statement.

groupno
Prefix each log line with the log group number for the log statement producing it.

group
Prefix each log line with the log group name for the log statement producing it.

tid
Prefix each log line with the current thread identifier.

thread
Prefix each log line with the current thread name.

time
Prefix each log line with the current UTC wall time.

timeprog
Prefix each log line with the current monotonic time since the start of the program.

msprog
Prefix each log line with the current monotonic timestamp value in milliseconds since the start of the program.

ts
Prefix each log line with the current monotonic timestamp value in nanoseconds.

tsc
Prefix each log line with the current CPU timestamp counter (TSC) value.

rel, abs*
Selects whether the ts and tsc prefixes should be displayed as relative to the previous log line or as absolute time.

hex*, dec
Selects whether the ts and tsc prefixes should be formatted as hexadecimal or decimal.

custom
Custom log prefix, has by default no meaning for VM processes.

usecrlf, uself*
Output with DOS style (CRLF) or just UNIX style (LF) line endings.

overwrite*, append
Overwrite the destination file or append to it.

This corresponds to the logflags command in the debugger.

debugvm osdetect
VBoxManage debugvm <uuid | vmname>osdetect

Make the VMM's debugger facility (re)-detect the guest operating system (OS). This will first load all debugger plug-ins.

This corresponds to the detect command in the debugger.

debugvm osinfo
VBoxManage debugvm <uuid | vmname>osinfo

Displays information about the guest operating system (OS) previously detected by the VMM's debugger facility.

debugvm osdmesg
VBoxManage debugvm <uuid | vmname>osdmesg [‑‑lines=lines]

Displays the guest OS kernel log, if detected and supported.

--lines=lines
Number of lines of the log to display, counting from the end. The default is infinite.

debugvm getregisters
VBoxManage debugvm <uuid | vmname>getregisters [‑‑cpu=id] [reg‑set.reg‑name...]

Retrieves register values for guest CPUs and emulated devices.

reg-set.reg-name
One or more registers, each having one of the following forms:

register-set.register-name.sub-field

register-set.register-name

cpu-register-name.sub-field

cpu-register-name

all

The all form will cause all registers to be shown (no sub-fields). The registers names are case-insensitive.

--cpu=id
Selects the CPU register set when specifying just a CPU register (3rd and 4th form). The default is 0.

debugvm setregisters
VBoxManage debugvm <uuid | vmname>setregisters [‑‑cpu=id] [reg‑set.reg‑name=value...]

Changes register values for guest CPUs and emulated devices.

reg-set.reg-name=value
One or more register assignment, each having one of the following forms:

register-set.register-name.sub-field=value

register-set.register-name=value

cpu-register-name.sub-field=value

cpu-register-name=value

The value format should be in the same style as what getregisters displays, with the exception that both octal and decimal can be used instead of hexadecimal.

--cpu=id
Selects the CPU register set when specifying just a CPU register (3rd and 4th form). The default is 0.

debugvm show
VBoxManage debugvm <uuid | vmname>show [‑‑human‑readable | ‑‑sh‑export | ‑‑sh‑eval | ‑‑cmd‑set] [settings‑item...]

Shows logging settings for the VM.

--human-readable
Selects human readable output.

--sh-export
Selects output format as bourne shell style export commands.

--sh-eval
Selects output format as bourne shell style eval command input.

--cmd-set
Selects output format as DOS style SET commands.

settings-item
What to display. One or more of the following:

logdbg-settings - debug log settings.

logrel-settings - release log settings.

log-settings - alias for both debug and release log settings.

debugvm stack
VBoxManage debugvm <uuid | vmname>stack [‑‑cpu=id]

Unwinds the guest CPU stacks to the best of our ability. It is recommended to first run the osdetect command, as this gives both symbols and perhaps unwind information.

--cpu=id
Selects a single guest CPU to display the stack for. The default is all CPUs.

debugvm statistics
VBoxManage debugvm <uuid | vmname>statistics [‑‑reset] [‑‑descriptions] [‑‑pattern=pattern]

Displays or resets VMM statistics.

Retrieves register values for guest CPUs and emulated devices.

--pattern=pattern
DOS/NT-style wildcards patterns for selecting statistics. Multiple patterns can be specified by using the '|' (pipe) character as separator.

--reset
Select reset instead of display mode.

debugvm guestsample
VBoxManage debugvm <uuid | vmname>guestsample [‑‑filename=filename] [‑‑sample‑interval‑us=interval] [‑‑sample‑time‑us=time]

Creates a sample report of the guest activity.

Retrieves the filename to dump the report to.

--filename=filename
The filename to dump the sample report to.

--sample-interval-us=interval
The interval in microseconds between guest samples.

--sample-time-us=time
The amount of microseconds to take guest samples.

VBoxManage dhcpserver
DHCP server management

Synopsis
VBoxManage dhcpserver add <‑‑network=netname | ‑‑interface=ifname> <‑‑server‑ip=address> <‑‑netmask=mask> <‑‑lower‑ip=address> <‑‑upper‑ip=address> <‑‑enable | ‑‑disable> [[‑‑global] [‑‑set‑opt=dhcp‑opt‑no value...] [‑‑set‑opt‑hex=dhcp‑opt‑no hexstring...] [‑‑force‑opt=dhcp‑opt‑no...] [‑‑suppress‑opt=dhcp‑opt‑no...] [‑‑min‑lease‑time=seconds] [‑‑default‑lease‑time=seconds] [‑‑max‑lease‑time=seconds]...] [<‑‑group=name> [‑‑set‑opt=dhcp‑opt‑no value...] [‑‑set‑opt‑hex=dhcp‑opt‑no hexstring...] [‑‑force‑opt=dhcp‑opt‑no...] [‑‑suppress‑opt=dhcp‑opt‑no...] [‑‑incl‑mac=address...] [‑‑excl‑mac=address...] [‑‑incl‑mac‑wild=pattern...] [‑‑excl‑mac‑wild=pattern...] [‑‑incl‑vendor=string...] [‑‑excl‑vendor=string...] [‑‑incl‑vendor‑wild=pattern...] [‑‑excl‑vendor‑wild=pattern...] [‑‑incl‑user=string...] [‑‑excl‑user=string...] [‑‑incl‑user‑wild=pattern...] [‑‑excl‑user‑wild=pattern...] [‑‑min‑lease‑time=seconds] [‑‑default‑lease‑time=seconds] [‑‑max‑lease‑time=seconds]...] [<‑‑vm=name|uuid> [‑‑nic=1‑N] [‑‑set‑opt=dhcp‑opt‑no value...] [‑‑set‑opt‑hex=dhcp‑opt‑no hexstring...] [‑‑force‑opt=dhcp‑opt‑no...] [‑‑suppress‑opt=dhcp‑opt‑no...] [‑‑min‑lease‑time=seconds] [‑‑default‑lease‑time=seconds] [‑‑max‑lease‑time=seconds] [‑‑fixed‑address=address]...] [<‑‑mac‑address=address> [‑‑set‑opt=dhcp‑opt‑no value...] [‑‑set‑opt‑hex=dhcp‑opt‑no hexstring...] [‑‑force‑opt=dhcp‑opt‑no...] [‑‑suppress‑opt=dhcp‑opt‑no...] [‑‑min‑lease‑time=seconds] [‑‑default‑lease‑time=seconds] [‑‑max‑lease‑time=seconds] [‑‑fixed‑address=address]...]
VBoxManage dhcpserver modify <‑‑network=netname | ‑‑interface=ifname> [‑‑server‑ip=address] [‑‑lower‑ip=address] [‑‑upper‑ip=address] [‑‑netmask=mask] [‑‑enable | ‑‑disable] [[‑‑global] [‑‑del‑opt=dhcp‑opt‑no...] [‑‑set‑opt=dhcp‑opt‑no value...] [‑‑set‑opt‑hex=dhcp‑opt‑no hexstring...] [‑‑force‑opt=dhcp‑opt‑no...] [‑‑unforce‑opt=dhcp‑opt‑no...] [‑‑suppress‑opt=dhcp‑opt‑no...] [‑‑unsuppress‑opt=dhcp‑opt‑no...] [‑‑min‑lease‑time=seconds] [‑‑default‑lease‑time=seconds] [‑‑max‑lease‑time=seconds] [‑‑remove‑config]...] [<‑‑group=name> [‑‑set‑opt=dhcp‑opt‑no value...] [‑‑set‑opt‑hex=dhcp‑opt‑no hexstring...] [‑‑force‑opt=dhcp‑opt‑no...] [‑‑unforce‑opt=dhcp‑opt‑no...] [‑‑suppress‑opt=dhcp‑opt‑no...] [‑‑unsuppress‑opt=dhcp‑opt‑no...] [‑‑del‑mac=address...] [‑‑incl‑mac=address...] [‑‑excl‑mac=address...] [‑‑del‑mac‑wild=pattern...] [‑‑incl‑mac‑wild=pattern...] [‑‑excl‑mac‑wild=pattern...] [‑‑del‑vendor=string...] [‑‑incl‑vendor=string...] [‑‑excl‑vendor=string...] [‑‑del‑vendor‑wild=pattern...] [‑‑incl‑vendor‑wild=pattern...] [‑‑excl‑vendor‑wild=pattern...] [‑‑del‑user=string...] [‑‑incl‑user=string...] [‑‑excl‑user=string...] [‑‑del‑user‑wild=pattern...] [‑‑incl‑user‑wild=pattern...] [‑‑excl‑user‑wild=pattern...] [‑‑zap‑conditions] [‑‑min‑lease‑time=seconds] [‑‑default‑lease‑time=seconds] [‑‑max‑lease‑time=seconds] [‑‑remove‑config]...] [<‑‑vm=name|uuid> [‑‑nic=1‑N] [‑‑del‑opt=dhcp‑opt‑no...] [‑‑set‑opt=dhcp‑opt‑no value...] [‑‑set‑opt‑hex=dhcp‑opt‑no hexstring...] [‑‑force‑opt=dhcp‑opt‑no...] [‑‑unforce‑opt=dhcp‑opt‑no...] [‑‑suppress‑opt=dhcp‑opt‑no...] [‑‑unsuppress‑opt=dhcp‑opt‑no...] [‑‑min‑lease‑time=seconds] [‑‑default‑lease‑time=seconds] [‑‑max‑lease‑time=seconds] [‑‑fixed‑address=address] [‑‑remove‑config]...] [<‑‑mac‑address=address> [‑‑del‑opt=dhcp‑opt‑no...] [‑‑set‑opt=dhcp‑opt‑no value...] [‑‑set‑opt‑hex=dhcp‑opt‑no hexstring...] [‑‑force‑opt=dhcp‑opt‑no...] [‑‑unforce‑opt=dhcp‑opt‑no...] [‑‑suppress‑opt=dhcp‑opt‑no...] [‑‑unsuppress‑opt=dhcp‑opt‑no...] [‑‑min‑lease‑time=seconds] [‑‑default‑lease‑time=seconds] [‑‑max‑lease‑time=seconds] [‑‑fixed‑address=address] [‑‑remove‑config]...]
VBoxManage dhcpserver remove <‑‑network=netname | ‑‑interface=ifname>
VBoxManage dhcpserver start <‑‑network=netname | ‑‑interface=ifname>
VBoxManage dhcpserver restart <‑‑network=netname | ‑‑interface=ifname>
VBoxManage dhcpserver stop <‑‑network=netname | ‑‑interface=ifname>
VBoxManage dhcpserver findlease <‑‑network=netname | ‑‑interface=ifname> <‑‑mac‑address=mac>
Description
The dhcpserver commands enable you to control the DHCP server that is built into Oracle VirtualBox. You may find this useful when using internal or host-only networking. Theoretically, you can also enable it for a bridged network, but that may cause conflicts with other DHCP servers in your physical network.

Common options
The subcommands of dhcpserver all operate on an internal network that can be identified via its name or in the host-only case via the host-only interface name:

--network=netname
The internal network name. This is the same name you would supply to the VBoxManage modifyvm --intnet option when configuring a VM for internal networking. The internal network name can be found labelled as VBoxNetworkName in the output of the following commands: VBoxManage list intnets, VBoxManage list natnets, or VBoxManage list hostonlyifs.

--interface=ifname
The host only interface name. This is the same value would supply to the VBoxManage modifyvm --host-only-adapter option when configuring a VM to use a host-only network. The value can also be found labelled as Name in the output of the VBoxManage list hostonlyifs command.

dhcpserver add
VBoxManage dhcpserver add <‑‑network=netname | ‑‑interface=ifname> <‑‑server‑ip=address> <‑‑netmask=mask> <‑‑lower‑ip=address> <‑‑upper‑ip=address> <‑‑enable | ‑‑disable> [[‑‑global] [‑‑set‑opt=dhcp‑opt‑no value...] [‑‑set‑opt‑hex=dhcp‑opt‑no hexstring...] [‑‑force‑opt=dhcp‑opt‑no...] [‑‑suppress‑opt=dhcp‑opt‑no...] [‑‑min‑lease‑time=seconds] [‑‑default‑lease‑time=seconds] [‑‑max‑lease‑time=seconds]...] [<‑‑group=name> [‑‑set‑opt=dhcp‑opt‑no value...] [‑‑set‑opt‑hex=dhcp‑opt‑no hexstring...] [‑‑force‑opt=dhcp‑opt‑no...] [‑‑suppress‑opt=dhcp‑opt‑no...] [‑‑incl‑mac=address...] [‑‑excl‑mac=address...] [‑‑incl‑mac‑wild=pattern...] [‑‑excl‑mac‑wild=pattern...] [‑‑incl‑vendor=string...] [‑‑excl‑vendor=string...] [‑‑incl‑vendor‑wild=pattern...] [‑‑excl‑vendor‑wild=pattern...] [‑‑incl‑user=string...] [‑‑excl‑user=string...] [‑‑incl‑user‑wild=pattern...] [‑‑excl‑user‑wild=pattern...] [‑‑min‑lease‑time=seconds] [‑‑default‑lease‑time=seconds] [‑‑max‑lease‑time=seconds]...] [<‑‑vm=name|uuid> [‑‑nic=1‑N] [‑‑set‑opt=dhcp‑opt‑no value...] [‑‑set‑opt‑hex=dhcp‑opt‑no hexstring...] [‑‑force‑opt=dhcp‑opt‑no...] [‑‑suppress‑opt=dhcp‑opt‑no...] [‑‑min‑lease‑time=seconds] [‑‑default‑lease‑time=seconds] [‑‑max‑lease‑time=seconds] [‑‑fixed‑address=address]...] [<‑‑mac‑address=address> [‑‑set‑opt=dhcp‑opt‑no value...] [‑‑set‑opt‑hex=dhcp‑opt‑no hexstring...] [‑‑force‑opt=dhcp‑opt‑no...] [‑‑suppress‑opt=dhcp‑opt‑no...] [‑‑min‑lease‑time=seconds] [‑‑default‑lease‑time=seconds] [‑‑max‑lease‑time=seconds] [‑‑fixed‑address=address]...]

Adds a new DHCP server to a network or host-only interface.

Options configuring the DHCP server core:

--server-ip=address
The IP address the DHCP server should use.

--lower-ip=address, --upper-ip=address
The IP address range for the DHCP server to manage. This should not include the address of the DHCP server itself, but it must be in the same network. The boundaries are inclusive, so both the lower and upper addresses will be handed out to clients.

--netmask=mask
The network mask. Typically *************.

--enable, --disable
Whether to enable the DHCP server or disable it. If not specified, the server will be created in a disabled state and no IP addresses will be handed out.

Options selecting the scope:

--global
Set the configuration scope to global. Any subsequent --set-opt options will be applied to all the DHCP clients.

--vm=vmname|uuid
Set the configuration scope to the first NIC of the specified VM. Any subsequent --set-opt options will apply just to that interface, nothing else.

--nic=1-N
Set the configuration scope to a NIC other than the first NIC of the VM specified in the --vm option.

--mac-address=address
Set the configuration scope to the specified MAC address.

--group=name
Set the configuration scope to the specified group.

Options configuring the currently selected scope:

--set-opt=dhcp-opt-no value
Adds the specified DHCP option number (0-255) and value. The value format is option specific (typically human readable) and will be validated by the API and the DHCP server.

--set-opt-hex=dhcp-opt-no hexstring
Adds the specified DHCP option number (0-255) and value. The option value is specified as a raw series of hex bytes optionally separated by colons. No validation is performed on these by the API or the DHCP server, they will be passed as specified to the client.

--force-opt=dhcp-opt-no
Forces the specified DHCP option number (0-255) to be sent to the client whether it requested it or not (provided the option is configured with a value at some level).

--suppress-opt=dhcp-opt-no
Prevents the specified DHCP option number (0-255) from being sent to the client when present in this or a higher configuration scope.

--min-lease-time=seconds
Sets the minimum lease time for the current scope in seconds. Zero means taking the value from a higher option level or use the default value.

--default-lease-time=seconds
Sets the default lease time for the current scope in seconds. Zero means taking the value from a higher option level or use the default value.

--max-lease-time=seconds
Sets the maximum lease time for the current scope in seconds. Zero means taking the value from a higher option level or use default.

--fixed-address=address
Fixed address assignment for a --vm or --mac-address configuration scope. Any empty address turns it back to dynamic address assignment.

Options configuring group membership conditions (excludes overrides includes):

--incl-mac=address
Include the specific MAC address in the group.

--excl-mac=address
Exclude the specific MAC address from the group.

--incl-mac-wild=pattern
Include the specific MAC address pattern in the group.

--excl-mac-wild=pattern
Exclude the specific MAC address pattern from the group.

--incl-vendor=string
Include the specific vendor class ID in the group.

--excl-vendor=string
Exclude the specific vendor class ID from the group.

--incl-vendor-wild=pattern
Include the specific vendor class ID pattern in the group.

--excl-vendor-wild=pattern
Exclude the specific vendor class ID pattern from the group.

--incl-user=string
Include the specific user class ID in the group.

--excl-user=string
Exclude the specific user class ID from the group.

--incl-user-wild=pattern
Include the specific user class ID pattern in the group.

--excl-user-wild=pattern
Exclude the specific user class ID pattern from the group.

dhcpserver modify
VBoxManage dhcpserver modify <‑‑network=netname | ‑‑interface=ifname> [‑‑server‑ip=address] [‑‑lower‑ip=address] [‑‑upper‑ip=address] [‑‑netmask=mask] [‑‑enable | ‑‑disable] [[‑‑global] [‑‑del‑opt=dhcp‑opt‑no...] [‑‑set‑opt=dhcp‑opt‑no value...] [‑‑set‑opt‑hex=dhcp‑opt‑no hexstring...] [‑‑force‑opt=dhcp‑opt‑no...] [‑‑unforce‑opt=dhcp‑opt‑no...] [‑‑suppress‑opt=dhcp‑opt‑no...] [‑‑unsuppress‑opt=dhcp‑opt‑no...] [‑‑min‑lease‑time=seconds] [‑‑default‑lease‑time=seconds] [‑‑max‑lease‑time=seconds] [‑‑remove‑config]...] [<‑‑group=name> [‑‑set‑opt=dhcp‑opt‑no value...] [‑‑set‑opt‑hex=dhcp‑opt‑no hexstring...] [‑‑force‑opt=dhcp‑opt‑no...] [‑‑unforce‑opt=dhcp‑opt‑no...] [‑‑suppress‑opt=dhcp‑opt‑no...] [‑‑unsuppress‑opt=dhcp‑opt‑no...] [‑‑del‑mac=address...] [‑‑incl‑mac=address...] [‑‑excl‑mac=address...] [‑‑del‑mac‑wild=pattern...] [‑‑incl‑mac‑wild=pattern...] [‑‑excl‑mac‑wild=pattern...] [‑‑del‑vendor=string...] [‑‑incl‑vendor=string...] [‑‑excl‑vendor=string...] [‑‑del‑vendor‑wild=pattern...] [‑‑incl‑vendor‑wild=pattern...] [‑‑excl‑vendor‑wild=pattern...] [‑‑del‑user=string...] [‑‑incl‑user=string...] [‑‑excl‑user=string...] [‑‑del‑user‑wild=pattern...] [‑‑incl‑user‑wild=pattern...] [‑‑excl‑user‑wild=pattern...] [‑‑zap‑conditions] [‑‑min‑lease‑time=seconds] [‑‑default‑lease‑time=seconds] [‑‑max‑lease‑time=seconds] [‑‑remove‑config]...] [<‑‑vm=name|uuid> [‑‑nic=1‑N] [‑‑del‑opt=dhcp‑opt‑no...] [‑‑set‑opt=dhcp‑opt‑no value...] [‑‑set‑opt‑hex=dhcp‑opt‑no hexstring...] [‑‑force‑opt=dhcp‑opt‑no...] [‑‑unforce‑opt=dhcp‑opt‑no...] [‑‑suppress‑opt=dhcp‑opt‑no...] [‑‑unsuppress‑opt=dhcp‑opt‑no...] [‑‑min‑lease‑time=seconds] [‑‑default‑lease‑time=seconds] [‑‑max‑lease‑time=seconds] [‑‑fixed‑address=address] [‑‑remove‑config]...] [<‑‑mac‑address=address> [‑‑del‑opt=dhcp‑opt‑no...] [‑‑set‑opt=dhcp‑opt‑no value...] [‑‑set‑opt‑hex=dhcp‑opt‑no hexstring...] [‑‑force‑opt=dhcp‑opt‑no...] [‑‑unforce‑opt=dhcp‑opt‑no...] [‑‑suppress‑opt=dhcp‑opt‑no...] [‑‑unsuppress‑opt=dhcp‑opt‑no...] [‑‑min‑lease‑time=seconds] [‑‑default‑lease‑time=seconds] [‑‑max‑lease‑time=seconds] [‑‑fixed‑address=address] [‑‑remove‑config]...]

The dhcpserver modify command modifies an existing DHCP server configuration. It takes the same options as the add command with the addition of the following options which have variable configuration scope:

--del-opt=dhcp-opt-no
Counterpart to --set-opt that will cause the specified DHCP option number (0-255) to be deleted from the server settings. Similar to --set-opt the scope of the deletion is governed by the --global, --vm, --mac-address and --group options.

--unforce-opt=dhcp-opt-no
Removes the specified DHCP option number (0-255) from the forced option list (i.e. the reverse of --force-opt). Similar to --set-opt the scope of the deletion is governed by the --global, --vm, --mac-address and --group options.

--unsuppress-opt=dhcp-opt-no
Removes the specified DHCP option number (0-255) from the suppressed option list (i.e. the reverse of --suppress-opt). Similar to --set-opt the scope of the deletion is governed by the --global, --vm, --mac-address and --group options.

--remove-config
Removes the configuration currently being scoped. The --global scope is not removable. The configuration scope will change to --global after this option.

And the addition of these group membership condition options:

--del-mac=address
Delete the specific MAC address from the group conditions.

--del-mac-wild=pattern
Delete the specific MAC address pattern from the group conditions.

--del-vendor=string
Delete the specific vendor class ID from the group conditions.

--del-vendor-wild=pattern
Delete the specific vendor class ID pattern from the group conditions.

--del-user=string
Delete the specific user class ID from the group conditions.

--del-user-wild=pattern
Delete the specific user class ID pattern from the group conditions.

--zap-conditions
Deletes all the group conditions.

dhcpserver remove
VBoxManage dhcpserver remove <‑‑network=netname | ‑‑interface=ifname>

Removes the specified DHCP server.

dhcpserver start
VBoxManage dhcpserver start <‑‑network=netname | ‑‑interface=ifname>

Start the specified DHCP server.

dhcpserver restart
VBoxManage dhcpserver restart <‑‑network=netname | ‑‑interface=ifname>

Restarts the specified DHCP server. The DHCP server must be running.

dhcpserver stop
VBoxManage dhcpserver stop <‑‑network=netname | ‑‑interface=ifname>

Stops the specified DHCP server.

dhcpserver findlease
VBoxManage dhcpserver findlease <‑‑network=netname | ‑‑interface=ifname> <‑‑mac‑address=mac>

Performs a lease database lookup. This is mainly for getting the IP address of a running VM.

--mac-address=mac
The MAC address to lookup in the lease database.

Common DHCP Options:
1 - SubnetMask
IPv4 netmask. Set to the value of the --netmask option by default.

2 - TimeOffset
UTC offset in seconds (32-bit decimal value).

3 - Routers
Space separated list of IPv4 router addresses.

4 - TimeServers
Space separated list of IPv4 time server (RFC 868) addresses.

5 - NameServers
Space separated list of IPv4 name server (IEN 116) addresses.

6 - DomainNameServers
Space separated list of IPv4 DNS addresses.

7 - LogServers
Space separated list of IPv4 log server addresses.

8 - CookieServers
Space separated list of IPv4 cookie server (RFC 865) addresses.

9 - LPRServers
Space separated list of IPv4 line printer server (RFC 1179) addresses.

10 - ImpressServers
Space separated list of IPv4 imagen impress server addresses.

11 - ResourseLocationServers
Space separated list of IPv4 resource location (RFC 887) addresses.

12 - HostName
The client name. See RFC 1035 for character limits.

13 - BootFileSize
Number of 512 byte blocks making up the boot file (16-bit decimal value).

14 - MeritDumpFile
Client core file.

15 - DomainName
Domain name for the client.

16 - SwapServer
IPv4 address of the swap server that the client should use.

17 - RootPath
The path to the root disk the client should use.

18 - ExtensionPath
Path to a file containing additional DHCP options (RFC2123).

19 - IPForwarding
Whether IP forwarding should be enabled by the client (boolean).

20 - OptNonLocalSourceRouting
Whether non-local datagrams should be forwarded by the client (boolean)

21 - PolicyFilter
List of IPv4 addresses and masks pairs controlling non-local source routing.

22 - MaxDgramReassemblySize
The maximum datagram size the client should reassemble (16-bit decimal value).

23 - DefaultIPTTL
The default time-to-leave on outgoing (IP) datagrams (8-bit decimal value).

24 - PathMTUAgingTimeout
RFC1191 path MTU discovery timeout value in seconds (32-bit decimal value).

25 - PathMTUPlateauTable
RFC1191 path MTU discovery size table, sorted in ascending order (list of 16-bit decimal values).

26 - InterfaceMTU
The MTU size for the interface (16-bit decimal value).

27 - AllSubnetsAreLocal
Indicates whether the MTU size is the same for all subnets (boolean).

28 - BroadcastAddress
Broadcast address (RFC1122) for the client to use (IPv4 address).

29 - PerformMaskDiscovery
Whether to perform subnet mask discovery via ICMP (boolean).

30 - MaskSupplier
Whether to respond to subnet mask requests via ICMP (boolean).

31 - PerformRouterDiscovery
Whether to perform router discovery (RFC1256) (boolean).

32 - RouterSolicitationAddress
Where to send router solicitation requests (RFC1256) (IPv4 address).

33 - StaticRoute
List of network and router address pairs addresses.

34 - TrailerEncapsulation
Whether to negotiate the use of trailers for ARP (RTF893) (boolean).

35 - ARPCacheTimeout
The timeout in seconds for ARP cache entries (32-bit decimal value).

36 - EthernetEncapsulation
Whether to use IEEE 802.3 (RTF1042) rather than of v2 (RFC894) ethernet encapsulation (boolean).

37 - TCPDefaultTTL
Default time-to-live for TCP sends (non-zero 8-bit decimal value).

38 - TCPKeepaliveInterval
The interface in seconds between TCP keepalive messages (32-bit decimal value).

39 - TCPKeepaliveGarbage
Whether to include a byte of garbage in TCP keepalive messages for backward compatibility (boolean).

40 - NISDomain
The NIS (Sun Network Information Services) domain name (string).

41 - NISServers
Space separated list of IPv4 NIS server addresses.

42 - NTPServers
Space separated list of IPv4 NTP (RFC1035) server addresses.

43 - VendorSpecificInfo
Vendor specific information. Only accessible using --set-opt-hex.

44 - NetBIOSNameServers
Space separated list of IPv4 NetBIOS name server (NBNS) addresses (RFC1001,RFC1002).

45 - NetBIOSDatagramServers
Space separated list of IPv4 NetBIOS datagram distribution server (NBDD) addresses (RFC1001,RFC1002).

46 - NetBIOSNodeType
NetBIOS node type (RFC1001,RFC1002): 1=B-node, 2=P-node, 4=M-node, and 8=H-node (8-bit decimal value).

47 - NetBIOSScope
NetBIOS scope (RFC1001,RFC1002). Only accessible using --set-opt-hex.

48 - XWindowsFontServers
Space separated list of IPv4 X windows font server addresses.

49 - XWindowsDisplayManager
Space separated list of IPv4 X windows display manager addresses.

62 - NetWareIPDomainName
Netware IP domain name (RFC2242) (string).

63 - NetWareIPInformation
Netware IP information (RFC2242). Only accessible using --set-opt-hex.

64 - NISPlusDomain
The NIS+ domain name (string).

65 - NISPlusServers
Space separated list of IPv4 NIS+ server addresses.

66 - TFTPServerName
TFTP server name (string).

67 - BootfileName
Bootfile name (string).

68 - MobileIPHomeAgents
Space separated list of IPv4 mobile IP agent addresses.

69 - SMTPServers
Space separated list of IPv4 simple mail transport protocol (SMTP) server addresses.

70 - POP3Servers
Space separated list of IPv4 post office protocol 3 (POP3) server addresses.

71 - NNTPServers
Space separated list of IPv4 network news transport protocol (NTTP) server addresses.

72 - WWWServers
Space separated list of default IPv4 world wide web (WWW) server addresses.

73 - FingerServers
Space separated list of default IPv4 finger server addresses.

74 - IRCServers
Space separated list of default IPv4 internet relay chat (IRC) server addresses.

75 - StreetTalkServers
Space separated list of IPv4 StreetTalk server addresses.

76 - STDAServers
Space separated list of IPv4 StreetTalk directory assistance (STDA) server addresses.

78 - SLPDirectoryAgent
Address of one or more service location protocol (SLP) directory agents, and an indicator of whether their use is mandatory. Only accessible using --set-opt-hex.

79 - SLPServiceScope
List of service scopes for the service location protocol (SLP) and whether using the list is mandatory. Only accessible using --set-opt-hex.

119 - DomainSearch
Domain search list, see RFC3397 and section 4.1.4 in RFC1035 for encoding. Only accessible using --set-opt-hex.

VBoxManage discardstate
Discard the saved state of a virtual machine

Synopsis
VBoxManage discardstate <uuid | vmname>
Description
The VBoxManage discardstate command discards the saved state of a virtual machine (VM) that is not currently running. This command causes the VM's operating system to restart the next time you start the VM.

Note:
Where possible, avoid performing this action. The effects of this command are equivalent to unplugging the power cable on a physical machine.

uuid | vmname
Specifies the Universally Unique Identifier (UUID) or name of the VM.

Examples
The following command discards the saved state file for the VM called vm2. When you next start the VM, the VM's operating system is restarted.

$ VBoxManage discardstate vm2
See Also
VBoxManage adoptstate

VBoxManage encryptmedium
Manage a DEK-encrypted medium or image

Synopsis
VBoxManage encryptmedium <uuid | filename> [‑‑cipher=cipher‑ID] [‑‑newpassword=password] [‑‑newpasswordid=password‑ID] [‑‑oldpassword=password]
Description
The VBoxManage encryptmedium command enables you to create and manage a DEK-encrypted medium or image. You can encrypt an image, decrypt an image, and change the encryption password of an image. See Encrypting Disk Images.

uuid | filename
Specifies the Universally Unique Identifier (UUID) or the absolute path name of the medium or image to encrypt.

--newpassword=password
Specifies the new encryption password. password is either the absolute path name of a password file on the host operating system or -, which prompts you for the password.

You must use the --newpasswordid option with this --newpassword option.

--oldpassword=password
Specifies the original encryption password. password is either the absolute path name of a password file on the host operating system or -, which prompts you for the original password.

This option enables you to gain access to an encrypted medium or image to do the following:

Decrypt an encrypted image by using this option by itself.

Change the password of the encrypted image by using the --newpassword option.

Change the encryption cipher of the image by using the --cipher option.

--cipher=cipher-ID
Specifies the cipher to use for encryption. Valid values are AES-XTS128-PLAIN64 or AES-XTS256-PLAIN64.

This option enables you to set up or change encryption on the medium or image.

--newpasswordid=password-ID
Specifies a new password identifier that is used for correct identification when supplying multiple passwords during VM startup.

If you use the same password and password identifier when encrypting multiple images, you need to supply the password only one time during VM startup.

Examples
The following example shows how to encrypt the ol7u4-1.vdi image by using the AES-XTS128-PLAIN64 cipher, specifying a password identifier of 1001, and using the $HOME/pwfile password file:

$ VBoxManage encryptmedium "$HOME/VirtualBox VMs/ol7u4/ol7u4-1.vdi" \
  --cipher="AES-XTS128-PLAIN64" --newpasswordid="1001" --newpassword=$HOME/pwfile
The following example shows how to decrypt an encrypted image called ol7u4-2.vdi:

$ VBoxManage encryptmedium "$HOME/VirtualBox VMs/ol7u4/ol7u4-2.vdi" \
  --oldpassword=-
  Password: original-password
The following example shows how to change the password for an encrypted image called ol7u4-3.vdi. The command reads the original password from the $HOME/pwfile.orig file, reads the new password from the $HOME/pwfile file, and assigns a password identifier of 1001.

$ VBoxManage encryptmedium "$HOME/VirtualBox VMs/ol7u4/ol7u4-3.vdi" \
  --oldpassword=$HOME/pwfile.orig --newpassword=$HOME/pwfile --newpasswordid="1001"
VBoxManage encryptvm
Change encryption and passwords of the VM

Synopsis
VBoxManage encryptvm <uuid | vmname>setencryption‑‑old‑passwordfile‑‑ciphercipher‑identifier‑‑new‑passwordfile‑‑new‑password‑idpassword‑identifier‑‑force
VBoxManage encryptvm <uuid | vmname>checkpassword <file>
VBoxManage encryptvm <uuid | vmname>addpassword‑‑passwordfile‑‑password‑idpassword‑identifier
VBoxManage encryptvm <uuid | vmname>removepassword <password‑identifier>
Description
The VBoxManage encryptvm command enables you to change the encryption or add and remove user passwords for the virtual machine (VM). The following sections describe the subcommands that you can use:

Set encryption of the Virtual Machine
VBoxManage encryptvm <uuid | vmname>setencryption‑‑old‑passwordfile‑‑ciphercipher‑identifier‑‑new‑passwordfile‑‑new‑password‑idpassword‑identifier‑‑force

The VBoxManage encryptvm vmname setencryption command changes encryption of a VM.

Use the --old-password to supply old encryption password. Either specify the absolute pathname of a password file on the host operating system, or - to prompt you for the old password.

Use the --cipher option to specify the new cipher for encryption of the VM. Only AES-128 and AES-256 are supported. Appropriate mode GCM, CTR or XTS will be selected by VM depending on encrypting component.

Use the --new-password option to specify the new password for encryption of the VM. Either specify the absolute pathname of a password file on the host operating system, or - to prompt you for the new password.

Use the --new-password-id option to specify the new id for the password for encryption of the VM.

Use the --force option to make the system to reencrypt the VM instead of simple changing the password.

Check the supplied password is correct
VBoxManage encryptvm <uuid | vmname>checkpassword <file>

The VBoxManage encryptvm vmname checkpassword command checks the correctness of the supplied password.

The password can be supplied from file. Specify the absolute pathname of a password file on the host operating system. Also, you can specify - to prompt you for the password.

Add password for decrypting the Virtual Machine
VBoxManage encryptvm <uuid | vmname>addpassword‑‑passwordfile‑‑password‑idpassword‑identifier

The VBoxManage encryptvm vmname addpassword command adds a password for decrypting the VM.

Use the --password to supply the encryption password. Either specify the absolute pathname of a password file on the host operating system, or - to prompt you for the password.

Use the --password-id option to specify the id the password is supplied for.

Remove password used for decrypting the Virtual Machine
VBoxManage encryptvm <uuid | vmname>removepassword <password‑identifier>

The VBoxManage encryptvm vmname removepassword command removes a password used for decrypting the VM.

Specify the password identifier for removing. The password becomes unknown and the VM can not be decrypted.

Examples
The following command encrypts the ol7 VM using AES-256 giving password via command prompt:

$ VBoxManage encryptvm ol7 setencryption --cipher=AES-256 --new-password - --new-password-id vmid
See Also
VBoxManage createvm,

VBoxManage export
Export one or more virtual machines to a virtual appliance or to a cloud service

Synopsis
VBoxManage export <machines> <‑‑output=name> [‑‑legacy09 | ‑‑ovf09 | ‑‑ovf10 | ‑‑ovf20] [‑‑manifest] [‑‑options=manifest | iso | nomacs | nomacsbutnat...] [‑‑vsys=virtual‑system‑number] [‑‑description=description‑info] [‑‑eula=license‑text] [‑‑eulafile=filename] [‑‑product=product‑name] [‑‑producturl=product‑URL] [‑‑vendor=vendor‑name] [‑‑vendorurl=vendor‑URL] [‑‑version=version‑info] [‑‑vmname=vmname]
VBoxManage export <machine> <‑‑output=cloud‑service‑provider> [‑‑opc10] [‑‑vmname=vmname] [‑‑cloud=virtual‑system‑number] [‑‑cloudprofile=cloud‑profile‑name] [‑‑cloudshape=cloud‑shape‑name] [‑‑clouddomain=cloud‑domain] [‑‑clouddisksize=disk‑size‑in‑GB] [‑‑cloudbucket=bucket‑name] [‑‑cloudocivcn=OCI‑VCN‑ID] [‑‑cloudocisubnet=OCI‑subnet‑ID] [‑‑cloudkeepobject=true | false] [‑‑cloudlaunchinstance=true | false] [‑‑cloudlaunchmode=EMULATED | PARAVIRTUALIZED] [‑‑cloudpublicip=true | false]
Description
The VBoxManage export command enables you to export one or more virtual machines (VMs) from Oracle VirtualBox into to one of the following formats:

A virtual appliance in OVF format. This includes copying the VM's virtual disk images to compressed VMDK.

A cloud service such as Oracle Cloud Infrastructure. Exports a single VM.

For more information about exporting VMs from Oracle VirtualBox, see Importing and Exporting Virtual Machines.

Export a Virtual Machine to an OVF Virtual Appliance
VBoxManage export <machines> <‑‑output=name> [‑‑legacy09 | ‑‑ovf09 | ‑‑ovf10 | ‑‑ovf20] [‑‑manifest] [‑‑options=manifest | iso | nomacs | nomacsbutnat...] [‑‑vsys=virtual‑system‑number] [‑‑description=description‑info] [‑‑eula=license‑text] [‑‑eulafile=filename] [‑‑product=product‑name] [‑‑producturl=product‑URL] [‑‑vendor=vendor‑name] [‑‑vendorurl=vendor‑URL] [‑‑version=version‑info] [‑‑vmname=vmname]

The VBoxManage export command enables you to export a VM as a virtual appliance in OVF format.

machines
Specifies a comma-separated list of one or more machines to export to the same OVF file.

--output=filename
Specifies the target OVF file. The file can be OVF, OVA, or a ZIP file compressed with the gzip command. Because the directory that contains the target OVF file will also store the exported disk images in the compressed VMDK format, ensure that this directory has sufficient disk space in which to store the images.

The short form of this option is -o.

--legacy09
Exports in OVF 0.9 legacy mode if the virtualization product is not fully compatible with the OVF 1.0 standard.

--ovf09
Exports in OVF 0.9 format.

--ovf10
Exports in OVF 1.0 format.

--ovf20
Exports in OVF 2.0 format.

--manifest
Creates a manifest of the exported files.

--options=argument,...
Specifies information to control the exact content of the appliance file. Specify one or more comma-separated arguments:

manifest
Produces a manifest file that detects corrupted appliances on import.

iso
Exports DVD images in an ISO file.

nomacs
Excludes all MAC addresses.

nomacsbutnat
Excludes all MAC addresses except for those in a NAT network.

--description=description-info
Specifies a description of the VM.

--eula=license-text
Specifies an end-user license text.

--eulafile=filename
Specifies an end-user license file.

--product=product-name
Specifies a product name.

--producturl=product-URL
Specifies a product URL.

--vendor=vendor-name
Specifies a vendor name.

--vendorurl=vendor-URL
Specifies a vendor URL.

--version=version-info
Specifies version information.

--vmname=vmname
Specifies the name of the VM to export.

--vsys=virtual-system-number
Specifies the number of the virtual system.

Export a Virtual Machine to Oracle Cloud Infrastructure
VBoxManage export <machine> <‑‑output=cloud‑service‑provider> [‑‑opc10] [‑‑vmname=vmname] [‑‑cloud=virtual‑system‑number] [‑‑cloudprofile=cloud‑profile‑name] [‑‑cloudshape=cloud‑shape‑name] [‑‑clouddomain=cloud‑domain] [‑‑clouddisksize=disk‑size‑in‑GB] [‑‑cloudbucket=bucket‑name] [‑‑cloudocivcn=OCI‑VCN‑ID] [‑‑cloudocisubnet=OCI‑subnet‑ID] [‑‑cloudkeepobject=true | false] [‑‑cloudlaunchinstance=true | false] [‑‑cloudlaunchmode=EMULATED | PARAVIRTUALIZED] [‑‑cloudpublicip=true | false]

The VBoxManage export command enables you to export a VM to a cloud service provider such as Oracle Cloud Infrastructure. By default, the exported disk image is converted into compressed VMDK format. This minimizes the amount of data to transfer to the cloud service.

Some of the following options are configuration settings for the VM instance. As a result, specify an Oracle Cloud Identifier (OCID) for a resource. Use the Oracle Cloud Infrastructure Console to view OCIDs.

--output=cloud-service-provider
Specifies the short name of the cloud service provider to which you export the VM. For Oracle Cloud Infrastructure, specify OCI://.

The short form of this option is -o.

--opc10
Exports in Oracle Cloud Infrastructure format.

--cloud=number-of-virtual-system
Specifies a number that identifies the VM to export. Numbering starts at 0 for the first VM.

--vmname=vmname
Specifies the name of the exported VM, which is used as the VM instance name in Oracle Cloud Infrastructure.

--cloudprofile=cloud-profile-name
Specifies the cloud profile to use to connect to the cloud service provider. The cloud profile contains your Oracle Cloud Infrastructure account details, such as your user OCID and the fingerprint for your public key.

To use a cloud profile, you must have the required permissions on Oracle Cloud Infrastructure.

--cloudshape=cloud-shape-name
Specifies the shape used by the VM instance. The shape defines the number of CPUs and the amount of memory that is allocated to the VM instance. Ensure that the shape is compatible with the exported image.

--clouddomain=cloud-domain
Specifies the availability domain to use for the VM instance. Enter the full name of the availability domain.

--clouddisksize=disk-size-in-GB
Specifies the amount of disk space, in gigabytes, to use for the exported disk image. Valid values are from 50 GB to 300 GB.

--cloudbucket=bucket-name
Specifies the bucket in which to store uploaded files. In Oracle Cloud Infrastructure, a bucket is a logical container for storing objects.

--cloudocivcn=OCI-VCN-ID
Specifies the OCID of the virtual cloud network (VCN) to use for the VM instance.

--cloudocisubnet=OCI-subnet-ID
Specifies the OCID of the VCN subnet to use for the VM instance.

--cloudkeepobject=true | false
Specifies whether to store the exported disk image in Oracle Object Storage.

--cloudlaunchinstance=true | false
Specifies whether to start the VM instance after the export to Oracle Cloud Infrastructure completes.

--cloudlaunchinstance=EMULATED | PARAVIRTUALIZED
Specifies the launch mode used for the instance. Paravirtualized mode gives improved performance.

--cloudpublicip=true | false
Specifies whether to enable a public IP address for the VM instance.

Example
The following example shows how to export the myVM VM to Oracle Cloud Infrastructure. The command's option arguments describe the configuration of the myVM_Cloud VM in Oracle Cloud Infrastructure.

# VBoxManage export myVM --output=OCI:// --cloud=0 --vmname=myVM_Cloud \
--cloudprofile="standard user" --cloudbucket=myBucket \
--cloudshape=VM.Standard2.1 --clouddomain=US-ASHBURN-AD-1 --clouddisksize=50  \
--cloudocivcn=ocid1.vcn.oc1.iad.aaaa... --cloudocisubnet=ocid1.subnet.oc1.iad.aaaa... \
--cloudkeepobject=true --cloudlaunchinstance=true --cloudpublicip=true
VBoxManage extpack
Extension package management

Synopsis
VBoxManage extpack install [‑‑replace] [‑‑accept‑license=sha256] <tarball>
VBoxManage extpack uninstall [‑‑force] <name>
VBoxManage extpack cleanup
Description
extpack install
VBoxManage extpack install [‑‑replace] [‑‑accept‑license=sha256] <tarball>

Installs a new extension pack on the system. This command will fail if an older version of the same extension pack type is already installed and the --replace isn't supplied. The --replace option uninstalls any previously installed extension packs of the same type being installed before the new extension pack is installed.

--replace
Uninstall existing extension pack before installing the specified extension pack.

--accept-license=sha256
Accept the license text with the given SHA-256 hash value.

VBoxManage will display the SHA-256 value when performing a manual installation. The hash can be calculated manually by extracting the license file from the extension pack using tar(1) and running sha256sum or similar to generate the hash value.

tarball
The file containing the extension pack to be installed.

extpack uninstall
VBoxManage extpack uninstall [‑‑force] <name>

Uninstalls an extension pack from the system. The subcommand will also succeed in the case where the specified extension pack is not present on the system. You can use VBoxManage list extpacks to list the names of the extension packs which are currently installed.

--force
Overrides most refusals to uninstall an extension pack

name
The name of the extension pack to be uninstalled.

extpack cleanup
VBoxManage extpack cleanup

Used to remove temporary files and directories that may have been left behind if a previous install or uninstall command failed.

Examples
How to list extension packs:
$ VBoxManage list extpacks
Extension Packs: 1
Pack no. 0:   Oracle VirtualBox Extension Pack
Version:      4.1.12
Revision:     77218
Edition:
Description:  USB 2.0 Host Controller, VirtualBox RDP, PXE ROM with E1000 support.
VRDE Module:  VBoxVRDP
Usable:       true
Why unusable:
How to remove an extension pack:
$ VBoxManage extpack uninstall "Oracle VirtualBox Extension Pack"
0%...10%...20%...30%...40%...50%...60%...70%...80%...90%...100%
Successfully uninstalled "Oracle VirtualBox Extension Pack".
VBoxManage getextradata
View keyword values that are associated with a virtual machine or configuration

Synopsis
VBoxManage getextradata <global | uuid | vmname> <keyword | enumerate>
Description
The VBoxManage getextradata command enables you to retrieve keyword data that is associated with a virtual machine (VM) or with an Oracle VirtualBox configuration.

global
Specifies to retrieve information about the configuration rather than a VM.

uuid | vmname
Specifies the Universally Unique Identifier (UUID) or name of the VM.

enumerate
Shows all keyword values for the specified VM or configuration.

keyword
Specifies the keyword for which to retrieve its value.

Examples
The following command retrieves the installdate keyword value for the Fedora5 VM:

$ VBoxManage getextradata Fedora5 installdate
Value: 2006.01.01
The following command retrieves the information for all keywords of the OracleLinux7u4 VM:

$ VBoxManage getextradata OracleLinux7u4 enumerate
Key: GUI/LastCloseAction, Value: PowerOff
Key: GUI/LastGuestSizeHint, Value: 1048,696
Key: GUI/LastNormalWindowPosition, Value: 851,286,1048,738
The following command retrieves the information for all keywords in the configuration:

$ VBoxManage getextradata global enumerate
Key: GUI/LastItemSelected, Value: m=OracleLinux7u4
Key: GUI/LastWindowPosition, Value: 951,510,960,520
Key: GUI/SplitterSizes, Value: 318,637
Key: GUI/Toolbar/MachineTools/Order, Value: Details
Key: GUI/Tools/LastItemsSelected, Value: Welcome,Details
Key: GUI/UpdateCheckCount, Value: 71
Key: GUI/UpdateDate, Value: 1 d, 2022-10-10, stable, 7.0.0
Key: GUI/VirtualMediaManager/Details/Expanded, Value: true
See Also
VBoxManage setextradata

VBoxManage guestcontrol
Control a virtual machine from the host system

Synopsis
VBoxManage guestcontrol <uuid | vmname>run [‑‑arg0=argument 0] [‑‑domain=domainname] [‑‑dos2unix] [‑‑exe=filename] [‑‑ignore‑orphaned‑processes] [‑‑no‑wait‑stderr | ‑‑wait‑stderr] [‑‑no‑wait‑stdout | ‑‑wait‑stdout] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑profile] [‑‑putenv=var‑name=[value]] [‑‑quiet] [‑‑timeout=msec] [‑‑unix2dos] [‑‑unquoted‑args] [‑‑username=username] [‑‑cwd=directory] [‑‑verbose] <‑‑[argument...]>
VBoxManage guestcontrol <uuid | vmname>start [‑‑arg0=argument 0] [‑‑domain=domainname] [‑‑exe=filename] [‑‑ignore‑orphaned‑processes] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑profile] [‑‑putenv=var‑name=[value]] [‑‑quiet] [‑‑timeout=msec] [‑‑unquoted‑args] [‑‑username=username] [‑‑cwd=directory] [‑‑verbose] <‑‑[argument...]>
VBoxManage guestcontrol <uuid | vmname>copyfrom [‑‑dereference] [‑‑domain=domainname] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑quiet] [‑‑no‑replace] [‑‑recursive] [‑‑target‑directory=host‑destination‑dir] [‑‑update] [‑‑username=username] [‑‑verbose] <guest‑source0>guest‑source1[...] <host‑destination>
VBoxManage guestcontrol <uuid | vmname>copyto [‑‑dereference] [‑‑domain=domainname] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑quiet] [‑‑no‑replace] [‑‑recursive] [‑‑target‑directory=guest‑destination‑dir] [‑‑update] [‑‑username=username] [‑‑verbose] <host‑source0>host‑source1[...]
VBoxManage guestcontrol <uuid | vmname>mkdir [‑‑domain=domainname] [‑‑mode=mode] [‑‑parents] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑quiet] [‑‑username=username] [‑‑verbose] <guest‑directory...>
VBoxManage guestcontrol <uuid | vmname>rmdir [‑‑domain=domainname] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑quiet] [‑‑recursive] [‑‑username=username] [‑‑verbose] <guest‑directory...>
VBoxManage guestcontrol <uuid | vmname>rm [‑‑domain=domainname] [‑‑force] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑quiet] [‑‑username=username] [‑‑verbose] <guest‑directory...>
VBoxManage guestcontrol <uuid | vmname>mv [‑‑domain=domainname] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑quiet] [‑‑username=username] [‑‑verbose] <source...> <destination‑directory>
VBoxManage guestcontrol <uuid | vmname>mktemp [‑‑directory] [‑‑domain=domainname] [‑‑mode=mode] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑quiet] [‑‑secure] [‑‑tmpdir=directory‑name] [‑‑username=username] [‑‑verbose] <template‑name>
VBoxManage guestcontrol <uuid | vmname>mount [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑username=username] [‑‑verbose]
VBoxManage guestcontrol <uuid | vmname>fsinfo [‑‑domain=domainname] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑human‑readable] [‑‑quiet] [‑‑total] [‑‑username=username] [‑‑verbose] <path>
VBoxManage guestcontrol <uuid | vmname>stat [‑‑domain=domainname] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑quiet] [‑‑username=username] [‑‑verbose] <filename>
VBoxManage guestcontrol <uuid | vmname>list <all | files | processes | sessions> [‑‑quiet] [‑‑verbose]
VBoxManage guestcontrol <uuid | vmname>closeprocess [‑‑session‑id=ID | ‑‑session‑name=name‑or‑pattern] [‑‑quiet] [‑‑verbose] <PID...>
VBoxManage guestcontrol <uuid | vmname>closesession [‑‑all | ‑‑session‑id=ID | ‑‑session‑name=name‑or‑pattern] [‑‑quiet] [‑‑verbose]
VBoxManage guestcontrol <uuid | vmname>updatega [‑‑quiet] [‑‑verbose] [‑‑source=guest‑additions.ISO] [‑‑wait‑start] [‑‑[argument...]]
VBoxManage guestcontrol <uuid | vmname>watch [‑‑quiet] [‑‑verbose]
Description
The VBoxManage guestcontrol command enables you to control a guest (VM) from the host system. See Guest Control of Applications.

Common Options and Operands
The following options can be used by any of the VBoxManage guestcontrol subcommands:

uuid | vmname
Specifies the Universally Unique Identifier (UUID) or name of the VM.

--quiet
Specifies that the command produce quieter output.

The short form of this option is -q.

--verbose
Specifies that the command produce more detailed output.

The short form of this option is -v.

Some of the VBoxManage guestcontrol subcommands require that you provide guest credentials for authentication. The subcommands are: copyfrom, copyto, fsinfo, mkdir, mktemp, mount, mv, rmdir, rm, run, start, and stat.

While you cannot perform anonymous executions, a user account password is optional and depends on the guest's OS security policy. If a user account does not have an associated password, specify an empty password. On OSes such as Windows, you might need to adjust the security policy to permit user accounts with an empty password. In addition, global domain rules might apply and therefore cannot be changed.

The following options are used for authentication on the guest VM:

--domain=domainname
Specifies the user domain for Windows guest VMs.

--password=password
Specifies the password for the specified user. If you do not specify a password on the command line or if the password is empty, the specified user needs to have an empty password.

--passwordfile=filename
Specifies the absolute path to a file on the host system that contains the password for the specified user. If the password file is empty or if you do not specify a password on the command line, the specified user needs to have an empty password.

--username=username
Specifies an existing user in the guest OS that runs the process. If unspecified, the name of the user on the host invoking the VBoxManage command will be used as the username for the guest OS login.

Guest Process Restrictions
By default, you can run up to 255 guest processes simultaneously. If attempting to start a new guest process would exceed this limit and there are inactive guest processes then the the oldest inactive guest process is discarded to run the new process. You cannot retrieve the output from a discarded guest process. If all 255 guest processes are active and running, attempting to start a new guest process fails.

Running Graphical Applications Inside a Guest
If using the VBoxManage guestcontrol run or VBoxManage guestcontrol start commands to execute a graphical application then the guest will need to be running a graphical windowing environment typically as the same user executing the GUI application. For example on Windows guests the user running the GUI application in the guest needs to be currently logged in with a running desktop session. Similarly on UNIX and UNIX-like guests a graphical windowing system such as X11 or Wayland needs to be running and the user executing the GUI application needs authorization to be able to connect to the display server.

Run a Command in the guest
VBoxManage guestcontrol <uuid | vmname>run [‑‑arg0=argument 0] [‑‑domain=domainname] [‑‑dos2unix] [‑‑exe=filename] [‑‑ignore‑orphaned‑processes] [‑‑no‑wait‑stderr | ‑‑wait‑stderr] [‑‑no‑wait‑stdout | ‑‑wait‑stdout] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑profile] [‑‑putenv=var‑name=[value]] [‑‑quiet] [‑‑timeout=msec] [‑‑unix2dos] [‑‑unquoted‑args] [‑‑username=username] [‑‑cwd=directory] [‑‑verbose] <‑‑[argument...]>

The VBoxManage guestcontrol vmname run command enables you to execute a program in the guest VM. Standard input, standard output, and standard error are redirected from the VM to the host system until the program completes.

--exe=path-to-executable
Specifies the absolute path of the executable program to run in the guest VM. For example: C:\Windows\System32\calc.exe.

--arg0=alternate-executable-name
Specifies an alternate name (i.e. arg0 or argv[0]) for the executable being run in the guest.

--cwd=path-to-directory
Specifies the absolute path of a directory in which to start the program. Optional. The directory must exist and be accessible to the guest user. For example: C:\Users\<USER>\work_area.

The short form of this option is -C.

--timeout=msec
Specifies the maximum amount of time, in milliseconds, that the program can run. While the program runs, VBoxManage receives its output.

If you do not specify a timeout value, VBoxManage waits indefinitely for the process to end, or for an error to occur.

--putenv=NAME=[value]
Sets, modifies, and unsets environment variables in the guest VM environment.

When you create a guest process, it runs with the default standard guest OS environment. Use this option to modify environment variables in that default environment.

Use the --putenv=NAME=[value] option to set or modify the environment variable specified by NAME.

Use the --putenv=NAME= option to unset the environment variable specified by NAME.

Ensure that any environment variable name or value that includes spaces is enclosed by quotes.

Specify a --putenv option for each environment variable that you want to modify.

The short form of this option is -E.

--unquoted-args
Disables the escaped double quoting of arguments that you pass to the program. For example, \"fred\".

--ignore-orphaned-processes
Ignores orphaned processes. Not yet implemented.

--profile
Uses a shell profile to specify the environment to use. Not yet implemented.

--no-wait-stdout
Does not wait for the guest process to end or receive its exit code and any failure explanation.

--wait-stdout
Waits for the guest process to end to receive its exit code and any failure explanation. The VBoxManage command receives the standard output of the guest process while the process runs.

--no-wait-stderr
Does not wait for the guest process to end to receive its exit code, error messages, and flags.

--wait-stderr
Waits for the guest process to end to receive its exit code, error messages, and flags. The VBoxManage command receives the standard error of the guest process while the process runs.

--dos2unix
Transform DOS or Windows guest output to UNIX or Linux output. This transformation changes CR + LF line endings to LF. Not yet implemented.

--unix2dos
Transform UNIX or Linux guest output to DOS or Windows output. This transformation changes LF line endings to CR + LF.

-- [argument...]
Specifies one or more arguments to pass to the program being run in the guest.

Ensure that any command argument that includes spaces is enclosed by quotes.

Start a Command on the guest
VBoxManage guestcontrol <uuid | vmname>start [‑‑arg0=argument 0] [‑‑domain=domainname] [‑‑exe=filename] [‑‑ignore‑orphaned‑processes] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑profile] [‑‑putenv=var‑name=[value]] [‑‑quiet] [‑‑timeout=msec] [‑‑unquoted‑args] [‑‑username=username] [‑‑cwd=directory] [‑‑verbose] <‑‑[argument...]>

The VBoxManage guestcontrol vmname start command enables you to execute a guest program and returns after the command has been successfully started. The invoked command doesn't wait until all data from stdout or stderr has been read before returning.

Copy a file from the guest to the host.
VBoxManage guestcontrol <uuid | vmname>copyfrom [‑‑dereference] [‑‑domain=domainname] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑quiet] [‑‑no‑replace] [‑‑recursive] [‑‑target‑directory=host‑destination‑dir] [‑‑update] [‑‑username=username] [‑‑verbose] <guest‑source0>guest‑source1[...] <host‑destination>

The VBoxManage guestcontrol vmname copyfrom command enables you to copy a file from the guest VM to the host system.

--dereference
Enables following of symbolic links on the guest file system.

--no-replace
Only copies a file if it does not exist on the host yet.

The short form of this option is -n.

--recursive
Recursively copies files and directories from the specified guest directory to the host.

The short form of this option is -R.

--target-directory=host-dst-dir
Specifies the absolute path of the destination directory on the host system. For example, C:\Temp.

--update
Only copies a file if the guest file is newer than on the host.

The short form of this option is -u.

guest-source0 [guest-source1 [...]]
Specifies the absolute path of one or more files to copy from the guest VM. For example, C:\Windows\System32\calc.exe. You can use wildcards to specify multiple files. For example, C:\Windows\System*\*.dll.

Copy a file from the host to the guest.
VBoxManage guestcontrol <uuid | vmname>copyto [‑‑dereference] [‑‑domain=domainname] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑quiet] [‑‑no‑replace] [‑‑recursive] [‑‑target‑directory=guest‑destination‑dir] [‑‑update] [‑‑username=username] [‑‑verbose] <host‑source0>host‑source1[...]

The VBoxManage guestcontrol vmname copyto command enables you to copy a file from the host system to the guest VM.

--dereference
Enables following of symbolic links on the host system.

--no-replace
Only copies a file if it does not exist on the guest yet.

The short form of this option is -n.

--recursive
Recursively copies files and directories from the specified host directory to the guest.

The short form of this option is -R.

--target-directory=guest-dst-dir
Specifies the absolute path of the destination directory on the guest. For example, /home/<USER>/fromhost.

--update
Only copies a file if the host file is newer than on the guest.

The short form of this option is -u.

host-source0 [host-source1 [...]]
Specifies the absolute path of a file to copy from the host system. For example, C:\Windows\System32\calc.exe. You can use wildcards to specify multiple files. For example, C:\Windows\System*\*.dll.

Show guest filesystem information.
VBoxManage guestcontrol <uuid | vmname>fsinfo [‑‑domain=domainname] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑human‑readable] [‑‑quiet] [‑‑total] [‑‑username=username] [‑‑verbose] <path>

The VBoxManage guestcontrol vmname fsinfo command enables you to show filesystem information of the guest VM.

An alternate form of this subcommand is df.

--human-readable
Shows the disk sizes in a human readable form.

--total
Produces a grand total of all disk sizes.

guest-path [guest-path...]
Specifies an absolute path to show guest filesystem information for.

Create a directory on the guest.
VBoxManage guestcontrol <uuid | vmname>mkdir [‑‑domain=domainname] [‑‑mode=mode] [‑‑parents] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑quiet] [‑‑username=username] [‑‑verbose] <guest‑directory...>

The VBoxManage guestcontrol vmname mkdir command enables you to create one or more directories on the guest VM.

Alternate forms of this subcommand are md, createdir, and createdirectory.

--parents
Creates any of the missing parent directories of the specified directory.

For example, if you attempt to create the D:\Foo\Bar directory and the D:\Foo directory does not exist, using the --parents creates the missing D:\Foo directory. However, if you attempt to create the D:\Foo\Bar and do not specify the --parents option, the command fails.

--mode=mode
Specifies the permission mode to use for the specified directory. If you specify the --parents option, the mode is used for the associated parent directories, as well. mode is a four-digit octal mode such as 0755.

guest-dir [guest-dir...]
Specifies an absolute path of one or more directories to create on the guest VM. For example, D:\Foo\Bar.

If all of the associated parent directories do not exist on the guest VM, you must specify the --parents option.

You must have sufficient rights on the guest VM to create the specified directory and its parent directories.

Remove a directory from the guest.
VBoxManage guestcontrol <uuid | vmname>rmdir [‑‑domain=domainname] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑quiet] [‑‑recursive] [‑‑username=username] [‑‑verbose] <guest‑directory...>

The VBoxManage guestcontrol vmname rmdir command enables you to delete the specified directory from the guest VM.

Alternate forms of this subcommand are removedir and removedirectory.

--recursive
Recursively removes directories from the specified from the guest VM.

The short form of this option is -R.

guest-dir [guest-dir...]
Specifies an absolute path of one or more directories to remove from the guest VM. You can use wildcards to specify the directory names. For example, D:\Foo\*Bar.

You must have sufficient rights on the guest VM to remove the specified directory and its parent directories.

Remove a file from the guest.
VBoxManage guestcontrol <uuid | vmname>rm [‑‑domain=domainname] [‑‑force] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑quiet] [‑‑username=username] [‑‑verbose] <guest‑directory...>

The VBoxManage guestcontrol vmname rm command enables you to delete the specified files from the guest VM.

The alternate form of this subcommand is removefile.

--force
Forces the operation and overrides any confirmation requests.

The short form of this option is -f.

guest-file [guest-file...]
Specifies an absolute path of one or more files to remove from the guest VM. You can use wildcards to specify the file names. For example, D:\Foo\Bar\text*.txt.

You must have sufficient rights on the guest VM to remove the specified file.

Rename a file or Directory on the guest
VBoxManage guestcontrol <uuid | vmname>mv [‑‑domain=domainname] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑quiet] [‑‑username=username] [‑‑verbose] <source...> <destination‑directory>

The VBoxManage guestcontrol vmname mv command enables you to rename files and directories on the guest VM.

Alternate forms of this subcommand are move, ren, and rename.

guest-source [guest-source...]
Specifies an absolute path of a file or a single directory to move or rename on the guest VM. You can use wildcards to specify the file names.

You must have sufficient rights on the guest VM to access the specified file or directory.

dest
Specifies the absolute path of the renamed file or directory, or the destination directory to which to move the files. If you move only one file, dest can be a file or a directory, otherwise dest must be a directory.

You must have sufficient rights on the guest VM to access the destination file or directory.

Create a Temporary File or Directory on the guest
VBoxManage guestcontrol <uuid | vmname>mktemp [‑‑directory] [‑‑domain=domainname] [‑‑mode=mode] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑quiet] [‑‑secure] [‑‑tmpdir=directory‑name] [‑‑username=username] [‑‑verbose] <template‑name>

The VBoxManage guestcontrol vmname mktemp command enables you to create a temporary file or temporary directory on the guest VM. You can use this command to assist with the subsequent copying of files from the host system to the guest VM. By default, this command creates the file or directory in the guest VM's platform-specific temp directory.

Alternate forms of this subcommand are createtemp and createtemporary.

--directory
Creates a temporary directory that is specified by the template operand.

--secure
Enforces secure file and directory creation by setting the permission mode to 0755. Any operation that cannot be performed securely fails.

--mode=mode
Specifies the permission mode to use for the specified directory. mode is a four-digit octal mode such as 0755.

--tmpdir=directory
Specifies the absolute path of the directory on the guest VM in which to create the specified file or directory. If unspecified, directory is the platform-specific temp directory.

template
Specifies a template file name for the temporary file, without a directory path. The template file name must contain at least one sequence of three consecutive X characters, or must end in X.

Shows mount points on the guest
VBoxManage guestcontrol <uuid | vmname>mount [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑username=username] [‑‑verbose]

The VBoxManage guestcontrol vmname mount command enables you to the current mount points on the guest VM. For Windows guests this shows the mapped drives.

Show a file or File System Status on the guest
VBoxManage guestcontrol <uuid | vmname>stat [‑‑domain=domainname] [‑‑passwordfile=password‑file | ‑‑password=password] [‑‑quiet] [‑‑username=username] [‑‑verbose] <filename>

The VBoxManage guestcontrol vmname stat command enables you to show the status of files or file systems on the guest VM.

file [file ...]
Specifies an absolute path of a file or file system on the guest VM. For example, /home/<USER>/a.out.

You must have sufficient rights on the guest VM to access the specified files or file systems.

List the Configuration and Status Information for a Guest Virtual Machine
VBoxManage guestcontrol <uuid | vmname>list <all | files | processes | sessions> [‑‑quiet] [‑‑verbose]

The VBoxManage guestcontrol vmname list command enables you to list guest control configuration and status information. For example, the output shows open guest sessions, guest processes, and files.

all|sessions|processes|files
Indicates the type of information to show. all shows all available data, sessions shows guest sessions, processes shows processes, and files shows files.

Terminate a Process in a guest Session
VBoxManage guestcontrol <uuid | vmname>closeprocess [‑‑session‑id=ID | ‑‑session‑name=name‑or‑pattern] [‑‑quiet] [‑‑verbose] <PID...>

The VBoxManage guestcontrol vmname closeprocess command enables you to terminate a guest process that runs in a guest session. Specify the process by using a process identifier (PID) and the session by using the session ID or name.

--session-id=ID
Specifies the ID of the guest session.

--session-name=name|pattern
Specifies the name of the guest session. Use a pattern that contains wildcards to specify multiple sessions.

PID [PID ...]
Specifies the list of PIDs of guest processes to terminate.

Close a guest Session
VBoxManage guestcontrol <uuid | vmname>closesession [‑‑all | ‑‑session‑id=ID | ‑‑session‑name=name‑or‑pattern] [‑‑quiet] [‑‑verbose]

The VBoxManage guestcontrol vmname closesession command enables you to close a guest session. Specify the guest session either by session ID or by name.

--session-id=ID
Specifies the ID of the guest session.

--session-name=name|pattern
Specifies the name of the guest session. Use a pattern that contains wildcards to specify multiple sessions.

--all
Closes all guest sessions.

Update the Guest Additions Software on the guest
VBoxManage guestcontrol <uuid | vmname>updatega [‑‑quiet] [‑‑verbose] [‑‑source=guest‑additions.ISO] [‑‑wait‑start] [‑‑[argument...]]

The VBoxManage guestcontrol vmname updatega command enables you to update the Guest Additions software installed in the specified guest VM.

Alternate forms of this subcommand are updateadditions and updateguestadditions.

--source=new-iso-path
Specifies the absolute path of the Guest Additions update .ISO file on the guest VM.

--reboot
Automatically reboots the guest after a successful Guest Additions update.

--timeout=ms
Sets the timeout (in ms) to wait for the overall Guest Additions update to complete. By default no timeout is being used.

--verify
Verifies whether the Guest Additions were updated successfully after a successful installation. A guest reboot is mandatory.

--wait-ready
Waits for the current Guest Additions being ready to handle the Guest Additions update.

--wait-start
Starts the VBoxManage update process on the guest VM and then waits for the Guest Additions update to begin before terminating the VBoxManage process.

By default, the VBoxManage command waits for the Guest Additions update to complete before it terminates. Use this option when a running VBoxManage process affects the interaction between the installer and the guest OS.

-- argument [argument ...]
Specifies optional command-line arguments to pass to the Guest Additions updater. You might use the -- option to pass the appropriate updater arguments to retrofit features that are not yet installed.

Ensure that any command argument that includes spaces is enclosed by quotes.

Wait for a guest run level
The VBoxManage guestcontrol vmname waitrunlevel command enables you to wait for a guest run level being reached.

--timeout=ms
Sets the timeout (in ms) to wait for reaching the run level. By default no timeout is being used.

system|userland|desktop
Specifies the run level to wait for.

Show Current Guest Control Activity
VBoxManage guestcontrol <uuid | vmname>watch [‑‑quiet] [‑‑verbose]

The VBoxManage guestcontrol vmname watch command enables you to show current guest control activity.

Examples
The following VBoxManage guestcontrol run command executes the ls -l /usr command on the My OL VM Oracle Linux VM as the user1 user.

$ VBoxManage --nologo guestcontrol "My OL VM" run --exe "/bin/ls" \
--username user1 --passwordfile pw.txt --wait-stdout -- -l /usr
The --exe option specifies the absolute path of the command to run in the guest VM, /bin/ls. Use the -- option to pass any arguments that follow it to the ls command.

Use the --username option to specify the user name, user1 and use the --passwordfile option to specify the name of a file that includes the password for the user1 user, pw.txt.

The --wait-stdout option waits for the ls guest process to complete before providing the exit code and the command output. The --nologo option suppresses the output of the logo information.

The following VBoxManage guestcontrol run command executes the ipconfig command on the My Win VM Windows VM as the user1 user. Standard input, standard output, and standard error are redirected from the VM to the host system until the program completes.

$ VBoxManage --nologo guestcontrol "My Win VM" run \
--exe "c:\\windows\\system32\\ipconfig.exe" \
--username user1 --passwordfile pw.txt --wait-stdout
The --exe specifies the absolute path of command to run in the guest VM, c:\windows\system32\ipconfig.exe. The double backslashes shown in this example are required only on UNIX host systems.

Use the --username option to specify the user name, user1 and use the --passwordfile option to specify the name of a file that includes the password for the user1 user, pw.txt.

The --wait-stdout option waits for the ls guest process to complete before providing the exit code and the command output. The --nologo option to suppress the output of the logo information.

The following VBoxManage guestcontrol start command executes the ls -l /usr command on the My OL VM Oracle Linux VM until the program completes.

$ VBoxManage --nologo guestcontrol "My Win VM" start \
--exe "c:\\windows\\system32\\ipconfig.exe" \
--username user1 --passwordfile pw.txt
The following VBoxManage guestcontrol run command executes a /usr/bin/busybox -l /usr command on the My OL VM Oracle Linux VM as the user1 user, explicitly using ls as argument 0.

$ VBoxManage --nologo guestcontrol "My OL VM" run --exe "/usr/bin/busybox" \
--username user1 --passwordfile pw.txt --wait-stdout --arg0 ls -- -l /usr
The --exe option specifies the absolute path of the command to run in the guest VM, /usr/bin/busybox. Use the -- option to pass any arguments that follow it to the busybox command.

Use the --username option to specify the user name, user1 and use the --passwordfile option to specify the name of a file that includes the password for the user1 user, pw.txt.

The --wait-stdout option waits for the ls guest process to complete before providing the exit code and the command output. The --nologo option suppresses the output of the logo information.

The --arg0 option explicitly specifies the argument 0 to use for the command to execute.

The default value of argument 0 (i.e. arg0 or argv[0]) is the value from the --exe option unless the --arg0 option has been supplied.

VBoxManage guestproperty
Manage virtual machine guest properties

Synopsis
VBoxManage guestproperty get <uuid | vmname> <property‑name> [‑‑verbose]
VBoxManage guestproperty enumerate <uuid | vmname> [‑‑no‑timestamp] [‑‑no‑flags] [‑‑relative] [‑‑old‑format] [patterns...]
VBoxManage guestproperty set <uuid | vmname> <property‑name> [property‑value [‑‑flags=flags]]
VBoxManage guestproperty unset <uuid | vmname> <property‑name>
VBoxManage guestproperty wait <uuid | vmname> <patterns> [‑‑timeout=msec] [‑‑fail‑on‑timeout]
Description
The VBoxManage guestproperty command enables you to set or retrieve the properties of a running virtual machine (VM). See Guest Properties. Guest properties are arbitrary name-value string pairs that can be written to and read from by either the guest or the host. As a result, these properties can be used as a low-overhead communication channel for strings provided that a guest is running and has the Guest Additions installed. In addition, the Guest Additions automatically set and maintain values whose keywords begin with /VirtualBox/.

General Command Operand
uuid | vmname
Specifies the Universally Unique Identifier (UUID) or name of the VM.

List All Properties for a Virtual Machine
VBoxManage guestproperty enumerate <uuid | vmname> [‑‑no‑timestamp] [‑‑no‑flags] [‑‑relative] [‑‑old‑format] [patterns...]

The VBoxManage guestproperty enumerate command lists each guest property and value for the specified VM. Note that the output is limited if the guest's property store is not being updated, for example because the VM is not running or because the Oracle VirtualBox Guest Additions are not installed.

--relative
Display the timestamp relative to current time.

--no-timestamp
Do not display the timestamp of the last update.

--no-flags
Do not display the flags.

--old-format
Use the output format from VirtualBox 6.1 and earlier.

pattern
Filters the list of properties based on the specified pattern, which can contain the following wildcard characters:

* (asterisk)
Represents any number of characters. For example, the /VirtualBox* pattern matches all properties that begin with /VirtualBox.

? (question mark)
Represents a single arbitrary character. For example, the fo? pattern matches both foo and for.

| (pipe)
Specifies multiple alternative patterns. For example, the s*|t* pattern matches any property that begins with s or t.

Retrieve a Property Value for a Virtual Machine
VBoxManage guestproperty get <uuid | vmname> <property‑name> [‑‑verbose]

The VBoxManage guestproperty get command retrieves the value of the specified property. If the property cannot be found, for example because the guest is not running, the command issues the following message:

No value set!
property-name
Specifies the name of the property.

--verbose
Provides the property value, timestamp, and any specified value attributes.

Set a Property Value for a Virtual Machine
VBoxManage guestproperty set <uuid | vmname> <property‑name> [property‑value [‑‑flags=flags]]

The VBoxManage guestproperty set command enables you to set a guest property by specifying the property and its value. If you omit the value, the property is deleted.

property-name
Specifies the name of the property.

property-value
Specifies the value of the property. If no value is specified, any existing value is removed.

--flags=flags
Specify the additional attributes of the value. The following attributes can be specified as a comma-separated list:

TRANSIENT
Removes the value with the VM data when the VM exits.

TRANSRESET
Removes the value when the VM restarts or exits.

RDONLYGUEST
Specifies that the value can be changed only by the host and that the guest can read the value.

RDONLYHOST
Specifies that the value can be changed only by the guest and that the host can read the value.

READONLY
Specifies that the value cannot be changed.

Wait for a Property Value to Be Created, Deleted, or Changed
VBoxManage guestproperty wait <uuid | vmname> <patterns> [‑‑timeout=msec] [‑‑fail‑on‑timeout]

The VBoxManage guestproperty wait command waits for a particular value that is described by the pattern string to change, to be deleted, or to be created.

patterns
Specifies a pattern that matches the properties on which you want to wait. For information about the pattern wildcards, see the description of the --patterns option.

--timeoutmsec
Specifies the number of microseconds to wait.

--fail-on-timeout
Specifies that the command fails if the timeout is reached.

Unset a Virtual Machine Property Value
VBoxManage guestproperty unset <uuid | vmname> <property‑name>

The VBoxManage guestproperty unset command unsets the value of a guest property.

The alternate form of this subcommand is delete.

property-name
Specifies the name of the property.

Examples
The following command lists the guest properties and their values for the win8 VM.

$ VBoxManage guestproperty enumerate win8
The following command creates a guest property called region for the win8 VM. The value of the property is set to west.

$ VBoxManage guestproperty set win8 region west
VBoxManage hostonlyif
Manage host-only network interfaces

Synopsis
VBoxManage hostonlyif ipconfig <ifname> [‑‑dhcp | ‑‑ip=IPv4‑address‑‑netmask=IPv4‑netmask | ‑‑ipv6=IPv6‑address‑‑netmasklengthv6=length]
VBoxManage hostonlyif create
VBoxManage hostonlyif remove <ifname>
Description
The VBoxManage hostonlyif command enables you to change the IP configuration of a host-only network interface. For a description of host-only networking, see Host-Only Networking. Each host-only network interface is identified by a name and can either use the internal DHCP server or a manual IP configuration, both IPv4 and IPv6.

Configure a Host-Only Interface
VBoxManage hostonlyif ipconfig <ifname> [‑‑dhcp | ‑‑ip=IPv4‑address‑‑netmask=IPv4‑netmask | ‑‑ipv6=IPv6‑address‑‑netmasklengthv6=length]

The VBoxManage hostonlyif ipconfig command configures a host-only interface.

ifname
Specifies the name of the network interface. The name is of the form vboxnetN where N is the interface instance.

--dhcp
Uses DHCP for the network interface.

You cannot use this option with the --ip, --ipv6, --netmask, or --netmasklengthv6 options.

--ip=IPv4-address
Specifies the IPv4 IP address for the network interface.

You cannot use this option with the --dhcp, --ipv6, or --netmasklengthv6 options.

--netmask=IPv4-netmask
Specifies the IPv4 netmask of the network interface. The default value is *************.

You can only use this option with the --ip option.

--ipv6=IPv6-address
Specifies the IPv6 IP address for the network interface.

You cannot use this option with the --dhcp, --ip, or --netmask options.

--netmasklengthv6=length
Specifies the length of the IPv6 network interface. The default value is 64.

You can only use this option with the --ipv6 option.

Create a Network Interface on the Host System
VBoxManage hostonlyif create

The VBoxManage hostonlyif create command creates a new host-only network interface on the host operating system (OS). The network interface name is of the form vboxnetN where N is the interface instance. You must run this command before you can attach virtual machines (VMs) to the host-only network.

This command is currently unavailable on Solaris hosts but the equivalent functionality is to run ifconfig vboxnet0 plumb as root or with sufficient privileges.

Remove a Network Interface From the Host System
VBoxManage hostonlyif remove <ifname>

The VBoxManage hostonlyif remove command removes the specified host-only network interface from the host OS.

This command is currently unavailable on Solaris hosts but the equivalent functionality is to run ifconfig vboxnet0 unplumb as root or with sufficient privileges.

ifname
Specifies the name of the network interface. The name is of the form vboxnetN where N is the interface instance.

Examples
The following command creates a new host-only network interface.

$ VBoxManage hostonlyif create
0%...10%...20%...30%...40%...50%...60%...70%...80%...90%...100%
Interface 'vboxnet2' was successfully created
The following command configures the IPv4 address for the vboxnet2 host-only network interface.

$ VBoxManage hostonlyif ipconfig vboxnet2 --ip *********
VBoxManage hostonlynet
Host Only Network management

Synopsis
VBoxManage hostonlynet add <‑‑name=netname> [‑‑id=netid] <‑‑netmask=mask> <‑‑lower‑ip=address> <‑‑upper‑ip=address> [‑‑enable | ‑‑disable]
VBoxManage hostonlynet modify <‑‑name=netname | ‑‑id=netid> [‑‑lower‑ip=address] [‑‑upper‑ip=address] [‑‑netmask=mask] [‑‑enable | ‑‑disable]
VBoxManage hostonlynet remove <‑‑name=netname | ‑‑id=netid>
Description
The hostonlynet commands enable you to control host-only networks.

Common options
The subcommands of hostonlynet all operate on an host-only network that can be identified via its name or uuid:

--name=netname
The host-only network name. You see it as VBoxNetworkName in the output from VBoxManage list hostonlynets.

--id=netid
The host-only network uuid. If not specified when adding a new network, one will be generated automatically.

hostonlynet add
VBoxManage hostonlynet add <‑‑name=netname> [‑‑id=netid] <‑‑netmask=mask> <‑‑lower‑ip=address> <‑‑upper‑ip=address> [‑‑enable | ‑‑disable]

Adds a new host-only network.

Options configuring the host-only network:

--netmask=mask
The network mask. Typically *************.

--lower-ip=address, --upper-ip=address
The IP address range for handing out via DHCP. The upper boundrary is inclusive while the lower one is not, so the upper address will be handed out to a client, while the lower address will be used by the host itself.

--enable, --disable
Whether to enable the host-only network or disable it. If not specified, the network will be created in enabled state.

hostonlynet modify
VBoxManage hostonlynet modify <‑‑name=netname | ‑‑id=netid> [‑‑lower‑ip=address] [‑‑upper‑ip=address] [‑‑netmask=mask] [‑‑enable | ‑‑disable]

This modifies an existing host-only network configuration. It takes the same options as the add command.

hostonlynet remove
VBoxManage hostonlynet remove <‑‑name=netname | ‑‑id=netid>

Removes the specified host-only network.

VBoxManage import
Import a virtual appliance in OVF format or from a cloud service and create virtual machines

Synopsis
VBoxManage import <ovfname | ovaname> [‑‑dry‑run] [‑‑options=keepallmacs | keepnatmacs | importtovdi] [‑‑vsys=n] [‑‑ostype=ostype] [‑‑vmname=name] [‑‑settingsfile=filename] [‑‑basefolder=folder] [‑‑group=group] [‑‑memory=MB] [‑‑cpus=n] [‑‑description=text] [‑‑eula=show | accept] [‑‑unit=n] [‑‑ignore] [‑‑scsitype=BusLogic | LsiLogic] [‑‑disk=path] [‑‑controller=index] [‑‑port=n]
VBoxManage import OCI://‑‑cloud [‑‑ostype=ostype] [‑‑vmname=name] [‑‑basefolder=folder] [‑‑memory=MB] [‑‑cpus=n] [‑‑description=text] <‑‑cloudprofile=profile> <‑‑cloudinstanceid=id> [‑‑cloudbucket=bucket]
Description
The VBoxManage import command imports a virtual appliance either in OVF format or from a cloud service such as Oracle Cloud Infrastructure (OCI). The import is performed by copying virtual disk images (by default using the VMDK image format) and by creating virtual machines (VMs) in Oracle VirtualBox. See Importing and Exporting Virtual Machines.

You must specify the path name of an OVF file or OVA archive to use as input, or a placeholder for the cloud case. For OVF appliances ensure that any disk images are in the same directory as the OVF file.

Note that any options you specify to control the imported virtual appliance or to modify the import parameters rely on the contents of the OVF file or the information from the cloud service.

Before you use the import operation to create the VM, perform a dry run to verify the correctness of your configuration. This is more useful with an OVF or OVA appliance, because with a cloud service even a dry run needs to perform most of the time consuming steps.

The import from a cloud service downloads a temporary file containing both the boot image and some metadata describing the details of the VM instance. The temporary file is deleted after successful import.

Common Options
ovfname | ovaname
Specifies the name of the OVF file or OVA archive that describes the appliance. In the cloud case this is usually a fixed string such as OCI://.

--dry-run
Performs a dry run of the VBoxManage import command instead of performing the actual import operation. A dry run operation does the following:

Outputs a description of the appliance's contents based on the specified OVF or OVA file.

Shows how the appliance would be imported into Oracle VirtualBox. In addition, the output shows any options that you can use to change the import behavior.

The shortened form of this option is -n.

--options=keepallmacs | keepnatmacs | importtovdi
Enables you to fine-tune the import operation.

Valid arguments are as follows:

keepallmacs: Specifies that the MAC addresses of every virtual network card are left unchanged.

keepnatmacs: Specifies that the MAC addresses of every virtual network card are left unchanged if the network type is NAT.

importtovdi: Specifies that all new disk images are to be created using the VDI file format.

--ostype=ostype
Specifies the guest operating system (OS) information for the VM. Use the VBoxManage list ostypes command to view the OS type identifiers.

--vmname=name
Specifies the name for the imported VM to be used by Oracle VirtualBox.

--basefolder=folder
Specifies the folder where the files of the imported VM are stored.

--memory=MB
Specifies the memory size in Megabytes for the imported VM.

--cpus=n
Specifies the number of CPUs for the imported VM.

--description=text
Specifies the description text visible in the GUI and CLI when checking the VM details.

OVF / OVA Import Options
The following options are specific to importing a virtual appliance in OVF or OVA format. Such an appliance can contain one or more VMs, which requires specifying which VM configuration should be adjusted in case you want to change it. See Importing an Appliance in OVF Format.

VBoxManage import <ovfname | ovaname> [‑‑dry‑run] [‑‑options=keepallmacs | keepnatmacs | importtovdi] [‑‑vsys=n] [‑‑ostype=ostype] [‑‑vmname=name] [‑‑settingsfile=filename] [‑‑basefolder=folder] [‑‑group=group] [‑‑memory=MB] [‑‑cpus=n] [‑‑description=text] [‑‑eula=show | accept] [‑‑unit=n] [‑‑ignore] [‑‑scsitype=BusLogic | LsiLogic] [‑‑disk=path] [‑‑controller=index] [‑‑port=n]

--vsys=n
Specifies the index selecting a specific VM within the appliance. Affects the following options.

--unit=n
Specifies the index selecting a specific unit of a VM within the appliance. Affects the following options.

--settingsfile=filename
Specifies the filename (as a relative or absolute path) of the VM config file which will be created as part of the import. The preferred way to rename the settings file is to override the VM name using the --vmname option and if necessary specify the folder in which to create the VM with --basefolder.

--group=group
Specifies the primary group of the imported VM.

--eula=show | accept
Enables you to show or accept the license conditions of a VM within the appliance,

Valid arguments are as follows:

show: Shows the EULA of a VM.

accepts: Accepts the EULA of a VM. Any VMs in an appliance which have an EULA require accepting it, otherwise the import will fail.

--ignore
Ignores the hardware associated with the VM specified with the --unit option when importing the VM, effectively removing all of its associated hardware.

--scsitype=BusLogic | LsiLogic
Enables you to select the type of the SCSI controller for the current unit of an imported VM.

Valid arguments are as follows:

BusLogic: Uses the (very old) BusLogic SCSI controller type.

LsiLogic: Uses the (more modern) LsiLogic SCSI controller type.

Cloud Import Options
The following options are specific to importing a VM instance from a cloud service provider. It always deals with a single VM. See Importing an Instance from Oracle Cloud Infrastructure.

VBoxManage import OCI://‑‑cloud [‑‑ostype=ostype] [‑‑vmname=name] [‑‑basefolder=folder] [‑‑memory=MB] [‑‑cpus=n] [‑‑description=text] <‑‑cloudprofile=profile> <‑‑cloudinstanceid=id> [‑‑cloudbucket=bucket]

--cloud
Specifies that the import should be from the cloud.

--cloudprofile=profile
Specifies the cloud profile which is used to connect to the cloud service provider. The cloud profile contains your Oracle Cloud Infrastructure account details, such as your user OCID and the fingerprint for your public key. To use a cloud profile, you must have the required permissions on Oracle Cloud Infrastructure.

--cloudinstanceid=id
Specifies the ID of an existing instance in the cloud.

--cloudbucket=bucket
Specifies the bucket name in which to store the object created from the instance. In Oracle Cloud Infrastructure, a bucket is a logical container for storing objects. By default the first bucket available with the cloud profile is used.

Examples
The following example performs the dry run of an OVF import operation for a sample appliance that contains a Windows 10 guest:

$ VBoxManage import Windows10.ovf --dry-run
Interpreting Windows10.ovf...
OK.
Virtual system 0:
 0: Suggested OS type: "Windows10_64"
    (change with "--vsys 0 --ostype <type>"; use "list ostypes" to list all)
 1: Suggested VM name "win10-appliance"
    (change with "--vsys 0 --vmname <name>")
 2: Suggested VM group "/"
    (change with "--vsys 0 --group <group>")
 3: Suggested VM settings file name "/home/<USER>/VirtualBox VMs/win10-appliance/win10-appliance.vbox"
    (change with "--vsys 0 --settingsfile <filename>")
 4: Suggested VM base folder "/home/<USER>/VirtualBox VMs"
    (change with "--vsys 0 --basefolder <path>")
 5: End-user license agreement
    (display with "--vsys 0 --eula show";
    accept with "--vsys 0 --eula accept")
 6: Number of CPUs: 1
    (change with "--vsys 0 --cpus <n>")
 7: Guest memory: 2048 MB (change with "--vsys 0 --memory <MB>")
 8: Sound card (appliance expects "ensoniq1371", can change on import)
    (disable with "--vsys 0 --unit 8 --ignore")
 9: USB controller
    (disable with "--vsys 0 --unit 9 --ignore")
10: Network adapter: orig bridged, config 2, extra type=bridged
11: Floppy
    (disable with "--vsys 0 --unit 11 --ignore")
12: SCSI controller, type BusLogic
    (change with "--vsys 0 --unit 12 --scsitype {BusLogic|LsiLogic}";
    disable with "--vsys 0 --unit 12 --ignore")
13: IDE controller, type PIIX4
    (disable with "--vsys 0 --unit 13 --ignore")
14: Hard disk image: source image=Windows10.vmdk,
      target path=/home/<USER>/disks/Windows10.vmdk, controller=12;channel=0
    (change target path with "--vsys 0 --unit 14 --disk <path>";
    change controller with "--vsys 0 --unit 14 --controller <index>";
    change controller port with "--vsys 0 --unit 14 --port <n>";
    disable with "--vsys 0 --unit 14 --ignore")
The dry run output lists and numbers the individual configuration items that are described in the Windows10.ovf file. Some of the items include information about how to disable or change the configuration of the item.

You can disable many of the items by using the --vsys X --unit Y --ignore options. X is the number of the virtual system. The value is 0 unless the appliance includes several virtual system descriptions. Y is the configuration item number.

Item 1 in the example command output specifies the name of the target machine. Items 12 and 13 specify the IDE and SCSI hard disk controllers, respectively.

Item 14 indicates the hard disk image and the --disk option specifies the target path where the image will be stored, the --controller option specifies which controller the disk will be attached to, and the --port option specifies which port on the controller the disk will be attached to. The default values are specified in the OVF file.

You can combine several items for the same virtual system by specifying the same value for the --vsys option. For example use the following command to import a machine as described in the OVF, exclude the sound card and USB controller and specify that the disk image is stored with a different name.

$ VBoxManage import Windows10.ovf --vsys 0 --unit 8 --ignore \
  --unit 9 --ignore --unit 14 --disk Windows10_disk0.vmdk
The following example illustrates how to import a VM from Oracle Cloud Infrastructure. To find the Oracle Cloud Infrastructure VM instances and its ID you can list all available instances with:

$ VBoxManage cloud --provider=OCI --profile=cloud-profile-name list instances
Once you know the ID the following command imports the instance from Oracle Cloud Infrastructure:

$ VBoxManage import OCI:// --cloud --vmname OCI_FreeBSD_VM --memory 4000 \
  --cpus 3 --ostype FreeBSD_64 --cloudprofile "standard user" \
  --cloudinstanceid ocid1.instance.oc1.iad.abuwc... --cloudbucket myBucket
VBoxManage list
View system information and VM configuration details

Synopsis
VBoxManage list [‑‑long] [‑‑platform‑arch=x86 | arm] [‑‑sorted] [bridgedifs | cloudnets | cloudprofiles | cloudproviders | cpu‑profiles | dhcpservers | dvds | extpacks | floppies | groups | hddbackends | hdds | hostcpuids | hostdrives | hostdvds | hostfloppies | hostinfo | hostonlyifs | hostonlynets | intnets | natnets | ostypes | ossubtypes | runningvms | screenshotformats | systemproperties | usbfilters | usbhost | vms | webcams]
Description
The VBoxManage list subcommands enable you to obtain information about the Oracle VirtualBox software, the VMs and associated services that you create.

Common Options
--long
Shows detailed information about each information entry if available. The short form of this option is -l.

--platform-arch
Filters the output by the given platform architecture (if available, otherwise ignored). The short form of this option is -p.

--sorted
Sorts the list of information entries alphabetically. The short form of this option is -s.

List the Bridged Network Interfaces on the Host System
VBoxManage list bridgedifs

The VBoxManage list bridgedifs command lists the bridged network interfaces that are currently available on the host system. The output shows detailed configuration information about each interface. See Virtual Networking.

List the Cloud Network Interfaces
VBoxManage list cloudnets

The VBoxManage list cloudnets command lists the cloud network interfaces that have been configured. A cloud network interface provides connectivity between local VMs and a cloud network.

List the Cloud Profiles
VBoxManage list cloudprofiles

The VBoxManage list cloudprofiles command lists the cloud profiles that have been configured. A cloud profile contains settings for a cloud service account.

List the Cloud Providers
VBoxManage list cloudproviders

The VBoxManage list cloudproviders command lists the cloud providers that are supported by Oracle VirtualBox. Oracle Cloud Infrastructure is an example of a cloud provider.

List the known CPU Profiles
VBoxManage list cpu‑profiles

The VBoxManage list cpu-profiles command lists the CPU profiles that are known by Oracle VirtualBox.

List the DHCP Servers on the Host System
VBoxManage list dhcpservers

The VBoxManage list dhcpservers command lists the DHCP servers that are currently available on the host system. The output shows detailed configuration information about each DHCP server. See Virtual Networking.

List the DVD Virtual Disk Images
VBoxManage list dvds

The VBoxManage list dvds command shows information about the DVD virtual disk images that are currently in use by the Oracle VirtualBox software. For each image, the output shows all the settings, the UUIDs associated with the image by Oracle VirtualBox, and all files associated with the image.

This command performs the same function as the Virtual Media Manager. See The Virtual Media Manager.

List the Installed Oracle VirtualBox Extension Packs
VBoxManage list extpacks

The VBoxManage list extpacks command shows all Oracle VirtualBox extension packs that are currently installed. See Install the Oracle VirtualBox Extension Pack and VBoxManage extpack.

List the Floppy Disk Virtual Disk Images
VBoxManage list floppies

The VBoxManage list floppies command shows information about the floppy disk images that are currently in use by the Oracle VirtualBox software. For each image, the output shows all the settings, the UUIDs associated with the image by Oracle VirtualBox, and all files associated with the image.

This command performs the same function as the Virtual Media Manager. See The Virtual Media Manager.

List the Virtual Machine Groups
VBoxManage list groups

The VBoxManage list groups command shows all VM groups. See Using VM Groups.

List the Virtual Disk Backends
VBoxManage list hddbackends

The VBoxManage list hddbackends command lists all known virtual disk backends of the Oracle VirtualBox software. For each such format, such as VDI, VMDK, or RAW, this command lists the backend's capabilities and configuration.

List the Hard Disk Virtual Disk Images
VBoxManage list hdds

The VBoxManage list hdds command shows information about the hard disk virtual disk images that are currently in use by the Oracle VirtualBox software. For each image, the output shows all the settings, the UUIDs associated with the image by Oracle VirtualBox, and all files associated with the image.

This command performs the same function as the Virtual Media Manager. See The Virtual Media Manager.

List the CPUID Information for the Host System CPUs
VBoxManage list hostcpuids

The VBoxManage list hostcpuids command lists CPUID information for each CPU on the host system. Use this information to perform a more fine grained analyis of the host system's virtualization capabilities.

List the Storage Drives on the Host System
VBoxManage list hostdrives

The VBoxManage list hostdrives command lists the disk drives on the host system potentially useful for creating a VMDK raw disk image. Each entry includes the name used to reference them from within Oracle VirtualBox.

List the DVD Drives on the Host System
VBoxManage list hostdvds

The VBoxManage list hostdvds command lists the DVD drives on the host system. Each DVD entry includes the name used to access them from within Oracle VirtualBox.

List the Floppy Disk Drives on the Host System
VBoxManage list hostfloppies

The VBoxManage list hostfloppies command lists the floppy disk drives on the host system. Each floppy disk entry includes the name used to access them from within Oracle VirtualBox.

List Information About the Host System
VBoxManage list hostinfo

The VBoxManage list hostinfo command shows information about the host system. The output includes information about the CPUs, memory, and the OS version.

List the Host-Only Network Interfaces on the Host System
VBoxManage list hostonlyifs

The VBoxManage list hostonlyifs command lists the host-only network interfaces that are currently available on the host system. The output shows detailed configuration information about each interface. See Virtual Networking.

List Host-Only Networks
VBoxManage list hostonlynets

The VBoxManage list hostonlynets command lists the host-only networks that have been configured. A host-only network provides connectivity between the host and local VMs. See Virtual Networking.

List Internal Networks
VBoxManage list intnets

The VBoxManage list intnets command shows information about the internal networks. See Virtual Networking.

List the NAT Network Interfaces on the Host System
VBoxManage list natnets

The VBoxManage list natnets command lists the NAT network interfaces that are currently available on the host system. See Virtual Networking.

List the Guest Operating Systems
VBoxManage list ostypes

The VBoxManage list ostypes command lists all guest operating systems (OSes) that are known to Oracle VirtualBox. Each OS entry includes an identifier, a description, a family identifier, a family description, and whether the OS has 64-bit support.

You can use these identifiers with the VBoxManage modifyvm command.

List the Guest Operating System Subtypes
VBoxManage list ossubtypes

The VBoxManage list ossubtypes command lists all guest operating system (OS) subtypes along with the associated guest OS descriptions that are known to Oracle VirtualBox. Each list entry includes a guest OS family identifier, the guest OS subtypes associated with that OS family (if any), and a description the guest OSes associated with that OS subtype.

List the Running Virtual Machines
VBoxManage list runningvms

The VBoxManage list runningvms command lists all virtual machines (VMs) that are currently running. By default this displays a compact list that shows the name and UUID of each VM.

List the Available Screen Shot Formats
VBoxManage list screenshotformats

The VBoxManage list screenshotformats command shows the list of available screen shot formats.

List System Properties
VBoxManage list systemproperties

The VBoxManage list systemproperties command shows a large collection of global Oracle VirtualBox settings and limits, such as minimum and maximum guest RAM, virtual hard disk size, folder settings, and the current authentication library in use.

List the Registered Global USB Filters
VBoxManage list usbfilters

The VBoxManage list usbfilters command lists all global USB filters registered with Oracle VirtualBox and displays the filter parameters. Global USB filters are for devices which are accessible to all virtual machines.

List the USB Devices on the Host System
VBoxManage list usbhost

The VBoxManage list usbhost command shows information about the USB devices that are attached to the host system. The output includes information that you can use to construct USB filters and indicates whether the device is currently in use by the host system.

List Virtual Machines
VBoxManage list vms

The VBoxManage list vms command lists all virtual machines (VMs) that are currently registered with Oracle VirtualBox. By default this command displays a compact list that shows the name and UUID of each VM.

List the Webcams Attached to a Running Virtual Machine
VBoxManage list webcams

The VBoxManage list webcams command shows the list of webcams that are attached to the running VM.

The output is a list of absolute paths or aliases that are used to attach the webcams to the VM by using the VBoxManage webcam attach command.

Examples
The following command lists the VM groups configured for Oracle VirtualBox.

$ VBoxManage list groups
"/Linux-VMs"
"/Windows-VMs"
The following command lists the VMs that are currently running.

$ VBoxManage list runningvms
"ol7" {ol7-UUID}
"win8" {win8-UUID}
VBoxManage mediumio
Medium content access

Synopsis
VBoxManage mediumio <‑‑disk=uuid|filename | ‑‑dvd=uuid|filename | ‑‑floppy=uuid|filename> [‑‑password‑file=‑|filename]formatfat [‑‑quick]
VBoxManage mediumio <‑‑disk=uuid|filename | ‑‑dvd=uuid|filename | ‑‑floppy=uuid|filename> [‑‑password‑file=‑|filename]cat [‑‑hex] [‑‑offset=byte‑offset] [‑‑size=bytes] [‑‑output=‑|filename]
VBoxManage mediumio <‑‑disk=uuid|filename | ‑‑dvd=uuid|filename | ‑‑floppy=uuid|filename> [‑‑password‑file=‑|filename]stream [‑‑format=image‑format] [‑‑variant=image‑variant] [‑‑output=‑|filename]
Description
Common options
The subcommands of mediumio all operate on a medium which must be specified using one of the following options along with an optional encryption password. The following common options can be placed before or after the sub-command:

--disk=uuid|filename
Either the UUID or filename of a harddisk image, e.g. VDI, VMDK, VHD, ++.

--dvd=uuid|filename
Either the UUID or filename of a DVD image, e.g. ISO, DMG, CUE.

--floppy=uuid|filename
Either the UUID or filename of a floppy image, e.g. IMG.

--password-file=-|filename
The name of a file containing the medium encryption password. If - is specified, the password will be read from stdin.

mediumio formatfat
VBoxManage mediumio <‑‑disk=uuid|filename | ‑‑dvd=uuid|filename | ‑‑floppy=uuid|filename> [‑‑password‑file=‑|filename]formatfat [‑‑quick]

Formats a medium with the FAT file system. This will erase the contents of the medium.

--quick
Quickformat the medium.

mediumio cat
VBoxManage mediumio <‑‑disk=uuid|filename | ‑‑dvd=uuid|filename | ‑‑floppy=uuid|filename> [‑‑password‑file=‑|filename]cat [‑‑hex] [‑‑offset=byte‑offset] [‑‑size=bytes] [‑‑output=‑|filename]

Dumps the contents of the medium to stdout or the specified file.

--hex
Dump as hex bytes.

--offset
The byte offset in the medium to start.

--size
The number of bytes to dump.

--output
The output filename. As usual - is take to mean stdout.

mediumio stream
VBoxManage mediumio <‑‑disk=uuid|filename | ‑‑dvd=uuid|filename | ‑‑floppy=uuid|filename> [‑‑password‑file=‑|filename]stream [‑‑format=image‑format] [‑‑variant=image‑variant] [‑‑output=‑|filename]

Converts the medium to a streamable format and dumps it to the given output.

--format
The format of the destination image.

--variant
The medium variant for the destination.

--output
The output filename. As usual - is take to mean stdout.

VBoxManage mediumproperty
Manage medium properties

Synopsis
VBoxManage mediumproperty [disk | dvd | floppy]set <uuid | filename> <property‑name> <property‑value>
VBoxManage mediumproperty [disk | dvd | floppy]get <uuid | filename> <property‑name>
VBoxManage mediumproperty [disk | dvd | floppy]delete <uuid | filename> <property‑name>
Description
The VBoxManage mediumproperty command enables you to set, retrieve, or delete a medium property.

Set a Medium Property
VBoxManage mediumproperty [disk | dvd | floppy]set <uuid | filename> <property‑name> <property‑value>

The VBoxManage mediumproperty set command enables you to set a medium property.

disk | dvd | floppy
Specifies the type of medium. Valid values are disk (hard drive), dvd, or floppy.

uuid | filename
Specifies the Universally Unique Identifier (UUID) or absolute path name of the medium or image.

property-name
Specifies the name of the property.

property-value
Specifies the value of the specified property.

Retrieve a Medium Property Value
VBoxManage mediumproperty [disk | dvd | floppy]get <uuid | filename> <property‑name>

The VBoxManage mediumproperty get command enables you to retrieve the value of a medium property.

disk | dvd | floppy
Specifies the type of medium. Valid values are disk (hard drive), dvd, or floppy.

uuid | filename
Specifies the Universally Unique Identifier (UUID) or absolute path name of the medium or image.

property-name
Specifies the name of the property.

Delete a Medium Property
VBoxManage mediumproperty [disk | dvd | floppy]delete <uuid | filename> <property‑name>

The VBoxManage mediumproperty delete command enables you to delete a medium property.

disk | dvd | floppy
Specifies the type of medium. Valid values are disk (hard drive), dvd, or floppy.

uuid | filename
Specifies the Universally Unique Identifier (UUID) or absolute path name of the medium or image.

property-name
Specifies the name of the property.

Examples
The following command sets the property called prop1 to val1 for the ol7.vdi disk image.

$ VBoxManage mediumproperty disk set ol7.vdi prop1 val1
The following command gets the value of the property called prop1 for the ol7.vdi disk image.

$ VBoxManage mediumproperty disk get ol7.vdi prop1
VBoxManage metrics
Monitor system resource usage

Synopsis
VBoxManage metrics collect [‑‑detach] [‑‑list] [‑‑period=seconds] [‑‑samples=count] [* | host | vmnamemetric‑list]
VBoxManage metrics disable [‑‑list] [* | host | vmnamemetric‑list]
VBoxManage metrics enable [‑‑list] [* | host | vmnamemetric‑list]
VBoxManage metrics list [* | host | vmnamemetric‑list]
VBoxManage metrics query [* | host | vmnamemetric‑list]
VBoxManage metrics setup [‑‑list] [‑‑periodseconds] [‑‑samplescount] [* | host | vmnamemetric‑list]
Description
The VBoxManage metrics command enables you to monitor system resource usage for the host system and for virtual machines (VMs). For example, you can monitor particular metrics, such as the percentage of time CPUs spend executing in user mode (CPU/Load/User), over a specified sampling period.

Use the VBoxManage metrics query command to retrieve data at any time.

By default, metrics are not collected unless you run the VBoxManage metrics setup command to specify a sampling interval in seconds and the number of metrics to save.

Note that you can enable metric collection only for running VMs. Collected data and collection settings for a VM are discarded when the VM shuts down.

Metrics
The host and VMs have different sets of associated metrics which you can view by running the VBoxManage metrics list command.

Each metric is represented as a string that is composed of a category and a metric. Optionally, the metric string can include any of the following: a submetric, a sub-submetric, and an aggregate. The metric string has the following format:

category/metric[/submetric[/sub-submetric]][:aggregate]
category is the resource type, such as CPU, RAM, FS, Net.

metric is a measurement type that is associated with the resource category. For example, the Load and MHz metrics are associated with the CPU resource category.

submetric is an optional measurement type that is associated with the metric. For example, the User, Kernel, and Idle submetrics are associated with the Load metric.

sub-submetric is an optional measurement type that is associated with the submetric. For example, the Rx and Tx sub-submetrics are associated with the Rate submetric of the Net resource category. The associated metric is the network interface.

aggregate is an optional function to provide minimum, maximum, and average measurements for a resource category. For example, the RAM/Usage/Free:min metric represents the minimum amount of available memory found in all saved data on the host system.

By default, the VBoxManage metrics commands operate on the host system and all VMs, and report on all metrics. You can optionally limit these commands to operate on the host system or on a particular VM, and report on a list of one or more metrics.

Common Options
* | host | vmname
Specifies the component on which to operate. By default, this command operates on the host system and all running VMs.

If you specify host, the VBoxManage metrics command operates on the host system only. If you specify an asterisk (*), the command operates on all VMs. If you specify the name of a VM, the VBoxManage metrics command operates on that VM.

metric-list
Specifies a comma-separated list of one or more metrics.

The form of the metric must include the category and metric part of the metric string separated by a slash.

Note that the VBoxManage metrics enable and VBoxManage metrics disable commands require that you specify metrics as parameters. The metrics must include only the resource category and metric part, such as CPU/Load and RAM/Usage.

Collect Data Metrics
VBoxManage metrics collect [‑‑detach] [‑‑list] [‑‑period=seconds] [‑‑samples=count] [* | host | vmnamemetric‑list]

The VBoxManage metrics collect command collects and outputs data periodically until you stop the process by pressing Ctrl+C.

--detach
Disables the collection of metric data, so no data is output. Using this option is the same as running the VBoxManage metrics setup command.

--list
Shows which metrics match the specified filter.

--period=seconds
Specifies the number of seconds to wait between collecting metric data samples. The default value is 1.

--samples=count
Specifies the number of metric data samples to save. To view the saved data, use the VBoxManage metrics query command. The default value is 1.

Disable Metric Data Collection
VBoxManage metrics disable [‑‑list] [* | host | vmnamemetric‑list]

The VBoxManage metrics disable command suspends data collection. This action does not affect the data collection properties or the collected data. Note that specifying a submetric in the metric list does not disable its underlying metrics.

Note that the VBoxManage metrics disable command requires that you specify metrics as parameters. The metrics must include only the resource category and metric part, such as CPU/Load and RAM/Usage.

--list
Shows whether the command succeeded as expected.

Enable Metric Data Collection
VBoxManage metrics enable [‑‑list] [* | host | vmnamemetric‑list]

The VBoxManage metrics enable command resumes data collection after it has been suspended by using the VBoxManage metrics disable command. Note that specifying a submetric in the metric list does not enable its underlying metrics.

Unlike the VBoxManage metrics setup command, the VBoxManage metrics enable command does not discard previously collected samples for the specified set of objects and metrics.

Note that the VBoxManage metrics enable command requires that you specify metrics as parameters. The metrics must include only the resource category and metric part, such as CPU/Load and RAM/Usage.

--list
Shows whether the command succeeded as expected.

List Metric Values
VBoxManage metrics list [* | host | vmnamemetric‑list]

The VBoxManage metrics list command shows the metrics that are currently available. Note that VM-specific metrics are shown only when that VM is running.

List Saved Metric Data
VBoxManage metrics query [* | host | vmnamemetric‑list]

The VBoxManage metrics query command retrieves and shows the saved metric data.

Note that the VBoxManage metrics query command does not remove or flush saved data but older samples are replaced by newer samples over time.

Configure Metric-Gathering Properties
VBoxManage metrics setup [‑‑list] [‑‑periodseconds] [‑‑samplescount] [* | host | vmnamemetric‑list]

The VBoxManage metrics setup command configures metric-gathering properties.

Note that this command discards any previously collected samples for the specified set of objects and metrics. To enable or disable metrics collection without discarding the data, use the VBoxManage metrics enable command or the VBoxManage metrics disable command, respectively.

--list
Shows which metrics have been modified as a result of the command execution.

--period=seconds
Specifies the number of seconds to wait between collecting metric data samples. The default value is 1.

--samples=count
Specifies the number of metric data samples to save. To view the saved data, use the VBoxManage metrics query command. The default value is 1.

Examples
The following example command enables the collection of host processor and memory usage metrics every second. The --samples option saves the five latest samples.

$ VBoxManage metrics setup --period 1 --samples 5 host CPU/Load,RAM/Usage
The following command lists the metrics that are available to the host system and VMs:

$ VBoxManage metrics list
Note that the host system and VMs have different sets of metrics.

The following example shows how to query metric data about the CPU time spent in user and kernel modes for the test VM:

$ VBoxManage metrics query test CPU/Load/User,CPU/Load/Kernel
VBoxManage modifymedium
Change the characteristics of an existing disk image

Synopsis
VBoxManage modifymedium [disk | dvd | floppy] <uuid | filename> [‑‑autoreset=on | off] [‑‑compact] [‑‑description=description] [‑‑move=pathname] [‑‑property=name=[value]] [‑‑resize=megabytes | ‑‑resizebyte=bytes] [‑‑setlocation=pathname] [‑‑type=normal | writethrough | immutable | shareable | readonly | multiattach]
Description
The VBoxManage modifymedium command enables you to change the characteristics of an existing disk image.

Note:
For compatibility with earlier versions of Oracle VirtualBox, you can use the modifyvdi and modifyhd commands.

disk | dvd | floppy
Specifies the media type of the image.

filename
Specifies the Universally Unique Identifier (UUID) or path name of the disk image on the host file system. You can specify the UUID only if the medium is registered. Use the VBoxManage list hdds command to list the registered images. You can specify an absolute or relative path to the medium.

--autoreset=on | off
Specifies whether to automatically reset an immutable hard disk on every virtual machine (VM) startup. This option is only for immutable hard disks and the default value is on. See Special Image Write Modes.

--compact
Compresses disk images by removing blocks that contain only zeroes. This option shrinks a dynamically allocated image and reduces the physical size of the image without affecting the logical size of the virtual disk.

You can use this option for base images and for differencing images that are created as part of a snapshot.

Note:
Before you compress the image, you must use a suitable software tool to zero out free space in the guest system. For example:

Windows guests. Run the sdelete -z command.

Linux guests. Use the zerofree utility, which supports ext2 and ext3 file systems.

Mac OS X guests. Use the diskutil secureErase freespace 0 / command.

Note that you can only use this option to compress VDI images. To compress non-VID images, you can zero out free blocks and then clone the disk to any other dynamically allocated format.

--description=description
Specifies a text description of the medium.

--move=pathname
Specifies a relative or absolute path to a medium on the host system. Use this option to relocate a medium to a different location on the host system.

--property=name=value
Specifies a property name and value for the medium.

--resize=size
Specifies the new capacity of an existing image in MB. You can use this option only to expand the capacity of an image. You can cannot shrink the capacity of an image.

Note that you can only resize dynamically allocated disk images that use the VDI and VHD formats. This option adjusts the logical size of a virtual disk and has only a minor affect on the physical size.

For example, if your dynamically allocated 10 GB disk is full, you can use the --resize 15360 option to increase the capacity of the existing disk to 15 GB (15,360 MB). This operation enables you to avoid having to create a new image and copy all data from within a VM.

Note that using this option only changes the capacity of the drive. So, you might need to subsequently use a partition management tool in the guest to adjust the main partition to fill the drive.

--resizebyte=size
Specifies the new capacity of an existing image in bytes. This option is similar to the --resize option, but you specify the size in bytes instead of megabytes.

--setlocation=pathname
Specifies the new location of the medium on the host system after the medium has been moved. The path name can be relative to the current directory or be absolute to the root.

Note that the VBoxManage modifymedium command does not perform any sanity checks on the path name you specify. Ensure that the path name is valid.

--type
Specifies the new mode type of an existing image. Valid values are normal, immutable, writethrough, multi-attach, shareable, and readonly. For descriptions of these mode types, see Special Image Write Modes.

Examples
The following command modifies the description for the disk image file called disk01.vdi.

$ VBoxManage modifymedium disk disk01.vdi --description "Oracle Linux 7 image"
The following command modifies the write mode for the disk image file called disk01.vdi.

$ VBoxManage modifymedium disk disk01.vdi --type writethrough
See Also
VBoxManage list

VBoxManage modifynvram
List and modify the NVRAM content of a virtual machine

Synopsis
VBoxManage modifynvram <uuid | vmname>inituefivarstore
VBoxManage modifynvram <uuid | vmname>enrollmssignatures
VBoxManage modifynvram <uuid | vmname>enrollorclpk
VBoxManage modifynvram <uuid | vmname>enrollpk [‑‑platform‑key=filename] [‑‑owner‑uuid=uuid]
VBoxManage modifynvram <uuid | vmname>enrollmok [‑‑mok=filename] [‑‑owner‑uuid=uuid]
VBoxManage modifynvram <uuid | vmname>secureboot <‑‑enable | ‑‑disable>
VBoxManage modifynvram <uuid | vmname>listvars
VBoxManage modifynvram <uuid | vmname>queryvar [‑‑name=name] [‑‑filename=filename]
VBoxManage modifynvram <uuid | vmname>deletevar [‑‑name=name] [‑‑owner‑uuid=uuid]
VBoxManage modifynvram <uuid | vmname>changevar [‑‑name=name] [‑‑filename=filename]
Description
The "modifynvram" commands are for experts who want to inspect and modify the UEFI variable store of a virtual machine. Any mistakes made here can result in a non-bootable virtual machine.

Common options
The subcommands of modifynvram all operate on a running virtual machine:

uuid | vmname
Either the UUID or the name (case sensitive) of a VM.

modifynvram inituefivarstore
VBoxManage modifynvram <uuid | vmname>inituefivarstore

Initalizes the UEFI variable store to a default state. Any previous existing variable store is deleted. Use with extreme caution!

modifynvram enrollmssignatures
VBoxManage modifynvram <uuid | vmname>enrollmssignatures

Enrolls the default Microsoft KEK and DB signatures required for UEFI secure boot.

modifynvram enrollorclpk
VBoxManage modifynvram <uuid | vmname>enrollorclpk

Enrolls the default platform key provided by Oracle required for UEFI secure boot.

modifynvram enrollpk
VBoxManage modifynvram <uuid | vmname>enrollpk [‑‑platform‑key=filename] [‑‑owner‑uuid=uuid]

Enrolls a custom platform key provided by the user required for UEFI secure boot. The following commands use openssl to generate a new platform key:

$ openssl req -new -x509 -newkey rsa:2048 -keyout PK.key -out PK.crt
$ openssl x509 -in PK.crt -out PK.cer -outform DER
--platform-key=filename
The platform key provided as a DER encoded X.509 signature.

--owner-uuid=uuid
The UUID identifying the owner of the platform key.

modifynvram secureboot
VBoxManage modifynvram <uuid | vmname>secureboot <‑‑enable | ‑‑disable>

Enables or disables UEFI secure boot.

--enable
Enables UEFI secure boot if the state of the key enrolment permits.

--disable
Disables UEFI secure boot.

modifynvram listvars
VBoxManage modifynvram <uuid | vmname>listvars

Lists all UEFI variables in the virtual machine's store along with their owner UUID.

modifynvram queryvar
VBoxManage modifynvram <uuid | vmname>queryvar [‑‑name=name] [‑‑filename=filename]

Queries the content of a given UEFI variable identified by its name.

--name=name
UEFI variable name to query.

--filename=filename
Where to store the content of the variable upon success. This is optional, if omitted the content will be dumped to the terminal as a hex dump.

modifynvram deletevar
VBoxManage modifynvram <uuid | vmname>deletevar [‑‑name=name] [‑‑owner‑uuid=uuid]

Deletes the given variable identified by its name and owner UUID.

--name=name
UEFI variable name to delete.

--owner-uuid=uuid
The UUID identifying the owner of the variable to delete.

modifynvram changevar
VBoxManage modifynvram <uuid | vmname>changevar [‑‑name=name] [‑‑filename=filename]

Changes the UEFI variable content to the one form the given file.

--name=name
UEFI variable name to change the data for.

--filename=filename
The file to read the data from.

VBoxManage modifyvm
Change settings for a virtual machine that is stopped

Synopsis
VBoxManage modifyvm <uuid | vmname> [‑‑name=name] [‑‑groups=group [,group...]] [‑‑description=description] [‑‑os‑type=OS‑type] [‑‑icon‑file=filename] [‑‑memory=size‑in‑MB] [‑‑page‑fusion=on | off] [‑‑vram=size‑in‑MB] [‑‑acpi=on | off] [‑‑ioapic=on | off] [‑‑hardware‑uuid=UUID] [‑‑cpus=CPU‑count] [‑‑cpu‑hotplug=on | off] [‑‑plug‑cpu=CPU‑ID] [‑‑unplug‑cpu=CPU‑ID] [‑‑cpu‑execution‑cap=number] [‑‑x86‑pae=on | off] [‑‑x86‑long‑mode=on | off] [‑‑ibpb‑on‑vm‑exit=on | off] [‑‑ibpb‑on‑vm‑entry=on | off] [‑‑spec‑ctrl=on | off] [‑‑l1d‑flush‑on‑sched=on | off] [‑‑l1d‑flush‑on‑vm‑entry=on | off] [‑‑mds‑clear‑on‑sched=on | off] [‑‑mds‑clear‑on‑vm‑entry=on | off] [‑‑cpu‑profile=host | Intel 8086 | Intel 80286 | Intel 80386] [‑‑x86‑hpet=on | off] [‑‑hwvirtex=on | off] [‑‑triple‑fault‑reset=on | off] [‑‑apic=on | off] [‑‑x86‑x2apic=on | off] [‑‑arm‑gic‑its=on | off] [‑‑paravirt‑provider=none | default | legacy | minimal | hyperv | kvm] [‑‑paravirt‑debug=key=value[,key=value...]] [‑‑nested‑paging=on | off] [‑‑large‑pages=on | off] [‑‑x86‑vtx‑vpid=on | off] [‑‑x86‑vtx‑ux=on | off] [‑‑nested‑hw‑virt=on | off] [‑‑virt‑vmsave‑vmload=on | off] [‑‑accelerate‑3d=on | off] [‑‑chipset=ich9 | piix3 | armv8virtual] [‑‑iommu=none | automatic | amd | intel] [‑‑tpm‑type=none | 1.2 | 2.0 | host | swtpm] [‑‑tpm‑location=location] [‑‑firmware‑logo‑fade‑in=on | off] [‑‑firmware‑logo‑fade‑out=on | off] [‑‑firmware‑logo‑display‑time=msec] [‑‑firmware‑logo‑image‑path=pathname] [‑‑firmware‑boot‑menu=disabled | menuonly | messageandmenu] [‑‑firmware‑apic=disabled | apic | x2apic] [‑‑firmware‑system‑time‑offset=msec] [‑‑firmware‑pxe‑debug=on | off] [‑‑system‑uuid‑le=on | off] [‑‑bootX=none | floppy | dvd | disk | net] [‑‑rtc‑use‑utc=on | off] [‑‑graphicscontroller=none | vboxvga | vmsvga | vboxsvga | qemuramfb] [‑‑snapshot‑folder=default | pathname] [‑‑firmware=bios | efi | efi32 | efi64] [‑‑guest‑memory‑balloon=size‑in‑MB] [‑‑default‑frontend=default | name] [‑‑vm‑process‑priority=default | flat | low | normal | high] [‑‑vm‑execution‑engine=default | hm | hwvirt | nem | native‑api | interpreter | recompiler]
VBoxManage modifyvm <uuid | vmname> [‑‑nicN=none | null | nat | bridged | intnet | hostonly | hostonlynet | generic | natnetwork | cloud] [‑‑nic‑typeN=Am79C970A | Am79C973 | 82540EM | 82543GC | 82545EM | virtio | usbnet] [‑‑cable‑connectedN=on | off] [‑‑nic‑traceN=on | off] [‑‑nic‑trace‑fileN=filename] [‑‑nic‑propertyN=name= [value]] [‑‑nic‑speedN=kbps] [‑‑nic‑boot‑prioN=priority] [‑‑nic‑promiscN=deny | allow‑vms | allow‑all] [‑‑nic‑bandwidth‑groupN=none | name] [‑‑bridge‑adapterN=none | device‑name] [‑‑cloud‑networkN=network‑name] [‑‑host‑only‑adapterN=none | device‑name] [‑‑host‑only‑netN=network‑name] [‑‑intnetN=network‑name] [‑‑nat‑networkN=network‑name] [‑‑nic‑generic‑drvN=driver‑name] [‑‑mac‑addressN=auto | MAC‑address]
VBoxManage modifyvm <uuid | vmname> [‑‑nat‑netN=network | default] [‑‑nat‑pfN=[rule‑name],tcp | udp,[host‑IP],hostport,[guest‑IP],guestport] [‑‑nat‑pfN=delete=rule‑name] [‑‑nat‑tftp‑prefixN=prefix] [‑‑nat‑tftp‑fileN=filename] [‑‑nat‑tftp‑serverN=IP‑address] [‑‑nat‑bind‑ipN=IP‑address] [‑‑nat‑dns‑pass‑domainN=on | off] [‑‑nat‑localhostreachableN=on | off] [‑‑nat‑settingsN=[mtu]] [‑‑nat‑forward‑broadcastN=on | off] [‑‑nat‑enable‑tftpN=on | off]
VBoxManage modifyvm <uuid | vmname> [‑‑mouse=ps2 | usb | usbtablet | usbmultitouch | usbmtscreenpluspad] [‑‑keyboard=ps2 | usb] [‑‑uartN=off | IO‑baseIRQ] [‑‑uart‑modeN=disconnected | serverpipe | clientpipe | tcpserverport | tcpclienthostname:port | filefilename | device‑name] [‑‑uart‑typeN=16450 | 16550A | 16750] [‑‑lpt‑modeN=device‑name] [‑‑lptN=off | IO‑baseIRQ] [‑‑audio‑controller=ac97 | hda | sb16] [‑‑audio‑codec=stac9700 | ad1980 | stac9221 | sb16] [‑‑audio‑driver=none | default | null | dsound | was | oss | alsa | pulse | coreaudio] [‑‑audio‑enabled=on | off] [‑‑audio‑in=on | off] [‑‑audio‑out=on | off] [‑‑clipboard‑mode=disabled | hosttoguest | guesttohost | bidirectional] [‑‑clipboard‑file‑transfers=enabled | disabled] [‑‑drag‑and‑drop=disabled | hosttoguest | guesttohost | bidirectional] [‑‑monitor‑count=number] [‑‑usb‑ehci=on | off] [‑‑usb‑ohci=on | off] [‑‑usb‑xhci=on | off] [‑‑usb‑rename=old‑namenew‑name]
VBoxManage modifyvm <uuid | vmname> [‑‑recording=on | off] [‑‑recording‑screens=all | none | screen‑ID[,screen‑ID...]] [‑‑recording‑file=filename] [‑‑recording‑max‑size=MB] [‑‑recording‑max‑time=seconds] [‑‑recording‑opts=key=value[,key=value...]] [‑‑recording‑video‑fps=fps] [‑‑recording‑video‑rate=rate] [‑‑recording‑video‑res=widthxheight]
VBoxManage modifyvm <uuid | vmname> [‑‑vrde=on | off] [‑‑vrde‑property=property‑name= [property‑value]] [‑‑vrde‑extpack=default | name] [‑‑vrde‑port=port] [‑‑vrde‑address=hostip] [‑‑vrde‑auth‑type=null | external | guest] [‑‑vrde‑auth‑library=default | name] [‑‑vrde‑multi‑con=on | off] [‑‑vrde‑reuse‑con=on | off] [‑‑vrde‑video‑channel=on | off] [‑‑vrde‑video‑channel‑quality=percent]
VBoxManage modifyvm <uuid | vmname> [‑‑teleporter=on | off] [‑‑teleporter‑port=port] [‑‑teleporter‑address=address | empty] [‑‑teleporter‑password=password] [‑‑teleporter‑password‑file=filename | stdin] [‑‑cpuid‑portability‑level=level] [‑‑cpuid‑set=leaf [:subleaf]eax ebx ecx edx] [‑‑cpuid‑remove=leaf [:subleaf]] [‑‑cpuid‑remove‑all]
VBoxManage modifyvm <uuid | vmname> [‑‑tracing‑enabled=on | off] [‑‑tracing‑config=string] [‑‑tracing‑allow‑vm‑access=on | off]
VBoxManage modifyvm <uuid | vmname> [‑‑usb‑card‑reader=on | off]
VBoxManage modifyvm <uuid | vmname> [‑‑autostart‑enabled=on | off] [‑‑autostart‑delay=seconds]
VBoxManage modifyvm <uuid | vmname> [‑‑guest‑debug‑provider=none | native | gdb | kd] [‑‑guest‑debug‑io‑provider=none | tcp | udp | ipc] [‑‑guest‑debug‑address=IP‑Address | path] [‑‑guest‑debug‑port=port]
VBoxManage modifyvm <uuid | vmname> [‑‑pci‑attach=host‑PCI‑address [@guest‑PCI‑bus‑address]] [‑‑pci‑detach=host‑PCI‑address]
VBoxManage modifyvm <uuid | vmname> [‑‑testing‑enabled=on | off] [‑‑testing‑mmio=on | off] [‑‑testing‑cfg‑dwordidx=value]
Description
The VBoxManage modifyvm command enables you to change the properties of a registered virtual machine (VM) that is not running.

Most of these properties correspond to the VM settings that are shown in each VM's Settings dialog in the VirtualBox Manager. See Working with Virtual Machines. However, some settings can only be viewed and managed with the VBoxManage command.

You can use the VBoxManage modifyvm command to change VM settings only when the VM is powered off. The VM cannot be running or in saved state when you use this command.

You can use the VBoxManage controlvm command to dynamically change some VM machine settings while the VM is running. See VBoxManage controlvm.

General Settings
VBoxManage modifyvm <uuid | vmname> [‑‑name=name] [‑‑groups=group [,group...]] [‑‑description=description] [‑‑os‑type=OS‑type] [‑‑icon‑file=filename] [‑‑memory=size‑in‑MB] [‑‑page‑fusion=on | off] [‑‑vram=size‑in‑MB] [‑‑acpi=on | off] [‑‑ioapic=on | off] [‑‑hardware‑uuid=UUID] [‑‑cpus=CPU‑count] [‑‑cpu‑hotplug=on | off] [‑‑plug‑cpu=CPU‑ID] [‑‑unplug‑cpu=CPU‑ID] [‑‑cpu‑execution‑cap=number] [‑‑x86‑pae=on | off] [‑‑x86‑long‑mode=on | off] [‑‑ibpb‑on‑vm‑exit=on | off] [‑‑ibpb‑on‑vm‑entry=on | off] [‑‑spec‑ctrl=on | off] [‑‑l1d‑flush‑on‑sched=on | off] [‑‑l1d‑flush‑on‑vm‑entry=on | off] [‑‑mds‑clear‑on‑sched=on | off] [‑‑mds‑clear‑on‑vm‑entry=on | off] [‑‑cpu‑profile=host | Intel 8086 | Intel 80286 | Intel 80386] [‑‑x86‑hpet=on | off] [‑‑hwvirtex=on | off] [‑‑triple‑fault‑reset=on | off] [‑‑apic=on | off] [‑‑x86‑x2apic=on | off] [‑‑arm‑gic‑its=on | off] [‑‑paravirt‑provider=none | default | legacy | minimal | hyperv | kvm] [‑‑paravirt‑debug=key=value[,key=value...]] [‑‑nested‑paging=on | off] [‑‑large‑pages=on | off] [‑‑x86‑vtx‑vpid=on | off] [‑‑x86‑vtx‑ux=on | off] [‑‑nested‑hw‑virt=on | off] [‑‑virt‑vmsave‑vmload=on | off] [‑‑accelerate‑3d=on | off] [‑‑chipset=ich9 | piix3 | armv8virtual] [‑‑iommu=none | automatic | amd | intel] [‑‑tpm‑type=none | 1.2 | 2.0 | host | swtpm] [‑‑tpm‑location=location] [‑‑firmware‑logo‑fade‑in=on | off] [‑‑firmware‑logo‑fade‑out=on | off] [‑‑firmware‑logo‑display‑time=msec] [‑‑firmware‑logo‑image‑path=pathname] [‑‑firmware‑boot‑menu=disabled | menuonly | messageandmenu] [‑‑firmware‑apic=disabled | apic | x2apic] [‑‑firmware‑system‑time‑offset=msec] [‑‑firmware‑pxe‑debug=on | off] [‑‑system‑uuid‑le=on | off] [‑‑bootX=none | floppy | dvd | disk | net] [‑‑rtc‑use‑utc=on | off] [‑‑graphicscontroller=none | vboxvga | vmsvga | vboxsvga | qemuramfb] [‑‑snapshot‑folder=default | pathname] [‑‑firmware=bios | efi | efi32 | efi64] [‑‑guest‑memory‑balloon=size‑in‑MB] [‑‑default‑frontend=default | name] [‑‑vm‑process‑priority=default | flat | low | normal | high] [‑‑vm‑execution‑engine=default | hm | hwvirt | nem | native‑api | interpreter | recompiler]

The following options enable you to modify general information about your VM.

The VBoxManage modifyvm command supports the following options:

--name=vmname
Changes the name of the VM and its related internal VM files. See VBoxManage createvm.

--groups=group
Changes the group membership of a VM. Group names always begin with a slash character (/) and can be nested. By default, VMs are members of the / group. A VM can be member of multiple groups, but its primary group determines the directory structure where the internal VM files are placed by default.

--description=desc
Changes the optional VM description. Use a description to record details about the VM in a meaningful way. The GUI interprets HTML markup while the VBoxManage modifyvm command enables you include arbitrary strings that can contain multiple lines.

--os-type=OS-type
Specifies the guest operating system (OS) information for the VM. Use the VBoxManage list ostypes command to view the OS type identifiers.

--icon-file=filename
Specifies the path to the VM icon file in PNG format on the host system. The icon is shown in the VM manager UI and when running the VM with UI.

--memory=size
Specifies the amount of host system RAM to allocate to the VM. The size is in MB. See Creating a New Virtual Machine.

--page-fusion=on | off
Enables or disables the Page Fusion feature, which is disabled by default. Use the Page Fusion feature to minimize the memory duplication between VMs that have similar configurations and that run on the same host system. See Page Fusion.

--vram=size
Specifies the amount of RAM to allocate to the virtual graphics card. See Display Settings.

--acpi=on | off
Determines whether the VM has ACPI support. See Motherboard Tab.

--ioapic=on | off
Determines whether the VM has I/O APIC support. See Motherboard Tab.

--hardware-uuid=uuid
Specifies the Universally Unique Identifier (UUID) to present to the guest VM in memory tables (DMI/SMBIOS), hardware, and VM properties. By default this hardware UUID is the same as the VM UUID. Cloning a VM and the teleporting feature automatically preserve the hardware UUID value. Likewise for Virtual Appliance export and import, but only if both operations are done by Oracle VirtualBox.

--cpus=CPU-count
Specifies the number of virtual CPUs to assign to the VM. See Processor Tab.

If CPU hot-plugging is enabled, this option specifies the maximum number of virtual CPUs that can be plugged into the VMs.

--cpu-hotplug=on | off
Enables or disables CPU hot-plugging. When enabled, you can dynamically add virtual CPUs to a VM or remove virtual CPUs from a VM. See CPU Hot-Plugging.

--plug-cpu=CPU-ID
Adds a virtual CPU to the VM. CPU-ID is the index of the virtual CPU to add. A valid index value is a number from 0 to the maximum number of CPUs that you configured by using the --cpus option.

Only use this option if CPU hot-plugging is enabled.

--unplug-cpu=CPU-ID
Removes a virtual CPU from the VM. CPU-ID is the index of the virtual CPU to remove. A valid index value is a number from 1 to the maximum number of CPUs that you configured by using the --cpus option.

Only use this option if CPU hot-plugging is enabled.

Note that you cannot remove CPU 0.

--cpuexectioncap=percentage
Specifies how much CPU time a virtual CPU can use. A valid value is from 1 to 100. A value of 50 indicates that a single virtual CPU can use up to 50% of a single host CPU.

Use this feature with caution, it can have unexpected results including timekeeping problems and lower performance than specified. If you want to limit the resource usage of a VM it is more reliable to pick an appropriate number of VCPUs.

--x86-pae=on | off
Enables or disables physical address extension (PAE). See Processor Tab.

--x86-long-mode=on | off
Enables or disables long mode. See Processor Tab.

--ibpb-on-vm-exit=on | off
Enables use of Indirect Branch Prediction Barrier (IBPB) on every VM exit.

--ibpb-on-vm-entry=on | off
Enables use of Indirect Branch Prediction Barrier (IBPB) on every VM entry.

--spec-ctrl=on | off
Enables or disables the exposure of speculation control interfaces to the guest VM. These interfaces must be available on the host system.

Depending on the host CPU and the workload, enabling speculation control might significantly reduce performance.

--l1d-flush-on-sched=on | off
Enables or disables level 1 data cache flushing when a thread is scheduled to execute guest code. See CVE-2018-3646.

--l1d-flush-on-vm-entry=on | off
Enables or disables level 1 data cache flushing on every VM entry. See CVE-2018-3646.

--mds-clear-on-sched=on | off
Enables CPU buffer clearing when a thread is scheduled to execute guest code. See CVE-2018-12126, CVE-2018-12127, CVE-2018-12130, CVE-2019-11091.

--mds-clear-on-vm-entry=on | off
Enables CPU buffer clearing on every VM entry. See CVE-2018-12126, CVE-2018-12127, CVE-2018-12130, CVE-2019-11091.

--cpu-profile=host | Intel 8086 | Intel 80286 | Intel 80386
Specifies the profile to use for guest CPU emulation. Specify a value that is based on the host system CPU (host) or one of the following older Intel micro-architectures: 8086, 80286, or 80386.

--x86-hpet=on | off
Enables or disables a High Precision Event Timer (HPET) that can replace a legacy system timer. This feature is disabled by default. Note HPET is supported on Windows versions starting with Vista.

--hwvirtex=on | off
Enables or disables the use of hardware virtualization extensions in the processor of the host system. Such extensions are Intel VT-x or AMD-V.

--triple-fault-reset=on | off
Enables or disables the resetting of the guest VM instead of triggering a Guru Meditation. Some guest VMs raise a triple fault to reset the CPU, so sometimes resetting the guest VM is the best outcome. This option only applies to guests that do not use symmetric multiprocessing (SMP).

--apic=on | off
Enables or disables APIC. With APIC, OSes can use more than 16 interrupt requests (IRQs) to avoid IRQ sharing and to improve reliability. APIC is enabled by default. See Motherboard Tab.

--x86-x2apic=on | off
Enables or disables the CPU x2APIC feature. CPU x2APIC enables an OS to run more efficiently on high core count configurations and to optimize interrupt distribution in virtualized environments. This feature is enabled by default.

Disable this feature when the OS that runs on a host system or a guest VM is incompatible with CPU x2APIC.

--arm-gic-its=on | off
Enables or disables the ITS (Interrupt Translation Service) component of the GIC (Generic Interrupt Controller). This feature is disabled by default.

--paravirt-provider=none | default | legacy | minimal | hyperv | kvm
Specifies one of the following paravirtualization interfaces to provide to the guest OS:

none does not expose any paravirtualization interface.

default selects the appropriate interface based on the guest OS type when starting the VM. This is the default value used when creating new VMs.

legacy selects a paravirtual interface for VMs that were created by older Oracle VirtualBox versions.

minimal is required for Mac OS X guest VMs.

kvm is recommended for Linux guest VMs. See Paravirtualization Providers.

hyperv is recommended for Windows guest VMs. See Paravirtualization Providers.

--paravirt-debug=property=value
Specifies debugging properties that are specific to the paravirtualization provider configured for the specified VM. See Paravirtualized Debugging.

--nested-paging=on | off
Enables or disables the nested paging feature in the processor of the host system. This option is available only when hardware virtualization is enabled. See CVE-2018-3646.

--large-pages=on | off
Enables or disables the hypervisor's use of large pages, which can improve performance by up to 5%. The use of large pages reduces TLB use and overhead. This option is available only when both hardware virtualization and nested paging are enabled.

--x86-vtx-vpid=on | off
Enables or disables the use of the tagged TLB (VPID) feature in the processor of your host system. This option is available only when hardware virtualization is enabled on Intel VT-x.

--x86-vtx-ux=on | off
Enables or disables the use of unrestricted guest mode for executing the guest VM. This option is available only when hardware virtualization is enabled on Intel VT-x.

--nested-hw-virt=on | off
Enables or disables nested virtualization. Enabling makes hardware virtualization features available to the VM. See Nested Virtualization.

--virt-vmsave-vmload=on | off
If hardware virtualization is enabled and the host has an AMD CPU, this setting enables or disables the use of the virtualized vmsave/vmload host feature while executing the VM. It is enabled by default. It is recommended to leave it enabled as it has a drastic impact on performance while executing nested VMs when using the nested hardware virtualization feature. Nested Virtualization.

--accelerate-3d=on | off
Enables or disables hardware 3D acceleration for the graphics adapter variants which support it. This option has an effect only when the Guest Additions are installed. See Hardware-Accelerated Graphics.

--chipset=piix3 | ich9 | armv8virtual
Specify the Intel chipset for Oracle VirtualBox to emulate. For the x86 platform, the default value is the Intel PIIX3 chipset. (piix3). For the ARM platform, the default value is the ARMv8Virtual chipset. (armv8virtual).

Change this value only if you need to relax some of the chipset constraints. See Motherboard Tab.

--iommu=none | automatic | amd | intel
Specifies the IOMMU type for Oracle VirtualBox to emulate. Both Intel and AMD IOMMU emulation currently require the use of the Intel ICH9 chipset (see --chipset option).

Valid values are as follows:

none – No IOMMU is present and is the default value.

automatic – An IOMMU is present but its type is automatically chosen to match the host CPU vendor when the VM is powered on.

amd – An AMD IOMMU is present.

intel – An Intel IOMMU is present.

--tpm-type=none | 1.2 | 2.0 | host | swtpm
Specifies the TPM type for Oracle VirtualBox to emulate.

Valid values are as follows:

none – No TPM is present and is the default value.

1.2 – A TPM conforming to the TCG specification version 1.2 is present.

2.0 – A TPM conforming to the TCG specification version 2.0 is present.

host – The host TPM is passed through to the guest. May not be available on all supported host platforms.

swtpm – The VM connects to an external TPM emulation compliant to swtpm. Requires to set the TPM location to connect to (see --tpm-location option).

--firmware-logo-fade-in=on | off
Specifies whether the BIOS logo fades in on VM startup. By default, an Oracle VirtualBox logo is shown.

--firmware-logo-fade-out=on | off
Specifies whether the BIOS logo fades out on VM startup.

--firmware-logo-display-time=msec
Specifies the amount of time in milliseconds that the BIOS logo is visible.

--firmware-logo-image-path=pathname
Replaces the existing BIOS logo with a different image. The replacement image must be an uncompressed 16, 256 or 16M color bitmap file (BMP) that does not contain color space information (Windows 3.0 format). Also ensure that the image is no larger than 640 X 480 pixels.

--firmware-boot-menu=disabled | menuonly | messageandmenu
Specifies whether the BIOS permits you to select a temporary boot device. Valid values are:

disabled outputs the alternate boot device message and permits you to select a temporary boot device by pressing F12.

menuonly suppresses the alternate boot device message, but permits you to select a temporary boot device by pressing F12.

messageandmenu suppresses the alternate boot device message and prevents you from selecting a temporary boot device by pressing F12.

--firmware-apic=x2apic | apic | disabled
Specifies the APIC level of the firmware. Valid values are: x2apic, apic, and disabled. When the value is disabled, neither the apic nor the x2apic version of the firmware is used.

Note that if you specify the x2apic value and x2APIC is unsupported by the virtual CPU, the APIC level downgrades to apic, if supported. Otherwise, the APIC level downgrades to disabled. Similarly, if you specify the apic value and APIC is unsupported by the virtual CPU, the APIC level downgrades to disabled.

--firmware-system-time-offset=msec
Specifies the time offset in milliseconds of the guest VM relative to the time on the host system. If the offset value is positive, the guest VM time runs ahead of the time on the host system.

--firmware-pxe-debug=on | off
Enables or disables additional debugging output when using the Intel PXE boot ROM. The debug output is written to the release log file. See Collecting Debugging Information.

--system-uuid-le=on | off
Enables or disables representing the system UUID in little endian form. The default value is on for new VMs. For old VMs the setting is off to keep the content of the DMI/SMBIOS table unchanged, which can be important for Windows license activation.

--bootN=none | floppy | dvd | disk | net
Enables you to specify the boot device order for the VM by assigning one of the device types to each of the four boot device slots that are represented by N in the option name.

A value of 1 for N represents the first boot device slot, and so on.

The device types are floppy for floppy disks, dvd for DVDs or CDs, disk for hard disks, and net for a network device. A value of none indicates that no boot device is associated with the specified slot.

--rtc-use-utc=on | off
Specifies whether the real-time clock (RTC) uses coordinated universal time (UTC). See Motherboard Tab.

--graphicscontroller=none | vboxvga | vmsvga | vboxsvga
Specifies the graphics controller type to use. See Screen Tab.

--snapshot-folder=default | pathname
Specifies the name of the VM's snapshot storage folder. If you specify default, the folder name is Snapshots/ in the machine folder.

--firmware=bios | efi | efi32 | efi64
Specifies the firmware used to boot the VM. Valid values are: bios, efi, efi32, or efi64. Use EFI values with care.

By default, BIOS firmware is used.

--guest-memory-balloon=size
Specifies the size of the guest memory balloon. The guest memory balloon is the memory allocated by the Guest Additions from the guest OS and returned to the hypervisor for use by other VMs. Specify size in megabytes. The default value is 0 megabytes. See Memory Ballooning.

--default-frontend=default | name
Specifies the default frontend to use when starting the specified VM. If you specify default, the VM is shown in a window on the user's desktop. See VBoxManage startvm.

--vm-process-priority=default | flat | low | normal | high
Specifies the priority scheme of the VM process to use when starting the specified VM and while the VM runs.

The following valid values are:

default – Default process priority determined by the OS.

flat – Assumes a scheduling policy which puts the process at the default priority and with all threads at the same priority.

low – Assumes a scheduling policy which puts the process mostly below the default priority of the host OS.

normal – Assume a scheduling policy which shares the CPU resources fairly with other processes running with the default priority of the host OS.

high – Assumes a scheduling policy which puts the task above the default priority of the host OS. This policy might easily cause other tasks in the system to starve.

Networking Settings
VBoxManage modifyvm <uuid | vmname> [‑‑nicN=none | null | nat | bridged | intnet | hostonly | hostonlynet | generic | natnetwork | cloud] [‑‑nic‑typeN=Am79C970A | Am79C973 | 82540EM | 82543GC | 82545EM | virtio | usbnet] [‑‑cable‑connectedN=on | off] [‑‑nic‑traceN=on | off] [‑‑nic‑trace‑fileN=filename] [‑‑nic‑propertyN=name= [value]] [‑‑nic‑speedN=kbps] [‑‑nic‑boot‑prioN=priority] [‑‑nic‑promiscN=deny | allow‑vms | allow‑all] [‑‑nic‑bandwidth‑groupN=none | name] [‑‑bridge‑adapterN=none | device‑name] [‑‑cloud‑networkN=network‑name] [‑‑host‑only‑adapterN=none | device‑name] [‑‑host‑only‑netN=network‑name] [‑‑intnetN=network‑name] [‑‑nat‑networkN=network‑name] [‑‑nic‑generic‑drvN=driver‑name] [‑‑mac‑addressN=auto | MAC‑address]

The following options enable you to modify networking on your VM. With all these options, N is an integer greater than zero that represents the particular virtual network adapter to configure.

--nicN=none | null | nat | natnetwork | bridged | intnet | hostonly | generic
Configures the network type used by each virtual network card in the VM.

The following valid values correspond to the modes described in Introduction to Networking Modes:

none – No networking present

null – Not connected to the host system

nat – Use network address translation (NAT)

natnetwork – Use a NAT network

bridged – Use bridged networking

intnet – Use internal networking

hostonly – Use host-only networking

generic – Access rarely used sub-modes

--nic-typeN=Am79C970A | Am79C973 | 82540EM | 82543GC | 82545EM | virtio | usbnet
Identifies the type of networking hardware that Oracle VirtualBox presents to the guest VM for the specified virtual network card. See Virtual Networking Hardware.

Valid values are as follows:

Am79C970A represents the AMD PCNet PCI II.

Am79C973 represents the AMD PCNet FAST III, which is the default value.

82540EM represents the Intel PRO/1000 MT Desktop.

82543GC represents the Intel PRO/1000 T Server.

82545EM represents the Intel PRO/1000 MT Server.

virtio represents a paravirtualized network adapter.

usbnet represents an Ethernet over USB network adapter.

--cable-connectedN=on | off
Temporarily disconnects a virtual network interface, as if you pull a network cable from a physical network card. You might use this option to reset certain software components in the VM.

--nic-traceN=on | off
Enables or disables network tracing for the specified virtual network card.

--nic-trace-fileN=filename
Specifies the absolute path of the file in which to write trace log information. Use this option if network tracing is enabled.

--nic-propertyN=name=value
Enables you to set property values and pass them to rarely used network backends. To use this option, you must also use the --nic-generic-drv option.

These properties are specific to the backend engine and differ between the UDP Tunnel and the VDE backend drivers. For property examples, see UDP Tunnel Networking.

--nic-speedN=kbps
Specifies the throughput rate in kilobits per second for rarely used networking sub-modes such as VDE network and UDP Tunnel. Use this option only if you used the --nic option to enable generic networking for the specified virtual network card.

--nic-boot-prioN=priority
Assigns a priority to each NIC that determines the order in which that NIC is used to perform a PXE network boot. The priority value is an integer in the range from 0 to 4. Priority 0, which is the default value, is the lowest priority. Priority 1 is the highest priority, and priorities 3 and 4 are lower.

This option has an effect only when using the Intel PXE boot ROM.

--nic-promiscN=deny | allow-vms | allow-all
Enables you to specify whether to deny or allow promiscuous mode for the specified VM virtual network card. This option is relevant only for bridged networking. Valid values are as follows:

deny hides any traffic that is not intended for the VM. This is the default value.

allow-vms hides all host traffic from the VM, but allows the VM to see traffic to and from other VMs.

allow-all allows the VM to see all traffic.

--nic-bandwidth-groupN=none | name
Adds or removes a bandwidth group assignment to the specified virtual network interface. Valid values are as follows:

none removes any current bandwidth group assignment from the specified virtual network interface.

name adds a bandwidth group assignment to the specified virtual network interface.

See Limiting Bandwidth for Network Input/Output.

--bridge-adapterN=none | device-name
Specifies the host interface to use for the specified virtual network interface. See Bridged Networking. Use this option only if you used the --nic option to enable bridged networking for the specified virtual network card.

--host-only-adapterN=none | device-name
Specifies which host-only networking interface to use for the specified virtual network interface. See Host-Only Networking. Use this option only if you used the --nic option to enable host-only networking for the specified virtual network card.

--intnetN=network-name
Specifies the name of the internal network. See Internal Networking. Use this option only if you used the --nic option to enable internal networking for the specified virtual network card.

--nat-networkN=network-name
Specifies the name of the NAT network to which this adapter is connected. Use this option only if the networking type is natnetwork, not nat.

--nic-generic-drvN=backend-driver
Enables you to access rarely used networking sub-modes, such as VDE networks and UDP Tunnel. Use this option only if you used the --nic option to enable generic networking for a virtual network card.

--mac-addressN=auto | MAC-address
Specifies the MAC address of the specified network adapter on the VM. By default, Oracle VirtualBox assigns a random MAC address to each network adapter at VM creation.

NAT Networking Settings
VBoxManage modifyvm <uuid | vmname> [‑‑nat‑netN=network | default] [‑‑nat‑pfN=[rule‑name],tcp | udp,[host‑IP],hostport,[guest‑IP],guestport] [‑‑nat‑pfN=delete=rule‑name] [‑‑nat‑tftp‑prefixN=prefix] [‑‑nat‑tftp‑fileN=filename] [‑‑nat‑tftp‑serverN=IP‑address] [‑‑nat‑bind‑ipN=IP‑address] [‑‑nat‑dns‑pass‑domainN=on | off] [‑‑nat‑localhostreachableN=on | off] [‑‑nat‑settingsN=[mtu]] [‑‑nat‑forward‑broadcastN=on | off] [‑‑nat‑enable‑tftpN=on | off]

The following options use N to specify the particular virtual network adapter to modify.

--nat-netN=default | network
Specifies the IP address range to use for this network. See Fine Tuning the Oracle VirtualBox NAT Engine. Use this option only if the networking type is nat, not natnetwork.

--nat-pfN=[name],tcp | udp,[host-IP],hostport,[guest-IP],guestport
Specifies the NAT port-forwarding rule to use. See Configuring Port Forwarding with NAT.

--nat-pfN=delete name
Specifies the NAT port-forwarding rule to delete. See Configuring Port Forwarding with NAT.

--nat-tftp-prefixN=prefix
Specifies a prefix to use for the built-in TFTP server. For example, you might use a prefix to indicate where the boot file is located. See PXE Booting with NAT and Configuring the Boot Server (Next Server) of a NAT Network Interface.

--nat-tftp-fileN=boot-file
Specifies the name of the TFT boot file. See Configuring the Boot Server (Next Server) of a NAT Network Interface.

--nat-tftp-serverN=tftp-server
Specifies the address of the TFTP server from which to boot. See Configuring the Boot Server (Next Server) of a NAT Network Interface.

--nat-bind-ipN=IP-address
Specifies an alternate IP address to which the NAT engine binds. See Fine Tuning the Oracle VirtualBox NAT Engine. By default, Oracle VirtualBox's NAT engine routes TCP/IP packets through the default interface assigned by the host's TCP/IP stack.

--nat-dns-pass-domainN=on | off
Specifies whether the built-in DHCP server passes the domain name for network name resolution.

--nat-localhostreachableN=on | off
Specifies whether the NAT engine allows traffic from the guest directed to ******** to pass to the host's loopback interface, i.e. localhost or 127.0.0.1.

--nat-settingsN=[mtu]
Specifies values for tuning NAT performance. See Fine Tuning the Oracle VirtualBox NAT Engine.

Other Hardware Settings
VBoxManage modifyvm <uuid | vmname> [‑‑mouse=ps2 | usb | usbtablet | usbmultitouch | usbmtscreenpluspad] [‑‑keyboard=ps2 | usb] [‑‑uartN=off | IO‑baseIRQ] [‑‑uart‑modeN=disconnected | serverpipe | clientpipe | tcpserverport | tcpclienthostname:port | filefilename | device‑name] [‑‑uart‑typeN=16450 | 16550A | 16750] [‑‑lpt‑modeN=device‑name] [‑‑lptN=off | IO‑baseIRQ] [‑‑audio‑controller=ac97 | hda | sb16] [‑‑audio‑codec=stac9700 | ad1980 | stac9221 | sb16] [‑‑audio‑driver=none | default | null | dsound | was | oss | alsa | pulse | coreaudio] [‑‑audio‑enabled=on | off] [‑‑audio‑in=on | off] [‑‑audio‑out=on | off] [‑‑clipboard‑mode=disabled | hosttoguest | guesttohost | bidirectional] [‑‑clipboard‑file‑transfers=enabled | disabled] [‑‑drag‑and‑drop=disabled | hosttoguest | guesttohost | bidirectional] [‑‑monitor‑count=number] [‑‑usb‑ehci=on | off] [‑‑usb‑ohci=on | off] [‑‑usb‑xhci=on | off] [‑‑usb‑rename=old‑namenew‑name]

The following options enable you to configure other hardware, such as the serial port, monitor, audio device, USB ports, and the clipboard, and drag-and-drop features.

--mouse=ps2 | usb | usbtablet | usbmultitouch | usbmtscreenpluspad
Specifies the mode of the mouse to use in the VM. Valid values are: ps2, usb, usbtablet, usbmultitouch and usbmtscreenpluspad.

--keyboard=ps2 | usb
Specifies the mode of the keyboard to use in the VM. Valid values are: ps2 and usb.

--uartN=off | I/O-base IRQ
Configures virtual serial ports for the VM. N represents the serial port to modify. Valid values are off to disable the port or an I/O base address and IRQ. For information about the traditional COM port I/O base address and IRQ values, see Serial Ports.

--uart-modeN=mode
Specifies how Oracle VirtualBox connects the specified virtual serial port to the host system that runs the VM. See Serial Ports.

Ensure that you first configure the virtual serial port by using the --uartN option.

Specify one of the following connection modes for each port:

disconnected indicates that even though the serial port is shown to the guest VM, it is not connected. This state is like a physical COM port without a cable attached.

server pipe-name creates the specified named pipe or local domain socket on the host system and connects the virtual serial device to it.

On a Windows host system, pipe-name is a named pipe that has a name that uses the following form: \\.\pipe\pipe-name.

On a Linux host system, pipe-name is a local domain socket.

client pipe-name connects the virtual serial device to the specified named pipe or local domain socket.

Note that the named pipe or local domain socket must already exist.

tcpserver port creates a TCP socket with the specified TCP port on the host system and connects the virtual serial device to it.

For UNIX-like systems, use ports over 1024 for non-root users.

tcpclient hostname:port connects the virtual serial device to the TCP socket.

Note that the TCP socket must already exist.

file filename redirects the serial port output to the specified raw file. Ensure that filename is the absolute path of the file on the host system.

device-name: specifies the device name of a physical hardware serial port on the specified host system to which the virtual serial port connects.

Use this mode to connect a physical serial port to a VM.

On a Windows host system, the device name is a COM port such as COM1. On a Linux host system, the device name is similar to /dev/ttyS0.

--uart-typeN=UART-type
Configures the UART type for the specified virtual serial port (N). Valid values are 16450, 16550A, and 16750. The default value is 16550A.

--lpt-modeN=device-name
Specifies the device name of the parallel port to use.

For a Windows host system, use a device name such as lpt1. For a Linux host system, use a device name such as /dev/lp0.

--lptN=I/O-base IRQ
Specifies the I/O base address and IRQ of the parallel port.

You can view the I/O base address and IRQ that the VM uses for the parallel port in the Device Manager.

--audio-controller=controller-type
Specifies the audio controller to be used with the VM. Valid audio controller type values are: ac97, hda, and sb16.

--audio-codec=codec-type
Specifies the audio codec to be used with the VM. Valid audio codec type values are: stac9700, ad1980, stac9221, and sb16.

--audio-driver=type
Specifies whether which audio driver (backend) to use. none, default, null, dsound, was, oss, alsa, pulse, and coreaudio.

Note that the audio driver are dependent on the host operating system. Use the VBoxManage modifyvm command usage output to determine the supported audio types for your host system.

For maximum interoperability between hosts, the default audio driver can be used. The VM will then automatically select the most appropriate audio driver for the current host available.

--audio-enabled=on|off
Specifies whether to enable or disable audio for the VM.

This option has precedence over the --audio-on and --audio-off options, i.e. turning off audio via this option will turn off both, input and output, audio.

--audio-in=on|off
Specifies whether to enable or disable audio capture from the host system.

--audio-out=on|off
Specifies whether to enable or disable audio playback from the guest VM.

--clipboard-mode=value
Specifies how to share the guest VM or host system OS's clipboard with the host system or guest VM, respectively. Valid values are: disabled, hosttoguest, guesttohost, and bidirectional. See General Settings.

The clipboard feature is available only if you have the Guest Additions be installed in the VM.

--clipboard-file-transfers=value
Specifies whether file transfers via clipboard between the guest VM and the host are enabled or not. Valid values are: disabled, enabled. Depends on the current clipboard mode being set.

This clipboard file transfer feature is available only if you have the Guest Additions be installed in the VM.

--drag-and-drop=value
Specifies how to use the drag and drop feature between the host system and the VM. Valid values are: disabled, hosttoguest, guesttohost, and bidirectional. See Drag and Drop.

The drag and drop feature is available only if you have the Guest Additions be installed in the VM.

--monitor-count=count
Enables you to configure multiple monitors. See Display Settings.

--usb-ohci=on | off
Enables or disables the VM's virtual USB 1.1 controller. See USB Settings.

--usb-ehci=on | off
Enables or disables the VM's virtual USB 2.0 controller. See USB Settings.

--usb-xhci=on | off
Enables or disables the VM's virtual USB 3.0 controller. This is the most efficient option if the VM supports it. See USB Settings.

--usb-rename=old-name new-name
Rename's the VM's virtual USB controller from old-name to new-name.

Recording Settings
VBoxManage modifyvm <uuid | vmname> [‑‑recording=on | off] [‑‑recording‑screens=all | none | screen‑ID[,screen‑ID...]] [‑‑recording‑file=filename] [‑‑recording‑max‑size=MB] [‑‑recording‑max‑time=seconds] [‑‑recording‑opts=key=value[,key=value...]] [‑‑recording‑video‑fps=fps] [‑‑recording‑video‑rate=rate] [‑‑recording‑video‑res=widthxheight]

The following options enable you to modify settings for video recording, audio recording, or both.

--recording=on | off
Enables or disables the recording of a VM session into a WebM or VP8 file. When set to on, recording begins when the VM session starts.

--recording-screens=all | none | screen-ID[,screen-ID...
Enables you to specify the VM screens to record. The recording for each screen is output to its own file. Valid values are: all, which records all screens, none, which records no screens, or one or more specified screens.

--recording-file=filename
Specifies the name of the file in which to save the recording.

--recording-max-size=MB
Specifies the maximum size of the recorded video file in megabytes. When the file reaches the specified size, recording stops. If the value is 0, recording continues until you manually stop recording.

--recording-max-time=seconds
Specifies the maximum amount of time to record in seconds. When the specified time elapses, recording stops. If the value is 0, recording continues until you manually stop recording.

--recording-opts=keyword=value
Specifies additional video-recording properties as a comma-separated property keyword-value list. For example, foo=bar,a=b.

Only use this option if you are an advanced user. For information about keywords, see the Oracle VirtualBox Programming Guide and Reference.

--recording-video-fps=fps
Specifies the maximum number of video frames per second (FPS) to record. The recording ignores any frames that have a higher frequency. When you increase the FPS, fewer frames are ignored but the recording and the size of the recording file increases.

--recording-video-rate=bit-rate
Specifies the bit rate of the video in kilobits per second. When you increase the bit rate, the recording appearance improves and the size of the recording file increases.

--recording-video-res=widthxheight
Specifies the video resolution (width and height) of the recorded video in pixels.

Remote Machine Settings
VBoxManage modifyvm <uuid | vmname> [‑‑vrde=on | off] [‑‑vrde‑property=property‑name= [property‑value]] [‑‑vrde‑extpack=default | name] [‑‑vrde‑port=port] [‑‑vrde‑address=hostip] [‑‑vrde‑auth‑type=null | external | guest] [‑‑vrde‑auth‑library=default | name] [‑‑vrde‑multi‑con=on | off] [‑‑vrde‑reuse‑con=on | off] [‑‑vrde‑video‑channel=on | off] [‑‑vrde‑video‑channel‑quality=percent]

The following options enable you to modify the VirtualBox Remote Desktop Extension (VRDE) behavior.

--vrde=on | off
Enables or disables the VRDE server.

--vrde-property=TCP/Ports=port
port is the port or port range to which the VRDE server binds. The default or 0 value uses port 3389, which is the standard RDP port.

See also the --vrde-port option description.

--vrde-property=TCP/Address=IP-address
IP-address is the IP address of the host network interface to which the VRDE server binds. When specified, the server accepts connections only on the host network interface at that IP address.

See also the --vrde-address option description.

--vrde-property=VideoChannel/Enabled=value
Specifies whether the VRDP video channel is on or off. 1 means on and 0 means off. See VRDP Video Redirection.

--vrde-property=Quality=value
Specifies a value between 10% and 100%, inclusive, that represents the JPEG compression level on the VRDE server video channel. A lower value produces lower JPEG quality but higher compression. See VRDP Video Redirection.

--vrde-property=DownscaleProtection=value
Enables or disables the video downscale protection feature. Valid values are 1 to enable the feature and 0 to disable the feature.

When this feature is enabled, Oracle VirtualBox determines whether to display the video:

When the video size equals the size of the shadow buffer, the video is considered to be full screen and is displayed.

When the video size is between full screen and the downscale threshold, the video is not displayed. Such a video might be an application window, which is unreadable when downscaled.

When this feature is disabled, an attempt is always made to display a video.

--vrde-property=Client/DisableDisplay=1
Disables the display VRDE server feature.

To re-enable a feature, assign an empty value. For example, to re-enable the display feature, specify the VBoxManage modifyvm --vrde-property=Client/DisableDisplay= command. See VRDP Customization.

--vrde-property=DisableInput=1
Disables the input VRDE server feature.

--vrde-property=DisableAudio=1
Disables the audio VRDE server feature.

--vrde-property=DisableUSB=1
Disables the USB VRDE server feature.

--vrde-property=Client/DisableClipboard=1
Disables the clipboard VRDE server feature. To re-enable the feature, assign an empty value. See VRDP Customization.

--vrde-property=DisableUpstreamAudio=1
Disables the upstream audio VRDE server feature. To re-enable the feature, assign an empty value. See VRDP Customization.

--vrde-property=Client/DisableRDPDR=1
Disables the RDP device redirection for smart cards VRDE server feature. To re-enable this feature, assign an empty value.

--vrde-property=H3DRedirect/Enabled=1
Enables the 3D redirection VRDE server feature. To disable this feature, assign an empty value.

--vrde-property=Security/Method=value
Specifies the following information that is required for a connection:

Negotiate indicates that both Enhanced (TLS) and Standard RDP Security connections are permitted. The security method is negotiated with the client. This is the default value.

RDP indicates that only Standard RDP Security is accepted.

TLS indicates that only Enhanced RDP Security is accepted. The client must support TLS.

See RDP Encryption.

--vrde-property=ServerCertificate=value
Specifies the absolute path to the server certificate. See RDP Encryption.

--vrde-property=ServerPrivateKey=value
Specifies the absolute path to the server private key. See RDP Encryption.

--vrde-property=CACertificate=value
Specifies the absolute path to the CA self-signed certificate. See RDP Encryption.

--vrde-property Audio/RateCorrectionMode=value
Specifies the audio connection mode or the path to the audio log file. Valid values are as follows:

VRDP_AUDIO_MODE_VOID is no mode. Use this value to unset any set audio mode.

VRDP_AUDIO_MODE_RC is the rate correction mode.

VRDP_AUDIO_MODE_LPF is the low pass filter mode.

VRDP_AUDIO_MODE_CS is the client sync sync mode to prevent an underflow or overflow of the client queue.

--vrde-property=LogPath=value
Specifies the absolute path to the audio log file.

--vrde-extpack=default | name
Specifies the library to use to access the VM remotely. The default value uses the RDP code that is part of the Oracle VirtualBox Extension Pack.

To use the VRDE module in VNC, specify VNC. See Other Extension Packs.

--vrde-port=default | port
port is the port or port range to which the VRDE server binds. The default or 0 value uses port 3389, which is the standard RDP port.

You can specify a comma-separated list of ports or port ranges of ports. Use a dash between two port numbers to specify a port range. The VRDE server binds to only one of the available ports from the list. Only one machine can use a given port at a time. For example, the --vrde-port=5000,5010-5012 option specifies that server can bind to one of following ports: 5000, 5010, 5011, or 5012.

--vrde-address=IP-address
Specifies the IP address of the host network interface to which the VRDE server binds. If you specify an IP address, the server accepts connections only on the specified host network interface.

Use this option to specify whether the VRDP server should accept IPv4, IPv6, or both type of connections:

Only IPv4: Use the --vrde-address="0.0.0.0" option.

Only IPv6: Use the --vrde-address="::" option.

Both IPv6 and IPv4: Use the --vrde-address="" option. This is the default value.

--vrde-auth-type=null | external | guest
Specify whether to use authorization and how to perform authorization. See RDP Authentication. Valid values are as follows:

null provides no authentication.

external provides external authentication through an authentication library.

guest performs authentication by using guest user accounts. This unsupported method requires that you install the Guest Additions on the VM.

--vrde-auth-library=default | name
Specifies the library to use for RDP authentication. The default library for external authentication is VBoxAuth. See RDP Authentication.

--vrde-multi-con=on | off
Enables or disables the multiple connections VRDE server feature, if supported. See Multiple Connections to the VRDP Server.

--vrde-reuse-con=on | off
Specifies how the VRDE server behaves when multiple connections are disabled. When the value is on, the server permits a new client to connect and drops the existing connection. When the value is off, a new connection is not accepted if a client is already connected to the server. This is the default value.

--vrde-video-channel=on | off
Enables video redirection if supported by the VRDE server. See VRDP Video Redirection.

--vrde-video-channel-quality=percent
Specifies the image quality for video redirection as a value from 10 to 100 percent. The percentage represents the JPEG compression level where a lower number diminishes quality and provides higher compression. See VRDP Video Redirection.

Teleporting Settings
VBoxManage modifyvm <uuid | vmname> [‑‑teleporter=on | off] [‑‑teleporter‑port=port] [‑‑teleporter‑address=address | empty] [‑‑teleporter‑password=password] [‑‑teleporter‑password‑file=filename | stdin] [‑‑cpuid‑portability‑level=level] [‑‑cpuid‑set=leaf [:subleaf]eax ebx ecx edx] [‑‑cpuid‑remove=leaf [:subleaf]] [‑‑cpuid‑remove‑all]

The following options enable you to configure a machine as a teleporting target. See Teleporting and the teleporting related entries in Potentially Insecure Operations.

--teleporter=on | off
Enables or disables the teleporter. When enabled, a machine starts up and waits to receive a teleporting request from the network instead of booting normally.

Teleporting requests are received on the port and address specified using the following parameters.

--teleporter-port=port
Specifies the port on which the VM listens to receive a teleporting request from another VM. port is any free TCP/IP port number, such as 6000. You must also specify the --teleporter option.

--teleporter-address=IP-address
Specifies the IP address on which the VM listens to receive a teleporting request from another VM. IP-address is any IP address or host name and specifies the TCP/IP socket on which to bind. The default IP address is 0.0.0.0, which represents any IP address. You must also specify the --teleporter option.

--teleporter-password=password
Specifies the password to use for authentication. When specified, the teleporting request only succeeds if the password on the source machine is the same password as the one you specify.

--teleporter-password-file=filename
Specifies a file that contains the password to use for authentication. When specified, the teleporting request only succeeds if the password on the source machine is the same password as the one you specify in the password file. A value of stdin reads the password from standard input.

--cpuid-portability-level=level
Restricts the virtual CPU capabilities that Oracle VirtualBox presents to the guest OS by using portability rules. Higher integer values designate more restrictive behavior. The default level of 0 indicates that all virtualized features supported by the host are made available to the guest. The value 3 suppresses most features. Values of 1 and 2 represent restrictions in between. The behavior may change depending on the product version.

--cpuid-set=leaf[:subleaf] eax ebx ecx edx
Advanced users can use this setting before a teleporting operation (in fact before starting the VM) to restrict the virtual CPU capabilities that Oracle VirtualBox presents to the guest operating system. This must be run on both the source and the target machines involved in teleporting and will then modify what the guest sees when it executes the CPUID machine instruction. This might help with misbehaving applications that wrongly assume that certain CPU capabilities are present. The meaning of the parameters is hardware dependent. Refer to the AMD or Intel processor documentation.

The values of leaf, subleaf (optional), eax, ebx, ecx and edx are integers given in hexadecimal format, i.e. using a radix (base) of 16 without requiring any prefix.

--cpuid-remove=leaf[:subleaf]
Removes an adjustment established with --cpuid-set.

--cpuid-remove-all
Removes all adjustments established with --cpuid-set.

Debugging Settings
VBoxManage modifyvm <uuid | vmname> [‑‑tracing‑enabled=on | off] [‑‑tracing‑config=string] [‑‑tracing‑allow‑vm‑access=on | off]

Only use the following options to perform low-level VM debugging. These options are for advanced users only.

--tracing-enabled=on | off
Enables or disables the trace buffer. Note that when specified, the trace buffer consumes some memory and adds overhead.

--tracing-config=config-string
Enables a tracing configuration that defines which group of trace points are enabled.

--tracing-allow-vm-access=on | off
Enables or disables VM access to the trace buffer. The default value is off, which disables access.

USB Card Reader Settings
VBoxManage modifyvm <uuid | vmname> [‑‑usb‑card‑reader=on | off]

The following options specify the access to a USB Card Reader by the guest environment. A USB card reader can access data on memory cards, such as CompactFlash (CF), Secure Digital (SD), and MultiMediaCard (MMC).

--usb-card-reader=on | off
Enables or disables the USB card reader interface.

Autostarting VMs During Host System Boot
The following options enable you to configure the VM autostart feature, which automatically starts the VM at host system boot-up. You must do some host system configuration before you can use this feature. See Starting Virtual Machines During System Boot.

VBoxManage modifyvm <uuid | vmname> [‑‑autostart‑enabled=on | off] [‑‑autostart‑delay=seconds]

--autostart-enabled=on | off
Enables or disables VM autostart at host system boot-up for the specified users.

--autostart-delay=seconds
Specifies the number of seconds after host system boot-up to autostart the VM.

Guest Debugging
These options are for configuring the VMM for guest debugging.

VBoxManage modifyvm <uuid | vmname> [‑‑guest‑debug‑provider=none | native | gdb | kd] [‑‑guest‑debug‑io‑provider=none | tcp | udp | ipc] [‑‑guest‑debug‑address=IP‑Address | path] [‑‑guest‑debug‑port=port]

--guest-debug-provider=none | native | gdb | kd
Selects the given debug stub provider.

--guest-debug-io-provider=none | tcp | udp | ipc
Selects the given I/O transport backend for the selected provider.

--guest-debug-address=IP-Address | path
Sets the path the debugger is accessible under, depends on the selected I/O transport.

--guest-debug-port=port
Sets the port the debugger is accessible under, depends on the selected I/O transport.

PCI Passthrough Settings
The following options enable you to configure the PCI passthrough feature, which currently is not available in Oracle VirtualBox. It is planned to bring this functionality back in the future.

VBoxManage modifyvm <uuid | vmname> [‑‑pci‑attach=host‑PCI‑address [@guest‑PCI‑bus‑address]] [‑‑pci‑detach=host‑PCI‑address]

--pci-attach=host-PCI-address[@guest-PCI-bus-address]
Attaches the specified PCI network controller on the host to the guest VM. You can optionally specify the PCI bus on the guest VM on which to attach the controller.

--pci-detach=host-PCI-address
Detaches the specified PCI network controller from the attached PCI bus on the guest VM.

Testing (ValidationKit / Bootsector)
These options are for configuring the testing functionality of the VMM device and almost exclusively used by the bootsector testcases in the ValidationKit.

VBoxManage modifyvm <uuid | vmname> [‑‑testing‑enabled=on | off] [‑‑testing‑mmio=on | off] [‑‑testing‑cfg‑dwordidx=value]

--testing-enabled=on | off
Enabled the testing functionality of the VMMDev. See VMMDevTesting.h for details.

--testing-mmio=on | off
Enabled the MMIO region of the VMMDev testing feature.

--testing-cfg-dwordidx=value
This sets one of the 10 dword configuration values. The idx must be in the range 0 through 9. The value is limited to 32 bits (dword).

Examples
The following command changes the description for the ol7 VM.

$ VBoxManage modifyvm ol7 --description "Oracle Linux 7 with UEK4"
The following command enables VirtualBox Remote Display Protocol (VRDP) support for the ol7 VM.

$ VBoxManage modifyvm ol7 --vrde on
See Also
VBoxManage showvminfo, VBoxManage controlvm, VBoxManage createvm, VBoxManage startvm VBoxManage list

VBoxManage movevm
Move a virtual machine to a new location on the host system

Synopsis
VBoxManage movevm <uuid | vmname> [‑‑type=basic] [‑‑folder=folder‑name]
Description
The VBoxManage movevm command moves a virtual machine (VM) to a new location on the host system.

When moved, all of the files that are associated with the VM, such as settings files and disk image files, are moved to the new location. The Oracle VirtualBox configuration is updated automatically.

uuid | vmname
Specifies the Universally Unique Identifier (UUID) or name of the VM to move.

--type=basic
Specifies the type of the move operation. So far basic is the only recognized value and also the default if not specified.

--folder=folder-name
Specifies a full path name or relative path name of the new location on the host file system. Not specifying the option or specifying the current location is allowed, and moves disk images and other parts of the VM to this location if they are currently in other locations.

Examples
The following command moves the ol7 VM to a new location on the host system.

$ VBoxManage movevm ol7 --folder "/home/<USER>/vms" --type basic
0%...10%...20%...30%...40%...50%...60%...70%...80%...90%...100%
Machine has been successfully moved into /home/<USER>/vms
VBoxManage natnetwork
Create, modify, and manage a NAT network

Synopsis
VBoxManage natnetwork add [‑‑disable | ‑‑enable] <‑‑netname=name> <‑‑network=network> [‑‑dhcp=on|off] [‑‑ipv6=on|off] [‑‑loopback‑4=rule] [‑‑loopback‑6=rule] [‑‑port‑forward‑4=rule] [‑‑port‑forward‑6=rule]
VBoxManage natnetwork list [filter‑pattern]
VBoxManage natnetwork modify [‑‑dhcp=on|off] [‑‑disable | ‑‑enable] <‑‑netname=name> <‑‑network=network> [‑‑ipv6=on|off] [‑‑loopback‑4=rule] [‑‑loopback‑6=rule] [‑‑port‑forward‑4=rule] [‑‑port‑forward‑6=rule]
VBoxManage natnetwork remove <‑‑netname=name>
VBoxManage natnetwork start <‑‑netname=name>
VBoxManage natnetwork stop <‑‑netname=name>
Description
The VBoxManage natnetwork command enables you to create, modify, and manage a NAT network.

NAT networks use the Network Address Translation (NAT) service. The service groups systems into a network and prevents external systems from directly accessing the systems in the network. The service also enables the systems in the network to communicate with each other and with external systems by means of TCP and UDP over IPv4 and IPv6.

A NAT service is attached to an internal network. For a VM to use the NAT service, you must attach the VM to the internal network. Specify the name of the internal network when you create the NAT service. Note that the internal network is created if it does not already exist.

Add a NAT Network Service
VBoxManage natnetwork add [‑‑disable | ‑‑enable] <‑‑netname=name> <‑‑network=network> [‑‑dhcp=on|off] [‑‑ipv6=on|off] [‑‑loopback‑4=rule] [‑‑loopback‑6=rule] [‑‑port‑forward‑4=rule] [‑‑port‑forward‑6=rule]

The VBoxManage natnetwork add command creates a new internal network interface and adds a NAT network service. You must use this command before you can attach the VM to the NAT network.

--disable
Disables the NAT network service.

--enable
Enables the NAT network service.

--netname=name
Specifies the name of the new internal network interface on the host OS.

--network
Specifies the static or DHCP network address and mask of the NAT service interface. By default, this value specifies the static network address.

--dhcp
Enables or disables the DHCP server specified with the --netname option.

--ipv6
Enables or disables IPv6. By default, IPv6 is disabled and IPv4 is enabled.

--loopback-4=rule
Enables an IPv4 loopback interface using the specified rule.

--loopback-6=rule
Enables an IPv6 loopback interface using the specified rule.

--port-forward-4=rule
Enables IPv4 port forwarding using the specified rule.

--port-forward-6=rule
Enables IPv6 port forwarding using the specified rule.

Remove a NAT Network Service
VBoxManage natnetwork remove <‑‑netname=name>

The VBoxManage natnetwork remove command removes the specified NAT network service.

--netname=name
Specifies the name of the NAT network service to remove.

Start a NAT Network Service
VBoxManage natnetwork start <‑‑netname=name>

The VBoxManage natnetwork start command starts a NAT network service and any associated DHCP server.

--netname=name
Specifies the name of the NAT network service to start.

Stop a NAT Network Service
VBoxManage natnetwork stop <‑‑netname=name>

The VBoxManage natnetwork stop command stops a NAT network service and any associated DHCP server.

--netname=name
Specifies the name of the NAT network service to stop.

List All NAT Network Services
VBoxManage natnetwork list [filter‑pattern]

The VBoxManage natnetwork list command lists all NAT network services. You can use a pattern to show a subset of the NAT network services.

filter-pattern
Specifies an optional filtering pattern.

Modify the Settings of a NAT Network Service
VBoxManage natnetwork modify [‑‑dhcp=on|off] [‑‑disable | ‑‑enable] <‑‑netname=name> <‑‑network=network> [‑‑ipv6=on|off] [‑‑loopback‑4=rule] [‑‑loopback‑6=rule] [‑‑port‑forward‑4=rule] [‑‑port‑forward‑6=rule]

The VBoxManage natnetwork modify command modifies the settings of an existing internal network interface.

--disable
Disables the NAT network service.

--enable
Enables the NAT network service.

--netname=name
Specifies the name of the new internal network interface on the host OS.

--network
Specifies the static or DHCP network address and mask of the NAT service interface. By default, this value specifies the static network address.

--dhcp
Enables or disables the DHCP server specified with the --netname option.

--ipv6
Enables or disables IPv6. By default, IPv6 is disabled and IPv4 is enabled.

--loopback-4=rule
Enables an IPv4 loopback interface using the specified rule.

--loopback-6=rule
Enables an IPv6 loopback interface using the specified rule.

--port-forward-4=rule
Enables IPv4 port forwarding using the specified rule.

--port-forward-6=rule
Enables IPv6 port forwarding using the specified rule.

Examples
The following command shows how to create a NAT network for the natnet1 internal network that uses the ************/24 network address and mask of the NAT service interface. In this static configuration, the gateway is assigned the ************ IP address by default. Note that this IP address is the next address after the network address that you specify with the --network option.

$ VBoxManage natnetwork add --netname natnet1 --network "************/24" --enable
The following command shows how to add a DHCP server to the natnet1 NAT network after creation:

$ VBoxManage natnetwork modify --netname natnet1 --dhcp on
VBoxManage objtracker
Manage the tracked objects

Synopsis
VBoxManage objtracker ifaces
VBoxManage objtracker objlist <‑‑ifacename=VirtualBox interface name>
VBoxManage objtracker objinfo <‑‑ifacename=VirtualBox interface name> <‑‑id=Unique object Id>
Description
objtracker ifaces
VBoxManage objtracker ifaces

Shows the actual list of VirtualBox interfaces supported for tracking

objtracker objlist
VBoxManage objtracker objlist <‑‑ifacename=VirtualBox interface name>

Shows the list of the unique identifiers of the tracked objects existing at moment for a specified interface

--ifacename=vbox interface name
The name which fully identifies VitualBox interface aka IProgress, ISession, IMachine.

objtracker objinfo
VBoxManage objtracker objinfo <‑‑ifacename=VirtualBox interface name> <‑‑id=Unique object Id>

Shows the information about the tracked object

--ifacename=vbox interface name
The name which fully identifies VitualBox interface aka IProgress, ISession, IMachine.

--id=unique object id
The unique identifer assigned to a tracked object by VirtualBox

VBoxManage registervm
Register a virtual machine

Synopsis
VBoxManage registervm <filename>‑‑passwordfile
Description
The VBoxManage registervm command enables you to create a virtual machine (VM) by importing an XML machine configuration file into Oracle VirtualBox. The VM cannot have the same UUID as a VM that is already registered in Oracle VirtualBox. Ensure that the XML machine configuration file is in the machines folder prior to registration.

Note:
When you use the VBoxManage createvm command to create a VM, you can specify the --register option to register the VM.

filename
Specifies the XML machine configuration file. This file has the .vbox file extension.

--password
Use the --password to supply the encryption password of the VM. Either specify the absolute pathname of a password file on the host operating system, or - to prompt you for the password on the command line.

Examples
The following command registers a VM called vm2. The XML machine configuration file for the VM is located in the default machines folder.

$ VBoxManage registervm "/home/<USER>/VirtualBox VMs/vm2/vm2.vbox"
See Also
VBoxManage createvm, VBoxManage unregistervm

VBoxManage setextradata
Set a keyword value that is associated with a virtual machine or configuration

Synopsis
VBoxManage setextradata <global | uuid | vmname> <keyword> [value]
Description
The VBoxManage setextradata command enables you to set a keyword value that is associated with a virtual machine (VM) or with an Oracle VirtualBox configuration.

global
Sets information about the configuration rather than a VM.

uuid | vmname
Specifies the Universally Unique Identifier (UUID) or name of the VM.

keyword
Specifies the keyword for which to set its value.

value
Specifies the keyword value. Specifying no value removes the keyword.

Examples
The following command sets the installdate keyword value for the Fedora5 VM to 2019.01.01:

$ VBoxManage setextradata Fedora5 installdate 2019.01.01
The following command unsets the value of the installdate keyword for the Fedora5 VM:

$ VBoxManage setextradata Fedora5 installdate
See Also
VBoxManage getextradata

VBoxManage setproperty
Change global settings

Synopsis
VBoxManage setproperty <property‑name> <property‑value>
Description
The VBoxManage setproperty command enables you to change global settings that affect the entire Oracle VirtualBox installation. Some of these settings correspond to the settings in the Preferences dialog in the VirtualBox Manager.

The following properties are available:

autostartdbpath
Specifies the path to the autostart database. Valid values are null, which disables the autostart database, or the name of the folder that contains the database. See Starting Virtual Machines During System Boot.

defaultfrontend
Specifies the global default VM frontend. Valid values are default, which specifies the default frontend, or the name of the frontend to use.

hwvirtexclusive
Specifies whether Oracle VirtualBox makes exclusive use of the Intel VT-x or AMD-V hardware virtualization extensions of the host system's processor.

Valid values are as follows:

on enables Oracle VirtualBox to make exclusive use of these extensions. This is the default value.

off shares these extensions with other hypervisors that run simultaneously. Note that sharing these extensions has negative performance implications.

language
Specifies the user language used to translate API messages. Valid values are C, which means no translation or a language code in the form of either ll or ll_CC, where ll is an ISO 639 two-letter language code and CC an ISO 3166 two-letter country code. For example, for Greek in Greece, ll is el, and CC is GR.

logginglevel
Specifies the VBoxSVC release logging details. See http://www.virtualbox.org/wiki/VBoxLogging.

loghistorycount
Specifies the number of rotated VM logs to retain.

machinefolder
Specifies the default folder in which virtual machine (VM) definitions are stored. Valid values are default, which specifies the default storage folder, or the name of the folder to use. See Where Oracle VirtualBox Stores its Files.

proxymode
Configures the mode for an HTTP proxy server. Valid values are as follows:

manual
Configure the URL of a HTTP proxy server manually, using the proxyurl property value.

noproxy
Do not use an HTTP proxy server. A direct connection to the Internet is used.

system
Detect the proxy settings automatically for the host network. This is the default value.

proxyurl
Specifies the URL for an HTTP proxy server when you specify a manual proxy by setting the proxymode property to manual.

vrdeauthlibrary
Specifies which library to use when external authentication has been configured for a particular VM. Valid values are default, which specifies the default library, or the name of the library to use. See RDP Authentication.

vrdeextpack
Specifies the library that implements the VirtualBox Remote Desktop Extension (RDE). Valid values are null, which disables the RDE, or the name of the library to use.

websrvauthlibrary
Specifies which library the web service uses to authenticate users. Valid values are default, which specifies the default library, null, which disables authentication, or the name of the library to use. For information about the Oracle VirtualBox web service, see Oracle VirtualBox Programming Interfaces.

Examples
The following command configures Oracle VirtualBox to use the specified HTTP proxy server.

$ VBoxManage setproperty proxymode manual
$ VBoxManage setproperty proxyurl "http://myproxy.com:8080"
See Also
VBoxManage startvm

VBoxManage sharedfolder
Add and remove shared folders, configure security policy for shared folders

Synopsis
VBoxManage sharedfolder add <global | uuid | vmname> <‑‑name=share‑name> <‑‑hostpath=hostpath> [‑‑readonly] [‑‑transient] [‑‑automount] [‑‑auto‑mount‑point=path]
VBoxManage sharedfolder remove <global | uuid | vmname> <‑‑name=share‑name> [‑‑transient]
VBoxManage sharedfolder modify <uuid | vmname> <‑‑name=share‑name> <‑‑readonly=true | false> <‑‑automount=true | false> <‑‑auto‑mount‑point=path> <‑‑symlink‑policy=forbidden | subtree | relative | any>
Description
Shared folders enable you to share data between the host system and guest VMs. To use shared folders you must first install the Oracle VirtualBox Guest Additions software in the guest VM.

The shared folder is associated with a share name and the full path name of the folder or directory on the host system. The share name is a unique name within the namespace of the host OS.

Add a Shared Folder
VBoxManage sharedfolder add <global | uuid | vmname> <‑‑name=share‑name> <‑‑hostpath=hostpath> [‑‑readonly] [‑‑transient] [‑‑automount] [‑‑auto‑mount‑point=path]

The VBoxManage sharedfolder add command creates a shared folder. The folder you specify is on the host computer. Once created the contents of the folder on the host system can be accessed from within the guest OS.

global
Specifies that the share is global which means that it is available to all virtual machines.

uuid | vmname
Specifies the name or UUID of the guest VM that shares a folder with the host system.

--name=share-name
Specifies the name of the share, which is a unique name within the namespace of the host OS.

--hostpath=hostpath
Specifies the absolute path of the folder or directory on the host OS to share with the guest OS.

--readonly
Specifies that the share has only read-only access to files at the host path.

By default, shared folders have read-write access to the files mounted from the host. However on Solaris and Linux distributions shared folders are mounted with 770 file permissions with the files owned by the root user and the vboxsf group which means the files are restricted to members of the vboxsf group and the root user. If the --readonly option is specified the file permissions become 700 and the files are accessible only to the root user.

--transient
Specifies that the share is transient which means that it is added and removed to a running VM and does not persist after the VM stops.

--automount
Specifies that the share is automatically mounted.

--auto-mount-point=path
Specifies the mount point of the share. This is guest OS specific.

For Windows and OS/2 guests this must be an unused drive letter. If left blank (or if the drive letter is already in use), the last unused drive letter is used instead (i.e. searching from Z: through A:).

For Linux, Solaris and other Unix guests, it must be an absolute path such as /mnt/mysharedfolder. If left empty the default location is /media/sf_sharename.

Remove a Shared Folder
VBoxManage sharedfolder remove <global | uuid | vmname> <‑‑name=share‑name> [‑‑transient]

The VBoxManage sharedfolder remove command removes a shared folder.

global
Specifies that the share is global which means that it is accessible from all applicable guest VMs.

uuid | vmname
Specifies the name or UUID of the guest VM that shares a folder with the host system.

--name=share-name
Specifies the name of the share to remove.

--transient
Specifies that the share is transient which means that it is added and removed to a running VM and does not persist after the VM stops.

Modify a Shared Folder's Configuration
VBoxManage sharedfolder modify <uuid | vmname> <‑‑name=share‑name> <‑‑readonly=true | false> <‑‑automount=true | false> <‑‑auto‑mount‑point=path> <‑‑symlink‑policy=forbidden | subtree | relative | any>

The VBoxManage sharedfolder modify command modifies the configuration of a Shared Folder.

uuid | vmname
Specifies the name or UUID of the guest VM that shares a folder with the host system.

--name=share-name
Specifies the name of the shared folder to modify.

--readonly=true | false
Specifies whether the shared folder is to be mounted as read-only.

--automount=true | false
Specifies whether the shared folder is to be mounted automatically when the VM boots.

--auto-mount-point=path
Specifies where to mount the shared folder if it is configured to be be mounted automatically when the VM boots.

--symlink-policy=policy-name
Specifies the symbolic link security policy of the shared folder. Valid symlink security policies are: forbidden, subtree, relative, and any.

Examples
The following command creates a shared folder named o7share for the ol7 VM and configures the share to be mounted automatically when the VM is started.

$ VBoxManage sharedfolder add ol7 --name ol7share --hostpath "/home/<USER>/ol7share" --automount
The following command removes the shared folder named o7share from the ol7 VM.

$ VBoxManage sharedfolder remove ol7 --name ol7share
VBoxManage showmediuminfo
Show information about a medium

Synopsis
VBoxManage showmediuminfo [disk | dvd | floppy] <uuid | filename>
Description
The VBoxManage showmediuminfo command shows the following information about a medium:

Size

Size on disk

Type

In use by virtual machines (VMs)

The medium must be specified by either its UUID, if the medium is registered, or by its filename. Registered images can be listed using VBoxManage list hdds, VBoxManage list dvds, or VBoxManage list floppies, as appropriate.

For backward compatibility, you can also use the showvdiinfo command to obtain information about the medium.

disk | dvd | floppy
Specifies the type of medium. Valid values are disk (hard drive), dvd, or floppy.

uuid | filename
Specifies the Universally Unique Identifier (UUID) or absolute path name of the medium or image.

If the medium is registered, you can specify the UUID. You can also list registered images by using the VBoxManage list hdds, VBoxManage list dvds, or VBoxManage list floppies command.

Examples
The following command shows information about the disk01.vdi disk image:

$ VBoxManage showmediuminfo disk01.vdi
The following command shows information about the floppy01.img floppy disk image.

$ VBoxManage showmediuminfo floppy floppy01.img
See Also
VBoxManage list

VBoxManage showvminfo
Show configuration information or log file contents for a virtual machine

Synopsis
VBoxManage showvminfo <uuid | vmname> [‑‑details] [‑‑machinereadable] [‑‑password‑id] [‑‑password]
VBoxManage showvminfo <uuid | vmname> <‑‑log=index> [‑‑password‑idid] [‑‑passwordfile|‑]
Description
The VBoxManage showvminfo command outputs configuration information or log file contents for a specified virtual machine (VM).

Viewing Virtual Machine Information
VBoxManage showvminfo <uuid | vmname> [‑‑details] [‑‑machinereadable] [‑‑password‑id] [‑‑password]

The VBoxManage showvminfo command outputs information about the specified VM in a detailed format or in a machine-readable format.

The VBoxManage showvminfo command shows the same information for the specified VM in the same format as the VBoxManage list vms --long command.

--details
Includes detailed information about the VM.

--machinereadable
Specifies that the VM information be in a machine-readable format.

--password-id id
Specifies password id of the VM if it is encrypted.

--password file|-
Specifies password of the VM if it is encrypted. Either specify the absolute pathname of a password file on the host operating system, or - to prompt you for the password.

Viewing Virtual Machine Log Contents
VBoxManage showvminfo <uuid | vmname> <‑‑log=index> [‑‑password‑idid] [‑‑passwordfile|‑]

The VBoxManage showvminfo --log command outputs the contents of one of the specified VM's log files.

--log=index
Specifies a numerical index that identifies the log file.

The index value starts at 0, which indicates the VBox.log file. An index value of 1 indicates the VBoxHardening.log file. Index values starting at 2 indicate other log files, such as the VBox.log.1 file.

--password-id id
Specifies password id of the VM if it is encrypted.

--password file|-
Specifies password of the VM if it is encrypted. Either specify the absolute pathname of a password file on the host operating system, or - to prompt you for the password.

Examples
The following example shows typical output for this command:

$ VBoxManage showvminfo "Windows 10"
Name:            Windows 10
Groups:          /
Guest OS:        Windows 10 (64-bit)
UUID:            1bf3464d-57c6-4d49-92a9-a5cc3816b7e7
Config file:     /home/<USER>/VirtualBox VMs/Windows 10/Windows 10.vbox
Snapshot folder: /home/<USER>/VirtualBox VMs/Windows 10/Snapshots
Log folder:      /home/<USER>/VirtualBox VMs/Windows 10/Logs
Hardware UUID:   1bf3464d-57c6-4d49-92a9-a5cc3816b7e7
Memory size:     2048MB
Page Fusion:     off
VRAM size:       12MB
CPU exec cap:    100%
...
The following example shows the information output in a machine-readable format, which shows the entries as a property=value string:

$ VBoxManage showvminfo "Windows 10" --machinereadable
...
groups="/"
ostype="Windows 10 (64-bit)"
UUID="1bf3464d-57c6-4d49-92a9-a5cc3816b7e7"
...
The following example shows the contents of the VBox.log log file:

$ VBoxManage showvminfo "Windows 10" --log 0
00:00:02.895106 VirtualBox VM 6.0.0_RC1 r127378 linux.amd64 (Dec 10 2018 17:16:06) release log
00:00:02.895109 Log opened 2018-12-14T14:31:44.088259000Z
00:00:02.895111 Build Type: release
00:00:02.895115 OS Product: Linux
00:00:02.895117 OS Release: 4.1.12-61.1.22.el7uek.x86_64
00:00:02.895119 OS Version: #2 SMP Fri Dec 2 09:28:44 PST 2016
...
See Also
VBoxManage list

VBoxManage signova
Digitally sign an OVA

Synopsis
VBoxManage signova <ova> <‑‑certificate=file> <‑‑private‑key=file> [‑‑private‑key‑password‑file=password‑file | ‑‑private‑key‑password=password] [‑‑digest‑type=type] [‑‑pkcs7 | ‑‑no‑pkcs7] [‑‑intermediate‑cert=file] [‑‑force] [‑‑verbose] [‑‑quiet] [‑‑dry‑run]
Description
The VBoxManage signova command adds a digital signature to an OVA file.

ova
The OVA file to sign.

--certificate=file
File containing the certificate that the OVA should be signed with. This can be in either PEM format (base64) or DER (binary).

--private-key=file
The file containing the private key. This can be in either PEM (base64) or DER (binary) format.

--private-key-password-file=password-file
File containing the private key password.

--private-key-password=password
The private key password.

--digest-type=type
Select the cryptographic digest algorithm to use for signing the OVA file. The possible values are : SHA-256 (default), SHA-512 and SHA-1.

Some older versions of VMware's OVF Tool command line utility and other VMware products may require the --digest-type=sha-1 option.

--pkcs7, --no-pkcs7
Enables or disables the creation of an additional PKCS#7/CMS signature. This is enabled by default.

--intermediate-cert=file
File containing an intermediary certificate that should be included in the optional PKCS#7/CMS signature. The file can be in either PEM format (base64) or DER (binary). This option can be repeated to add multiple intermediate certificates. This option implies the --pkcs7 option.

--force
Overwrite existing signature if present. The default behaviour is to fail if the OVA is already signed.

--dry-run
Do not actually modify the OVA, just test-run the signing operation.

-v, --verbose, -q, --quiet
Controls the verbosity of the command execution. The --verbose option can be specified multiple times to get more output.

VBoxManage snapshot
Manage virtual machine snapshots

Synopsis
VBoxManage snapshot <uuid | vmname>
VBoxManage snapshot <uuid | vmname>take <snapshot‑name> [‑‑description=description] [‑‑live] [‑‑uniquename Number,Timestamp,Space,Force]
VBoxManage snapshot <uuid | vmname>delete <snapshot‑name>
VBoxManage snapshot <uuid | vmname>restore <snapshot‑name>
VBoxManage snapshot <uuid | vmname>restorecurrent
VBoxManage snapshot <uuid | vmname>edit <snapshot‑name | ‑‑current> [‑‑description=description] [‑‑name=new‑name]
VBoxManage snapshot <uuid | vmname>list [‑‑details | ‑‑machinereadable]
VBoxManage snapshot <uuid | vmname>showvminfo <snapshot‑name>
Description
The VBoxManage snapshot command manages snapshots.

Oracle VirtualBox uses snapshots to capture the state of a virtual machine (VM). You can later use the snapshot to revert to the state described by the snapshot.

A snapshot is a complete copy of a VM's settings. If you take a snapshot while the VM is running, the snapshot also includes the VM's current running state.

After you take a snapshot, Oracle VirtualBox creates a differencing hard disk for each normal hard disk that is associated with the host machine. When you restore a snapshot, Oracle VirtualBox uses these differencing files to quickly restore the contents of the VM's virtual hard disks.

For each VBoxManage snapshot command, you must specify the name or the universal unique identifier (UUID) of the VM for which you want to take a snapshot.

General Command Operand
uuid | vmname
Specifies the UUID or name of the VM.

Take a Snapshot of a Virtual Machine
VBoxManage snapshot <uuid | vmname>take <snapshot‑name> [‑‑description=description] [‑‑live] [‑‑uniquename Number,Timestamp,Space,Force]

The VBoxManage snapshot take command takes a snapshot of the current state of the VM. You must supply a name for the snapshot and can optionally supply a description. The new snapshot is inserted into the snapshots tree as a child of the current snapshot (if the VM has any snapshots) and then becomes the new current snapshot.

--description=description
Specifies a description of the snapshot.

--live
Specifies that the VM is not stopped while you create the snapshot. This operation is know as live snapshotting.

--uniquename Number,Timestamp,Space,Force
TBD.

snapshot-name
Specifies the name of the snapshot to create.

Delete a Snapshot
VBoxManage snapshot <uuid | vmname>delete <snapshot‑name>

The VBoxManage snapshot delete command removes the specified snapshot.

The delete operation may take some time to finish. This is because the differencing images that are associated with the snapshot may need to be merged with their child differencing images.

snapshot-name
Specifies the UUID or name of the snapshot.

Restore a Snapshot
VBoxManage snapshot <uuid | vmname>restore <snapshot‑name>

The VBoxManage snapshot restore command restores the specified snapshot. This operation resets the VM's settings and current state to that of the snapshot. The state of the VM on which you restore a snapshot is lost. When restored, the specified snapshot becomes the new current snapshot and subsequent snapshots become children of that snapshot.

snapshot-name
Specifies the UUID or name of the snapshot.

Restore the Current Snapshot
VBoxManage snapshot <uuid | vmname>restorecurrent

The VBoxManage snapshot restorecurrent command restores the current snapshot. The current snapshot is the one from which the current state is derived. This command is equivalent to using the VBoxManage snapshot restore command and specifying the name or UUID of the current snapshot.

Change the Name or Description of an Existing Snapshot
VBoxManage snapshot <uuid | vmname>edit <snapshot‑name | ‑‑current> [‑‑description=description] [‑‑name=new‑name]

The VBoxManage snapshot edit command enables you to change the name or the description of a specified snapshot.

snapshot-name
Specifies the UUID or name of the snapshot to edit.

This option is mutually exclusive with the --current option.

--current
Specifies that you update the current version of the snapshot.

This option is mutually exclusive with a specific snapshot name or its UUID.

--description=description
Specifies a new description for the snapshot.

--name=new-name
Specifies a new name for the snapshot.

List the Snapshots
VBoxManage snapshot <uuid | vmname>list [‑‑details | ‑‑machinereadable]

The VBoxManage snapshot list command lists all the snapshots for a VM.

--details
Specifies that the output shows detailed information about the snapshot.

This option is mutually exclusive with the --machinereadable option.

--machinereadable
Specifies that the output is shown in a machine-readable format.

This option is mutually exclusive with the --details option.

Show Information About a Snapshot's Settings
VBoxManage snapshot <uuid | vmname>showvminfo <snapshot‑name>

The VBoxManage snapshot showvminfo command enables you to view the VM settings that are part of an existing snapshot.

snapshot-name
Specifies the UUID or name of the snapshot.

Examples
The following command creates a snapshot of the ol7u4 VM. The snapshot is called ol7u4-snap-001. The command uses the --description option to provide a description of the snapshot contents.

$ VBoxManage snapshot ol7u4 take ol7u4-snap-001 \
--description="Oracle Linux 7.4"
The following command lists the snapshots for the ol7u4 VM.

$ VBoxManage snapshot ol7u4 list
The following command changes the description for the ol7u4-snap-001 snapshot of the ol7u4 VM.

$ VBoxManage snapshot ol7u4 edit ol7u4-snap-001 \
--description="Oracle Linux 7.4 with UEK4 kernel"
The following command shows VM settings for the ol7u1-snap-001 snapshot of the ol7u4 VM.

$ VBoxManage snapshot ol7u4 showvminfo ol7u4-snap-001
Name:            ol7u4
Groups:          /
Guest OS:        Oracle (64-bit)
UUID:            43349d78-2ab3-4cb8-978f-0e755cd98090
Config file:     C:\Users\<USER>\VirtualBox VMs\ol7u4\ol7u4.vbox
...
Snapshots:

   Name: ol7u4-snap-001 (UUID: 1cffc37d-5c37-4b86-b9c5-a0f157a55f43)
   Description: Oracle Linux 7.4 with UEK4 kernel
VBoxManage startvm
Start a virtual machine

Synopsis
VBoxManage startvm [‑‑putenv=name[=value]] [‑‑type=<gui|headless|sdl|separate>] [‑‑password=file] [‑‑password‑id=password‑identifier] <uuid | vmname...>
Description
The VBoxManage startvm command starts an Oracle VirtualBox virtual machine (VM) that is in the Powered Off or Saved state.

uuid | vmname
Specifies the name or Universally Unique Identifier (UUID) of the VM.

--putenv=name=value
Assigns a value to an environment variable as a name-value pair. For example, VBOX_DISABLE_HOST_DISK_CACHE=1.

The short form of this option is -E.

--type=gui | headless | sdl | separate
Specifies the frontend used to start the VM.

You can use the VBoxManage setproperty command to set a global default value for the frontend. Alternatively, you can use the VBoxManage modifyvm command to specify a default frontend value for a specific VM. If neither a global or per-VM default value is set and you do not specify the --type option, then the VM opens in a window on the host desktop.

The --type option accepts the following values:

gui
Starts a VM in a graphical user interface (GUI) window. This is the default.

headless
Starts a VM for remote display only.

sdl
Starts a VM using the VBoxSDL frontend.

separate
Starts a VM with a detachable user interface (UI), which means that the VM runs headless with the UI in a separate process.

--password
Use the --password to supply the encryption password. Either specify the absolute pathname of a password file on the host operating system, or - to prompt you for the password on the command line.

--password-id
Use the --password-id option to specify the id the password is supplied for.

Note:
If a VM fails to start with a particular frontend and the error information is inconclusive, consider starting the VM directly by running the frontend. This workaround might provide additional error information.

Examples
The following command starts the ol7u6 VM:

$ VBoxManage startvm ol7u6
The following command starts the ol7u6-mininstall VM in headless mode.

$ VBoxManage startvm ol7u6-mininstall --type headless
See Also
VBoxHeadless, the Remote Desktop Server, VBoxManage setproperty, VBoxManage modifyvm.

VBoxManage storageattach
Attach, remove, and modify storage media used by a virtual machine

Synopsis
VBoxManage storageattach <uuid | vmname> <‑‑storagectl=name> [‑‑bandwidthgroup=name | none] [‑‑comment=text] [‑‑device=number] [‑‑discard=on | off] [‑‑encodedlun=lun] [‑‑forceunmount] [‑‑hotpluggable=on | off] [‑‑initiator=initiator] [‑‑intnet] [‑‑lun=lun] [‑‑medium=none | emptydrive | additions | uuid | filename | host:drive | iscsi] [‑‑mtype=normal | writethrough | immutable | shareable | readonly | multiattach] [‑‑nonrotational=on | off] [‑‑passthrough=on | off] [‑‑passwordfile=file] [‑‑password=password] [‑‑port=number] [‑‑server=name | ip] [‑‑setparentuuid=uuid] [‑‑setuuid=uuid] [‑‑target=target] [‑‑tempeject=on | off] [‑‑tport=port] [‑‑type=dvddrive | fdd | hdd] [‑‑username=username]
Description
The VBoxManage storageattach command attaches, modifies, or removes a storage medium connected to a storage controller that was previously added with the VBoxManage storagectl command.

uuid | vmname
Specifies the Universally Unique Identifier (UUID) or the name of the virtual machine (VM).

--storagectl=name
Specifies the name of the storage controller. Use the VBoxManage showvminfo command to list the storage controllers that are attached to the VM.

--port=number
Specifies the port number of the storage controller to modify. You must specify this option unless the storage controller has only a single port.

--device=number
Specifies the port's device number to modify. You must specify this option unless the storage controller has only one device per port.

--type=dvddrive | fdd | hdd
Specifies the drive type to which the medium is associated. Only omit this option if the medium type can be determined by using the --medium option or by information provided by an earlier medium attachment command.

--medium=none | emptydrive | additions | uuid | filename | host:drive | iscsi
Specifies one of the following values:

none
Removes any existing device from the specified slot.

emptydrive
For a virtual DVD or floppy drive only.

Makes the device slot behave like a removable drive into which no media has been inserted.

additions
For a virtual DVD drive only.

Attaches the VirtualBox Guest Additions image to the specified device slot.

uuid
Specifies the UUID of a storage medium to attach to the specified device slot. The storage medium must already be known to Oracle VirtualBox, such as a storage medium that is attached to another VM. Use the VBoxManage list command to list media.

filename
Specifies the full path of an existing disk image to attach to the specified device slot. The disk image can be in ISO, RAW, VDI, VMDK, or other format.

host:drive
For a virtual DVD or floppy drive only.

Connects the specified device slot to the specified DVD or floppy drive on the host computer.

iscsi
For virtual hard disks only.

Specifies an iSCSI target for which you must specify additional information. See iSCSI Servers.

For removable media such as floppies and DVDs, you can make configuration changes while a VM is running. Changes to devices or hard disk device slots require that the VM be powered off.

--mtype=normal | writethrough | immutable | shareable | readonly | multiattach
Specifies how this medium behaves with respect to snapshots and write operations. See Special Image Write Modes.

--comment=text
Specifies an optional description to store with the medium.

--setuuid=uuid
Modifies the UUID of a medium before attaching it to a VM.

This is an expert option. Inappropriate values might make the medium unusable or lead to broken VM configurations if another VM already refers to the same medium.

Using the --setuuid="" option assigns a new random UUID to an image, which can resolve duplicate UUID errors if you used a file copy utility to duplicate an image.

--setparentuuid=uuid
Modifies the parent UUID of a medium before attaching it to a VM.

This is an expert option. Inappropriate values might make the medium unusable or lead to broken VM configurations if another VM already refers to the same medium.

--passthrough=on | off
For a virtual DVD drive only.

Enables writing to a DVD. This feature is experimental, see CD/DVD Support.

--tempeject=on | off
For a virtual DVD drive only.

Specifies whether to permit a temporary guest-triggered medium eject operation. When set to on, you can eject a medium. The ability for a guest-triggered eject operation does not persist if the VM is powered off and restarted. So, when you set this option to on and the VM is restarted, the originally configured medium is still in the drive.

--nonrotational=on | off
Enables you to specify that the virtual hard disk is non-rotational. Some guest OSes, such as Windows 7 or later, treat such disks as solid state drives (SSDs) and do not perform disk fragmentation on them.

--discard=on | off
Specifies whether to enable the auto-discard feature for a virtual hard disk. When set to on, a VDI image is shrunk in response to a trim command from the guest OS.

This feature is experimental and has known bugs which can cause hangs and other instabilities of the guest OS. Do not use if you care about the integrity of data on the virtual hard disk.

The virtual hard disk must meet the following requirements:

The disk format must be VDI.

The size of the cleared area of the disk must be at least 1 MB.

Ensure that the space being trimmed is at least a 1 MB contiguous block at a 1 MB boundary.

Consider running defragmentation commands as background cron jobs to save space. On Windows, run the defrag.exe /D command. On Linux, run the btrfs filesystem defrag command.

Note:
When you configure the guest OS to issue the trim command, the guest OS typically sees the disk as an SSD.

Ext4 supports the -o discard mount option. Mac OS X might require additional settings. Windows 7, 8, and 10 automatically detect and support SSDs. The Linux exFAT driver from Samsung supports the trim command.

The Microsoft implementation of exFAT might not support this feature.

You can use other methods to issue trim commands. The Linux fstrim command is part of the util-linux package. Earlier solutions required you to zero out unused areas by using the zerofree or a similar command, and then to compact the disk. You can only perform these steps when the VM is offline.

--bandwidthgroup=name
Specifies the bandwidth group to use for the device. See Limiting Bandwidth for Disk Images.

--forceunmount
For a virtual DVD or floppy drive only.

Forcibly unmounts the DVD, CD, or floppy or mounts a new DVD, CD, or floppy even if the previous removable storage is locked by the guest for reading. See CD/DVD Support.

The following options are applicable when you specify the --medium=iscsi option:

--server=hostname | IP-address
Specifies the host name or IP address of the iSCSI target.

--target=target
Specifies the target name string, which is determined by the iSCSI target and is used to identify the storage resource.

--tport=port
Specifies the TCP/IP port number of the iSCSI service on the target.

--lun=LUN
Specifies the logical unit number (LUN) of the target resource. For a single disk drive, the value is zero.

--encodedlun=LUN
Specifies the hexadecimal-encoded of the target resource. For a single disk drive, the value is zero.

--username=username
Specifies the user name to use for target authentication.

Note:
Unless you provide a settings password, the user name is stored as clear text in the XML machine configuration file.

--password=password
Specifies the password used for target authentication.

Note:
Unless you provide a settings password, this password is stored as clear text in the XML machine configuration file. When you specify a settings password for the first time, the target authentication password is stored in encrypted form.

--passwordfile=password-filename
Specifies a file that contains the target authentication password as clear text.

Note:
Use permission and ownership settings to ensure that the contents of this file cannot be read by unauthorized users.

--initiator=initiator
Specifies the iSCSI initiator.

The Microsoft iSCSI Initiator is a system, such as a server, that attaches to an IP network and initiates requests and receives responses from an iSCSI target. The SAN components in the iSCSI initiator are largely analogous to Fibre Channel SAN components, and they include the following:

iSCSI driver. Transports blocks of iSCSI commands over the IP network. This iSCSI driver is installed on the iSCSI host and is included with the Microsoft iSCSI Initiator.

Gigabit Ethernet adapter. Connects to an iSCSI target. Use an Ethernet adapter that can transmit 1000 megabits per second (Mbps). Like standard 10/100 adapters, most gigabit adapters use a preexisting Category 5 or Category 6E cable. Each port on the adapter is identified by a unique IP address.

iSCSI target. Is any device that receives iSCSI commands. The device can be an end node such as a storage device, or it can be an intermediate device such as a network bridge between IP and Fibre Channel devices. Each port on the storage array controller or network bridge is identified by one or more IP addresses.

--intnet
Specifies whether to connect to the iSCSI target that uses internal networking. This configuration requires further configuration. See Access iSCSI Targets Using Internal Networking.

Examples
The following command attaches the o7.vdi disk image to the specified SATA storage controller on the ol7 VM.

$ storageattach ol7 --storagectl "SATA Controller" --port 0 --device 0 \
--type hdd --medium /VirtualBox/ol7/ol7.vdi
The following command attaches the o7-r6-dvd.iso DVD image to the specified IDE storage controller on the ol7 VM.

$ VBoxManage storageattach ol7 --storagectl "IDE Controller" --port 0 --device 0 \
--type dvddrive --medium ol7-r6-dvd.iso
See Also
VBoxManage list, VBoxManage showvminfo, VBoxManage storagectl

VBoxManage storagectl
Manage a storage controller

Synopsis
VBoxManage storagectl <uuid | vmname> <‑‑name=controller‑name> [‑‑add=floppy | ide | pcie | sas | sata | scsi | usb] [‑‑controller=BusLogic | I82078 | ICH6 | IntelAhci | LSILogic | LSILogicSAS | NVMe | PIIX3 | PIIX4 | USB | VirtIO] [‑‑bootable=on | off] [‑‑hostiocache=on | off] [‑‑portcount=count] [‑‑remove] [‑‑rename=new‑controller‑name]
Description
The VBoxManage storagectl command enables you to attach, modify, and remove a storage controller. After you configure the storage controller, you can use the VBoxManage storageattach command to attach virtual media to the controller.

uuid | vmname
Specifies the Universally Unique Identifier (UUID) or name of the virtual machine (VM).

--name=controller-name
Specifies the name of the storage controller.

--add=system-bus-type
Specifies the type of the system bus to which to connect the storage controller. Valid values are floppy, ide, pcie, sas, sata, scsi, and usb.

--controller=chipset-type
Specifies the chipset type to emulate for the specified storage controller. Valid values are BusLogic, I82078, ICH6, IntelAHCI, LSILogic, LSILogicSAS, NVMe, PIIX3, PIIX4, and USB.

The default value varies, according to the type of storage controller.

--portcount=count
Specifies the number of ports that the storage controller supports. Valid values depend on the type of storage controller.

--hostiocache=on|off
Specifies whether to use the host I/O cache for all disk images attached to this storage controller. Valid values are on and off. See Host Input/Output Caching.

--bootable=on|off
Specifies whether this controller is bootable. Valid values are on and off.

--rename=new-controller-name
Specifies a new name for the storage controller.

--remove
Removes a storage controller from the VM configuration.

Examples
The following command creates a SATA storage controller called sata01 and adds it to the ol7 VM. The storage controller emulates the IntelAHCI chipset.

$ VBoxManage storagectl ol7 --name "sata01" --add sata --controller IntelAHCI
The following command creates an IDE storage controller called ide01 and adds it to the ol7 VM.

$ VBoxManage storagectl ol7 --name "ide01" --add ide
See Also
VBoxManage storageattach

VBoxManage unattended
Unattended guest OS installation

Synopsis
VBoxManage unattended detect <‑‑iso=install‑iso> [‑‑machine‑readable]
VBoxManage unattended install <uuid | vmname> <‑‑iso=install‑iso> [‑‑user=login] [‑‑user‑password=password] [‑‑user‑password‑file=file] [‑‑admin‑password=password] [‑‑admin‑password‑file=file] [‑‑full‑user‑name=name] [‑‑key=product‑key] [‑‑install‑additions] [‑‑no‑install‑additions] [‑‑additions‑iso=add‑iso] [‑‑install‑txs] [‑‑no‑install‑txs] [‑‑validation‑kit‑iso=testing‑iso] [‑‑locale=ll_CC] [‑‑country=CC] [‑‑time‑zone=tz] [‑‑proxy=url] [‑‑hostname=fqdn] [‑‑package‑selection‑adjustment=keyword] [‑‑dry‑run] [‑‑auxiliary‑base‑path=path] [‑‑image‑index=number] [‑‑script‑template=file] [‑‑post‑install‑template=file] [‑‑post‑install‑command=command] [‑‑extra‑install‑kernel‑parameters=params] [‑‑language=lang] [‑‑start‑vm=session‑type]
Description
unattended detect
VBoxManage unattended detect <‑‑iso=install‑iso> [‑‑machine‑readable]

Detects the guest operating system (OS) on the specified installation ISO and displays the result. This can be used as input when creating a VM for the ISO to be installed in.

--iso=install-iso
The installation ISO to run the detection on.

--machine-readable
Produce output that is simpler to parse from a script.

unattended install
VBoxManage unattended install <uuid | vmname> <‑‑iso=install‑iso> [‑‑user=login] [‑‑user‑password=password] [‑‑user‑password‑file=file] [‑‑admin‑password=password] [‑‑admin‑password‑file=file] [‑‑full‑user‑name=name] [‑‑key=product‑key] [‑‑install‑additions] [‑‑no‑install‑additions] [‑‑additions‑iso=add‑iso] [‑‑install‑txs] [‑‑no‑install‑txs] [‑‑validation‑kit‑iso=testing‑iso] [‑‑locale=ll_CC] [‑‑country=CC] [‑‑time‑zone=tz] [‑‑proxy=url] [‑‑hostname=fqdn] [‑‑package‑selection‑adjustment=keyword] [‑‑dry‑run] [‑‑auxiliary‑base‑path=path] [‑‑image‑index=number] [‑‑script‑template=file] [‑‑post‑install‑template=file] [‑‑post‑install‑command=command] [‑‑extra‑install‑kernel‑parameters=params] [‑‑language=lang] [‑‑start‑vm=session‑type]

Reconfigures the specified VM for installation and optionally starts it up.

uuid | vmname
Either the UUID or the name (case sensitive) of a VM.

--iso=install-iso
The installation ISO to run the detection on.

--user=login
The login name. (default: vboxuser)

--user-password=password
The user login password. This is used for the user given by --user (default: changeme)

--user-password-file=file
Alternative to --user-password for providing the user password. Special filename stdin can be used to read the password from standard input.

--admin-password=password
The admin / root login password. If not specified, the password from --user-password will be used.

--admin-password-file=file
Alternative to --admin-password for providing the admin / root password. Special filename stdin can be used to read the password from standard input.

--full-user-name=name
The full user name. (default: --user)

--key=product-key
The guest OS product key. Not all guest OSes requires this.

--install-additions, --no-install-additions
Whether to install the VirtualBox guest additions. (default: --no-install-additions)

--additions-iso=add-iso
Path to the VirtualBox guest additions ISO. (default: installed/downloaded GAs)

--install-txs, --no-install-txs
Whether to install the Test eXecution Service (TXS) from the VirtualBox ValidationKit. This is useful when preparing VMs for testing or similar. (default: --no-install-txs)

--validation-kit-iso=testing-iso
Path to the VirtualBox ValidationKit ISO. This is required if --install-txs is specified.

--locale=ll_CC
The base locale specification for the guest, like en_US, de_CH, or nn_NO. (default: host or en_US)

--country=CC
The two letter country code if it differs from the specified by --location.

--time-zone=tz
The time zone to set up the guest OS with. (default: host time zone or UTC)

--proxy=url
Proxy URL to use.

--hostname=fqdn
The fully qualified domain name of the guest machine. (default: vmname.myguest.virtualbox.org)

--package-selection-adjustment=keyword
Adjustments to the guest OS packages/components selection. This can be specified more than once. Currently the only recognized keyword is minimal which triggers a minimal installation for some of the guest OSes.

--dry-run
Do not create any files or make any changes to the VM configuration.

--start-vm=session-type
Start the VM using the front end given by session-type. This is the same as the --type option for the startvm command, but we have add none for indicating that the VM should not be started. (default: none)

Advanced options:

--auxiliary-base-path=path
The path prefix to the media related files generated for the installation. (default: vm-config-dir/Unattended-vm-uuid-)

--image-index=number
Windows installation image index. (default: 1)

--script-template=file
The unattended installation script template. (default: IMachine::OSTypeId dependent)

--post-install-template=file
The post installation script template. (default: IMachine::OSTypeId dependent)

--post-install-command=command
A single command to run after the installation is completed. The exact format and exactly when this is run is guest OS installer dependent.

--extra-install-kernel-parameters=params
List of extra Linux kernel parameters to use during the installation. (default: IMachine::OSTypeId dependent)

--language=lang
Specifies the UI language for a Windows installation. The lang is generally of the form {ll}-{CC}. See the detectedOSLanguages results from VBoxManage unattended detect. (default: detectedOSLanguages[0])

VBoxManage unregistervm
Unregister a virtual machine

Synopsis
VBoxManage unregistervm <uuid | vmname> [‑‑delete] [‑‑delete‑all]
Description
The VBoxManage unregistervm command unregisters a virtual machine (VM).

uuid | vmname
Specifies the name or Universally Unique Identifier (UUID) of the VM.

--delete
Deletes the following files related to the VM automatically:

All hard disk image files, including differencing files.

All saved state files that the machine created, including one for each snapshot.

XML VM machine definition file and its backups.

VM log files.

The empty directory associated with the unregistered VM.

--delete-all
Deletes the files described in the --delete option, as well as all DVDs and Floppy disks located in the VM folder and attached only to this VM.

Examples
The following command unregisters a VM called vm2.

$ VBoxManage unregistervm vm2
The following command unregisters a VM called vm3. All files associated with the VM are deleted.

$ VBoxManage unregistervm vm3 --delete
%...10%...20%...30%...40%...50%...60%...70%...80%...90%...100%
See Also
VBoxManage registervm

VBoxManage updatecheck
Checks for a newer version of Oracle VirtualBox

Synopsis
VBoxManage updatecheck perform [‑‑machine‑readable]
VBoxManage updatecheck list [‑‑machine‑readable]
VBoxManage updatecheck modify [‑‑disable | ‑‑enable] [‑‑channel=stable | withbetas | all] [‑‑frequency=days]
Description
The updatecheck subcommand is used to check if a newer version of Oracle VirtualBox is available. The two subcommand options of updatecheck are used for modifying or viewing the settings associated with checking for a newer version of Oracle VirtualBox.

updatecheck perform
VBoxManage updatecheck perform [‑‑machine‑readable]

Checks if a newer version of Oracle VirtualBox is available.

--machine-readable
Machine readable output.

updatecheck list
VBoxManage updatecheck list [‑‑machine‑readable]

Displays the current settings used for specifying when to check for a newer version of Oracle VirtualBox.

--machine-readable
Machine readable output.

updatecheck modify
VBoxManage updatecheck modify [‑‑disable | ‑‑enable] [‑‑channel=stable | withbetas | all] [‑‑frequency=days]

Modifies the settings used for specifying when to check for a newer version of Oracle VirtualBox.

--enable
Enable the update check service.

--disable
Disable the update check service.

--channel=stable | withbetas | all
The preferred release type used for determining whether a newer version of Oracle VirtualBox is available. The default is 'stable'.

stable
Checks for newer stable releases (maintenance and minor releases within the same major release) of Oracle VirtualBox.

all
Checks for newer stable releases (maintenance and minor releases within the same major release) and major releases of Oracle VirtualBox.

withbetas
Checks for newer stable releases (maintenance and minor releases within the same major release), major releases, and beta releases of Oracle VirtualBox.

--frequency=days
Specifies how often in days to check for a newer version of Oracle VirtualBox.

--proxy-mode=system | manual | none
Specifies the proxy mode to use.

--proxy-url=<address>
Specifies the proxy address to use. Set to empty string to clear proxy address.

VBoxManage usbdevsource
Add and remove USB device sources

Synopsis
VBoxManage usbdevsource add <source‑name> <‑‑backend=backend> <‑‑address=address>
VBoxManage usbdevsource remove <source‑name>
Description
The VBoxManage usbdevsource command adds a USB device source and makes it available to VM guests on the host system. You can also use this command to remove a USB device source.

Add a USB Device Source
VBoxManage usbdevsource add <source‑name> <‑‑backend=backend> <‑‑address=address>

The VBoxManage usbdevsource add command adds a USB device source, which is then available to all guest VMs on the host system.

source-name
Specifies a unique name for the USB device source.

--address=address
Specifies the address of the USB backend.

--backend=backend
Specifies the USB proxy service backend to use.

If specifying a remote server over the USB/IP protocol the only currently supported backend value is USBIP.

Remove a USB Device
VBoxManage usbdevsource remove <source‑name>

The VBoxManage usbdevsource remove command removes a USB device.

source-name
Specifies the name of the USB device source to remove.

Examples
The following command adds a USB device server called hostusb01.

$ VBoxManage usbdevsource add hostusb01 --backend USBIP --address *********
VBoxManage usbfilter
Manage USB filters

Synopsis
VBoxManage usbfilter add <index,0‑N> <‑‑target=<uuid | vmname | global>> <‑‑name=string> <‑‑action=ignore | hold> [‑‑active=yes | no] [‑‑vendorid=XXXX] [‑‑productid=XXXX] [‑‑revision=IIFF] [‑‑manufacturer=string] [‑‑product=string] [‑‑port=hex] [‑‑remote=yes | no] [‑‑serialnumber=string] [‑‑maskedinterfaces=XXXXXXXX]
VBoxManage usbfilter modify <index,0‑N> <‑‑target=<uuid | vmname | global>> [‑‑name=string] [‑‑action=ignore | hold] [‑‑active=yes | no] [‑‑vendorid=XXXX| ""] [‑‑productid=XXXX| ""] [‑‑revision=IIFF| ""] [‑‑manufacturer=string| ""] [‑‑product=string| ""] [‑‑port=hex] [‑‑remote=yes | no] [‑‑serialnumber=string| ""] [‑‑maskedinterfaces=XXXXXXXX]
VBoxManage usbfilter remove <index,0‑N> <‑‑target=<uuid | vmname | global>>
Description
The VBoxManage usbfilter command enables you to manage USB filters for a specific virtual machine (VM), or global USB filters that affect the entire Oracle VirtualBox configuration.

Global filters are applied before VM-specific filters. This means that you can use a global filter to prevent devices from being captured by any VM.

Global filters are applied in sequence based on where they were located in the list (see the --index option below for the list ordering details). Only the first filter that matches a device is applied. For example, if two global filters were created and the first filter made a specific Kingston memory stick device available while the second filter ignored all Kingston devices the result of applying these two filters would be that this specific Kingston memory stick would made available to any VM but no other Kingston USB devices would be made available.

Common Operand and Options
index,0-N
Specifies a single integer that indicates the position of the filter in the list. Zero (0) represents the first position in the list. If a filter already exists at the specified position, the existing filter and any existing filters that follow are moved down the list. Otherwise, the new filter is appended to the list.

--action=ignore | hold
Specifies whether to allow VMs access to devices that match a USB filter (hold) or to deny them access (ignore). This option applies only to global filters.

--active=yes | no
Specifies whether a USB filter is active or temporarily disabled. Valid values are yes, which activates the filter, and no, which disables the filter. The default value is yes.

--manufacturer=string
Specifies a manufacturer ID filter as a string. The default value is an empty string ("").

--maskedinterfaces=XXXXXXXX
Specifies a masked interface filter that is used to hide one or more USB interfaces from the guest. The value is a bit mask where the set bits correspond to the USB interfaces to hide, or mask off. This feature is supported on Linux host systems only.

--name=filter-name
Specifies the name of the filter.

--port=hex
Specifies a hub port number filter as a string. The default value is an empty string ("").

--product=string
Specifies a product ID filter as a string. The default value is an empty string ("").

--productid=XXXX
Specifies a product ID filter. The string representation for an exact match has the form XXXX, where X is a hexadecimal digit including leading zeroes. The default value is an empty string ("").

--remote=yes | no
Specifies a remote filter that indicates whether the device is physically connected to a remote VRDE client or to a local host system. This option applies to VM filters only. The default value is no.

--revision=IIFF
Specifies a revision ID filter. The string representation for an exact match has the form IIFF. I is a decimal digit of the integer part of the revision. F is a decimal digit of its fractional part that includes leading and trailing zeros. The default value is an empty string ("").

To specify a range of revision IDs, ensure that you use the hexadecimal form so that the revision is stored as a 16-bit packed BCD value. For example, the int:0x0100-0x0199 expression matches any revision from 1.0 to 1.99, inclusive.

--serialnumber=string
Specifies a serial number filter as a string. The default value is an empty string ("").

--target=uuid | vmname | global
Specifies the VM that the filter is attached to. You can specify the Universally Unique Identifier (UUID) or the name of the VM. To apply the filter description to all VMs, specify global.

--vendorid=XXXX
Specifies a vendor ID filter, which is a string representation of a four-digit hexadecimal number. X is the hexadecimal digit including leading zeroes. The default value is an empty string ("").

Add a USB Filter or a Global Filter
VBoxManage usbfilter add <index,0‑N> <‑‑target=<uuid | vmname | global>> <‑‑name=string> <‑‑action=ignore | hold> [‑‑active=yes | no] [‑‑vendorid=XXXX] [‑‑productid=XXXX] [‑‑revision=IIFF] [‑‑manufacturer=string] [‑‑product=string] [‑‑port=hex] [‑‑remote=yes | no] [‑‑serialnumber=string] [‑‑maskedinterfaces=XXXXXXXX]

Use the VBoxManage usbfilter add command to create a new USB filter.

In addition, specify parameters by which to filter. You can use the VBoxManage list usbhost command to view the parameters of the USB devices that are attached to your system.

Modify a USB Filter or a Global Filter
VBoxManage usbfilter modify <index,0‑N> <‑‑target=<uuid | vmname | global>> [‑‑name=string] [‑‑action=ignore | hold] [‑‑active=yes | no] [‑‑vendorid=XXXX| ""] [‑‑productid=XXXX| ""] [‑‑revision=IIFF| ""] [‑‑manufacturer=string| ""] [‑‑product=string| ""] [‑‑port=hex] [‑‑remote=yes | no] [‑‑serialnumber=string| ""] [‑‑maskedinterfaces=XXXXXXXX]

Use the VBoxManage usbfilter modify command to modify a USB filter. You can use the VBoxManage list usbfilters command to list global filter indexes and the VBoxManage showvminfo command to list indexes for a specific virtual machine.

Remove a USB Filter or a Global Filter
VBoxManage usbfilter remove <index,0‑N> <‑‑target=<uuid | vmname | global>>

Use the VBoxManage usbfilter remove command to remove a USB filter entry.

Examples
The following command lists the available USB devices on the host system.

$ VBoxManage list usbhost
The following command adds a USB filter called filter01 to the ol7 VM. The filter specifies a Kingston DataTraveler memory stick and is placed first in the list of USB filters for the VM.

$ VBoxManage usbfilter add 0 --target ol7 --name filter01 --vendorid 0x0930 --productid 0x6545
The following command removes the USB filter that is second in the list for the ol7 VM.

$ VBoxManage usbfilter remove 1 --target ol7
vboximg-mount
FUSE mount a virtual disk image for Mac OS and Linux hosts

Synopsis
vboximg‑mount <‑? | ‑h | ‑‑help>
vboximg‑mount <‑‑image=image‑UUID> [‑‑guest‑filesystem] [‑o=FUSE‑option [,FUSE‑option...]] [‑‑root] [‑‑rw] <mountpoint>
vboximg‑mount <‑‑list> [‑‑image=image‑UUID] [‑‑verbose] [‑‑vm=vm‑UUID] [‑‑wide]
Description
The vboximg-mount command enables you to make Oracle VirtualBox disk images available to a Mac OS or Linux host operating system (OS) for privileged or non-priviliged access. You can mount any version of the disk from its available history of snapshots. Use this command to mount, view, and optionally modify the contents of an Oracle VirtualBox virtual disk image, and you can also use this command to view information about registered virtual machines (VMs).

This command uses the Filesystem in Userspace (FUSE) technology to provide raw access to an Oracle VirtualBox virtual disk image.

When you use the --image option to specify a base image identifier, only the base image is mounted. Any related snapshots are disregarded. Alternatively, if you use the --image option to specify a snapshot, the state of the FUSE-mounted virtual disk is synthesized from the implied chain of snapshots, including the base image.

The vboximg-mount command features read-only access to file systems inside a VM disk image. This feature enables you to extract some files from the VM disk image without starting the VM and without requiring third-party file system drivers on the host system. Oracle VirtualBox supports the FAT, NTFS, ext2, ext3, and ext4 file systems.

The virtual disk is exposed as a device node within a FUSE-based file system that overlays the specified mount point.

The FUSE file system includes a directory that contains a number of files. The file system can also contain a directory that includes a symbolic link that has the same base name (see the basename(1) man page) as the virtual disk base image and points to the location of the virtual disk base image. The directory can be of the following types:

vhdd provides access to the raw disk image data as a flat image

volID provides access to an individual volume on the specified disk image

fsID provides access to a supported file system without requiring a host file system driver

General Command Options
vboximg‑mount <‑? | ‑h | ‑‑help>

Use the following options to obtain information about the vboximg-mount command and its options.

--help, --h, or--?
Shows usage information.

Mounting an Oracle VirtualBox Disk Image
vboximg‑mount <‑‑image=image‑UUID> [‑‑guest‑filesystem] [‑o=FUSE‑option [,FUSE‑option...]] [‑‑root] [‑‑rw] <mountpoint>

Use the vboximg-mount command to mount an Oracle VirtualBox virtual disk image on a Mac OS or Linux host system. When mounted, you can view the contents of the disk image or modify the contents of the disk image.

You can use the vboximg-mount command to restrict FUSE-based access to a subsection of the virtual disk.

--image=disk-image
Specifies the Universally Unique Identifier (UUID), name, or path of the Oracle VirtualBox disk image.

The short form of the --image option is -i.

--guest-filesystem
Enables read-only support for guest file systems. When you specify this option, all known file systems are made available to access.

The short form of the --guest-filesystem option is -g.

-o=FUSE-option[,FUSE-option...]
Specifies FUSE mount options.

The vboximg-mount command enables you to use the FUSE mount options that are described in the mount.fuse(8) man page.

--root
Overrides the security measure that restricts file access to the file system owner by also granting file access to the root user.

Same as the -o allow_root option. See the -o option description.

This option is incompatible with the -o allow_other option.

--rw
Mounts the specified image as read-write, which is required if you want to modify its contents. By default, images are mounted as read-only.

mount-point
Specifies the path name of a directory on which to mount the Oracle VirtualBox disk image.

Viewing Oracle VirtualBox Disk Image Information
vboximg‑mount <‑‑list> [‑‑image=image‑UUID] [‑‑verbose] [‑‑vm=vm‑UUID] [‑‑wide]

Use the vboximg-mount command to view information about registered VMs or an Oracle VirtualBox virtual disk image.

--list
Shows information about the disks that are associated with the registered VMs. If you specify a disk image, this option shows information about the partitions of the specified image.

When you specify the --verbose option, the output includes detailed information about the VMs and media, including snapshot images and file paths.

The short form of the --list option is -l.

--image=disk-image
Specifies the UUID, name, or path of the Oracle VirtualBox disk image.

The short form of the --image option is -i.

--verbose
Shows or logs detailed information.

The short form of the --verbose option is -v.

--vm=vm-UUID
Outputs information about the VM that is associated with the specified UUID.

--wide
Outputs information in a wide format. This output includes the lock state information of running VMs. For VMs that are not running, the state is created.

The wide output uses a tree-like structure in the VM column to show the relationship between a VM base image and its snapshots.

Examples
The following example shows how to mount a virtual disk image on the host operating system (OS).

$ mkdir fuse_mount_point
$ vboximg-mount --image=b490e578-08be-4f7d-98e9-4c0ef0952377 fuse_mount_point
$ ls fuse_mount_point
ubu.vdi[32256:2053029880]   vhdd
$ sudo mount fuse_mount_point/vhdd /mnt
The mkdir command creates a mount point called fuse_mount_point on the host OS. The vboximg-mount command is then used to mount the specified disk image on the fuse_mount_point mount point. The mount includes all snapshots for the disk image.

The ls command shows the contents of fuse_mount_point. The mount command is then used to mount the FUSE-mounted device node, vhdd, on the /mnt mount point. The vhdd device node represents the virtual disk image.

The following example shows how to make the known file systems of the b490e578-08be-4f7d-98e9-4c0ef0952377 disk image accessible when the image is mounted on the fuse_mount_point mount point:

$ vboximg-mount --image=b490e578-08be-4f7d-98e9-4c0ef0952377 \
--guest-filesystem fuse_mount_point
The following command outputs detailed information about all registered VMs and their snapshots:

$ vboximg-mount --list --verbose
The following command shows an excerpt of the list output in wide format.

$ vboximg-mount --list --wide

VM  Image                 Size Type State   UUID (hierarchy)
------------------------------------------  ------------------------------------
Proxy                                       0833f5bc-6304-42e1-b799-cdc81c576c60
 |
 +- Proxy.vdi             4.8G VDI  rlock   d5f84afb-0794-4952-ab71-6bbcbee07737
 |  +- <snapshot>        12.3G VDI  rlock     dffc67aa-3023-477f-8033-b27e3daf4f54
 |  +- <snapshot>         8.8G VDI  rlock       3b2755bd-5f2a-4171-98fe-647d510b6274
 |  +- <snapshot>        14.6G VDI  rlock         e2ccdb5f-49e8-4123-8623-c61f363cc5cf
 |  +- <snapshot>         7.4G VDI  wlock           3c1e6794-9091-4be3-9e80-11aba40c2649

------------------------------------------  ------------------------------------
Oracle Linux 7                              5365ab5f-470d-44c0-9863-dad532ee5905
 |
 +- Oracle Linux 7.vdi     7.0G VDI created 96d2e92e-0d4e-46ab-a0f1-008fdbf997e7
 | +- <snapshot>          15.9G VDI created   f9cc866a-9166-42e9-a503-bbfe9b7312e8
 |
 +- kernel.vdi            11.1G VDI created 79a370bd-0c4f-480a-30bb-10cdea68423f
The output shows that the Proxy VM is running the fourth snapshot of the Proxy.vdi virtual disk image. The running state is indicated by the wlock value in the State column.

The Oracle Linux 7 VM is not running. It has two images: Oracle Linux 7.vdi and kernel.vdi. The Oracle Linux 7.vdi image has a snapshot.

The following command shows information about the VM with the specified UUID:

$ vboximg-mount --list --vm=b1d5563b-2a5b-4013-89f1-26c81d6bbfa0
-----------------------------------------------------------------
VM:   ubu
UUID: b1d5563b-2a5b-4013-89f1-26c81d6bbfa0

  Image:   ubu.vdi
  UUID:    b490e578-08be-4f7d-98e9-4c0ef0952377

       Snapshot: 35afe1e0-0a51-44f3-a228-caf172f3306f
       Size:     12.1G

       Snapshot: 874279c1-4425-4282-ada8-a9c07c00bbf9
       Size:     13.6G

  Image:   kernel.vdi
  UUID:    79a370bd-6eb7-4dbf-8bc6-d29118f127e0