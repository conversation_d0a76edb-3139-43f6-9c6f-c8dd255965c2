#!/bin/bash

# VirtualBox VM Configuration Script for Linux
# Converted from AutoHotkey script with dynamic host detection

# =============================================================================
# CONFIGURATION SECTION - Edit these values as needed
# =============================================================================

# VM Basic Settings
VM="vm3"                            # VM name
vmscfgdir="$HOME/vm-config"         # Custom BIOS/ACPI files directory (optional)

# VM Hardware Settings
OS_TYPE="Linux_64"                  # OS type for new VM
CPU_COUNT="2"                       # Number of CPU cores
vm_memory="4096"                    # Memory in MB
vm_mouse="usbtablet"                # Mouse type: ps2, usb, usbtablet
vm_vga="vboxvga"                     # Graphics controller: vboxvga, vmsvga, vboxsvga
vga_vram="256"                      # Video memory in MB
enable_chipset_ich9="true"         # Set to "true" to enable ICH9 chipset

# Remote Access Settings (choose one)
enable_vnc="true"                   # Enable VNC (VRDE) for remote access
vnc_port="5900"                     # VNC port number
enable_rdp="false"                  # Enable RDP for remote access
rdp_port="3389"                     # RDP port number

# =============================================================================
# NETWORK AND MAC CONFIGURATION
# =============================================================================

echo "Configuring VM: $VM"
echo "=================="

# Fixed network adapter (no detection)
bridge_adapter="eth0"

# Generate random MAC address with Intel prefix (00:1B:21 is Intel)
MAC="001B21$(openssl rand -hex 3 2>/dev/null | tr '[:lower:]' '[:upper:]' || printf "%06X" $((RANDOM * RANDOM % 16777216)))"

echo "Network adapter: $bridge_adapter"
echo "Generated Intel MAC: $MAC"
echo "VM configuration: $VM (${vm_memory}MB RAM, ${vga_vram}MB VRAM, ${CPU_COUNT} CPUs)"
echo

# Function to run VBoxManage commands with error checking
run_vboxmanage() {
    echo "Running: VBoxManage $*"
    vboxManage "$@" 2>/dev/null
    if [ $? -ne 0 ]; then
        echo "Warning: Command failed: VBoxManage $*"
    fi
}

# =============================================================================
# AUTOMATIC VM CREATION/DETECTION
# =============================================================================

# Check if VM exists and create if needed (using VirtualBox default folder)
if VBoxManage list vms | grep -q "\"$VM\""; then
    echo "VM '$VM' exists - applying configuration"
else
    echo "VM '$VM' not found - creating new VM"

    # Create the VM
    run_vboxmanage createvm --name "$VM" --ostype "$OS_TYPE" --register

    # Basic VM settings
    run_vboxmanage modifyvm "$VM" --memory "$vm_memory"
    run_vboxmanage modifyvm "$VM" --cpus "$CPU_COUNT"
    run_vboxmanage modifyvm "$VM" --vram "$vga_vram"

    # CPU settings
    run_vboxmanage modifyvm "$VM" --pae on
    run_vboxmanage modifyvm "$VM" --longmode on
    run_vboxmanage modifyvm "$VM" --x2apic on
    run_vboxmanage modifyvm "$VM" --largepages on

    # Hardware virtualization
    run_vboxmanage modifyvm "$VM" --hwvirtex on
    run_vboxmanage modifyvm "$VM" --nestedpaging on
    run_vboxmanage modifyvm "$VM" --vtxvpid on
    run_vboxmanage modifyvm "$VM" --vtxux on

    # Other hardware settings
    run_vboxmanage modifyvm "$VM" --hpet on
    run_vboxmanage modifyvm "$VM" --paravirtprovider legacy
    run_vboxmanage modifyvm "$VM" --apic on

    # Graphics and peripherals
    run_vboxmanage modifyvm "$VM" --graphicscontroller "$vm_vga"
    run_vboxmanage modifyvm "$VM" --mouse "$vm_mouse"

    # BIOS settings
    run_vboxmanage modifyvm "$VM" --ioapic on
    run_vboxmanage modifyvm "$VM" --biosbootmenu menuonly

    # Optional ICH9 chipset
    if [ "$enable_chipset_ich9" = "true" ]; then
        run_vboxmanage modifyvm "$VM" --chipset ich9
    fi

    echo "VM creation completed"
fi

echo

# =============================================================================
# VM CONFIGURATION (applies to both new and existing VMs)
# =============================================================================

# Network configuration
run_vboxmanage modifyvm "$VM" --nic1 bridged
run_vboxmanage modifyvm "$VM" --bridgeadapter1 "$bridge_adapter"
run_vboxmanage modifyvm "$VM" --macaddress1 "$MAC"

# Basic VM settings
run_vboxmanage modifyvm "$VM" --memory "$vm_memory"
run_vboxmanage modifyvm "$VM" --mouse "$vm_mouse"
run_vboxmanage modifyvm "$VM" --graphicscontroller "$vm_vga"
run_vboxmanage modifyvm "$VM" --vram "$vga_vram"

# Boot configuration - PXE/Network boot only
run_vboxmanage modifyvm "$VM" --boot1 net
run_vboxmanage modifyvm "$VM" --boot2 none
run_vboxmanage modifyvm "$VM" --boot3 none
run_vboxmanage modifyvm "$VM" --boot4 none

# Remote access configuration
if [ "$enable_vnc" = "true" ]; then
    echo "Enabling VNC (VRDE) on port $vnc_port"
    run_vboxmanage modifyvm "$VM" --vrde on
    run_vboxmanage modifyvm "$VM" --vrdeport "$vnc_port"
    run_vboxmanage modifyvm "$VM" --vrdeproperty "TCP/Ports=$vnc_port"
elif [ "$enable_rdp" = "true" ]; then
    echo "Enabling RDP on port $rdp_port"
    run_vboxmanage modifyvm "$VM" --vrde on
    run_vboxmanage modifyvm "$VM" --vrdeport "$rdp_port"
else
    echo "Remote access disabled"
    run_vboxmanage modifyvm "$VM" --vrde off
fi

# CPU and virtualization settings
run_vboxmanage setextradata "$VM" "VBoxInternal/CPUM/EnableHVP" 0
run_vboxmanage setextradata "$VM" "VBoxInternal/TM/TSCMode" RealTSCOffset

# DMI BIOS settings
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVendor" "Asus"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVersion" "MB52.88Z.0088.B05.0904162222"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseDate" "08/10/13"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMajor" "5"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMinor" "9"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMajor" "1"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMinor" "0"

# DMI System settings
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemVendor" "Asus"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemProduct" "Asus5"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemVersion" "1.0"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemSerial" "CSN12345678901234567"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemSKU" "FM550EA#ACB"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemFamily" "Ultrabook"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemUuid" "B5FA3000-9403-81E0-3ADA-F46D045CB676"

# DMI Board settings
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardVendor" "Asus"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardProduct" "Aus-F22788AA"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardVersion" "3.0"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardSerial" "BSN12345678901234567"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardAssetTag" "Base Board Asset Tag#"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardLocInChass" "Board Loc In"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardBoardType" 10

# DMI Chassis settings
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisVendor" "Asus Inc."
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisType" 10
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisVersion" "Asus-F22788AA"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisSerial" "CSN12345678901234567"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisAssetTag" "WhiteHouse"

# DMI OEM settings
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxVer" "Extended version info: 1.00.00"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxRev" "Extended revision info: 1A"

# AHCI settings
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/ahci/0/Config/Port0/ModelNumber" "Hitachi HTS543230AAA384"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/ahci/0/Config/Port0/FirmwareRevision" "ES2OA60W"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/ahci/0/Config/Port0/SerialNumber" "2E3024L1T2V9KA"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/ahci/0/Config/Port1/ModelNumber" "Slimtype DVD A  DS8A8SH"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/ahci/0/Config/Port1/FirmwareRevision" "KAA2"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/ahci/0/Config/Port1/SerialNumber" "ABCDEF0123456789"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIVendorId" "Slimtype"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIProductId" "DVD A  DS8A8SH"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIRevision" "KAA2"

# ACPI settings
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/acpi/0/Config/AcpiOemId" "ASUS"

# Virtualization and hardware settings
run_vboxmanage modifyvm "$VM" --paravirtprovider legacy

# Custom BIOS logo (only if file exists)
if [ -f "$vmscfgdir/vm_splash.bmp" ]; then
    echo "Applying custom BIOS logo: $vmscfgdir/vm_splash.bmp"
    run_vboxmanage modifyvm "$VM" --bioslogoimagepath "$vmscfgdir/vm_splash.bmp"
fi

run_vboxmanage modifyvm "$VM" --hwvirtex on
run_vboxmanage modifyvm "$VM" --vtxvpid on
run_vboxmanage modifyvm "$VM" --vtxux on
run_vboxmanage modifyvm "$VM" --apic on
run_vboxmanage modifyvm "$VM" --pae on
run_vboxmanage modifyvm "$VM" --longmode on
run_vboxmanage modifyvm "$VM" --hpet on
run_vboxmanage modifyvm "$VM" --nestedpaging on
run_vboxmanage modifyvm "$VM" --largepages on

# Optional ICH9 chipset
if [ "$enable_chipset_ich9" = "true" ]; then
    run_vboxmanage modifyvm "$VM" --chipset ich9
fi


# Custom BIOS and ACPI files (only if they exist)
if [ -d "$vmscfgdir" ]; then
    echo "Checking for custom BIOS/ACPI files in: $vmscfgdir"

    if [ -f "$vmscfgdir/ACPI-DSDT.bin" ]; then
        echo "Applying custom DSDT: $vmscfgdir/ACPI-DSDT.bin"
        run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/acpi/0/Config/DsdtFilePath" "$vmscfgdir/ACPI-DSDT.bin"
    fi

    if [ -f "$vmscfgdir/ACPI-SSDT.bin" ]; then
        echo "Applying custom SSDT: $vmscfgdir/ACPI-SSDT.bin"
        run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/acpi/0/Config/SsdtFilePath" "$vmscfgdir/ACPI-SSDT.bin"
    fi

    if [ -f "$vmscfgdir/vgabios386.bin" ]; then
        echo "Applying custom VGA BIOS: $vmscfgdir/vgabios386.bin"
        run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/vga/0/Config/BiosRom" "$vmscfgdir/vgabios386.bin"
    fi

    if [ -f "$vmscfgdir/pcbios386.bin" ]; then
        echo "Applying custom PC BIOS: $vmscfgdir/pcbios386.bin"
        run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/BiosRom" "$vmscfgdir/pcbios386.bin"
    fi
else
    echo "Custom config directory not found: $vmscfgdir (skipping custom BIOS/ACPI files)"
fi

# GUI settings
run_vboxmanage setextradata global GUI/SuppressMessages all
run_vboxmanage setextradata global GUI/ShowMiniToolBar false
run_vboxmanage setextradata "$VM" GUI/DefaultCloseAction PowerOff
run_vboxmanage modifyvm "$VM" --biosbootmenu menuonly

# Boot order settings
# Options: none | floppy | dvd | disk | net
run_vboxmanage modifyvm "$VM" --boot1 "$boot1"
run_vboxmanage modifyvm "$VM" --boot2 none
run_vboxmanage modifyvm "$VM" --boot3 none
run_vboxmanage modifyvm "$VM" --boot4 none

# Input settings
run_vboxmanage setextradata global GUI/Input/MachineShortcuts FullscreenMode=None
run_vboxmanage setextradata global GUI/Input/MachineShortcuts SeamlessMode=None
run_vboxmanage setextradata global GUI/Input/HostKeyCombination 123
run_vboxmanage setextradata global GUI/Input/AutoCapture false

echo ""
echo "VirtualBox VM configuration completed successfully!"
echo "=================================================="
echo "VM Name: $VM"
echo "OS Type: $OS_TYPE"
echo "CPUs: $CPU_COUNT"
echo "Memory: $vm_memory MB"
echo "VRAM: $vga_vram MB"
echo "Graphics Controller: $vm_vga"
echo "Network: $bridge_adapter (MAC: $MAC)"
echo "Boot Method: PXE/Network boot only"
echo "Storage: No disks attached (diskless boot)"

if [ "$enable_vnc" = "true" ]; then
    echo "Remote Access: VNC enabled on port $vnc_port"
elif [ "$enable_rdp" = "true" ]; then
    echo "Remote Access: RDP enabled on port $rdp_port"
else
    echo "Remote Access: Disabled"
fi

echo ""
echo "VM Location: VirtualBox default folder (~VirtualBox VMs/$VM/)"
echo "Hardware spoofing applied: ASUS system with Intel NIC"

# Show custom files status
if [ -d "$vmscfgdir" ]; then
    custom_files_found=false
    echo "Custom files:"
    for file in "vm_splash.bmp" "ACPI-DSDT.bin" "ACPI-SSDT.bin" "vgabios386.bin" "pcbios386.bin"; do
        if [ -f "$vmscfgdir/$file" ]; then
            echo "  ✓ $file"
            custom_files_found=true
        fi
    done
    if [ "$custom_files_found" = "false" ]; then
        echo "  (no custom files found in $vmscfgdir)"
    fi
else
    echo "Custom files: Not configured (using defaults)"
fi

echo ""
echo "Start the VM with:"
echo "  VBoxManage startvm \"$VM\""
echo "  VBoxManage startvm \"$VM\" --type headless"

if [ "$enable_vnc" = "true" ]; then
    echo ""
    echo "Connect via VNC: localhost:$vnc_port"
elif [ "$enable_rdp" = "true" ]; then
    echo ""
    echo "Connect via RDP: localhost:$rdp_port"
fi

