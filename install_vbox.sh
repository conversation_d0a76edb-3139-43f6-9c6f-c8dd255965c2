#!/bin/bash

# VirtualBox VM Configuration Script for Linux
# Converted from AutoHotkey script

# Check if configuration file exists and source it
CONFIG_FILE="vm_config.conf"
if [ -f "$CONFIG_FILE" ]; then
    echo "Loading configuration from $CONFIG_FILE"
    source "$CONFIG_FILE"
else
    echo "Configuration file $CONFIG_FILE not found, using default values"
fi

# Set default variables (will be overridden by config file if it exists)
vmFolder="${vmFolder:-/root/VirtualBox VMs}"
vm="${vm:-vm_name}"
VM="${VM:-vm3}"
MAC="${MAC:-080027123456}"
vm_memory="${vm_memory:-2048}"
vm_mouse="${vm_mouse:-ps2}"
vm_vga="${vm_vga:-vmsvga}"
vga_vram="${vga_vram:-128}"
vm_rdp="${vm_rdp:-off}"
rdp_port="${rdp_port:-3389}"
vmscfgdir="${vmscfgdir:-/root/.config/VirtualBox/}"
boot1="${boot1:-disk}"
bridge_adapter="${bridge_adapter:-eth0}"
enable_chipset_ich9="${enable_chipset_ich9:-false}"

# Function to run VBoxManage commands with error checking
run_vboxmanage() {
    echo "Running: VBoxManage $*"
    VBoxManage "$@" 2>/dev/null
    if [ $? -ne 0 ]; then
        echo "Warning: Command failed: VBoxManage $*"
    fi
}

# Set machine folder and import VM
run_vboxmanage setproperty machinefolder "$vmFolder"
run_vboxmanage import "$vmFolder/$vm.ova" --vsys 0 --vmname "$VM"

# Network configuration
run_vboxmanage modifyvm vm3 --nic1 bridged
run_vboxmanage modifyvm vm3 --bridgeadapter1 "$bridge_adapter"

# Basic VM settings
run_vboxmanage modifyvm vm3 --macaddress1 "$MAC"
run_vboxmanage modifyvm vm3 --memory "$vm_memory"
run_vboxmanage modifyvm vm3 --mouse "$vm_mouse"
run_vboxmanage modifyvm vm3 --graphicscontroller "$vm_vga"
run_vboxmanage modifyvm vm3 --vram "$vga_vram"
run_vboxmanage modifyvm vm3 --vrde "$vm_rdp"
run_vboxmanage modifyvm vm3 --vrdeport "$rdp_port"

# CPU and virtualization settings
run_vboxmanage setextradata vm3 "VBoxInternal/CPUM/EnableHVP" 0
run_vboxmanage setextradata vm3 "VBoxInternal/TM/TSCMode" RealTSCOffset

# DMI BIOS settings
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVendor" "Asus"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVersion" "MB52.88Z.0088.B05.0904162222"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseDate" "08/10/13"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMajor" "5"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMinor" "9"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMajor" "1"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMinor" "0"

# DMI System settings
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiSystemVendor" "Asus"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiSystemProduct" "Asus5"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiSystemVersion" "1.0"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiSystemSerial" "CSN12345678901234567"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiSystemSKU" "FM550EA#ACB"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiSystemFamily" "Ultrabook"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiSystemUuid" "B5FA3000-9403-81E0-3ADA-F46D045CB676"

# DMI Board settings
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBoardVendor" "Asus"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBoardProduct" "Aus-F22788AA"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBoardVersion" "3.0"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBoardSerial" "BSN12345678901234567"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBoardAssetTag" "Base Board Asset Tag#"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBoardLocInChass" "Board Loc In"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiBoardBoardType" 10

# DMI Chassis settings
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiChassisVendor" "Asus Inc."
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiChassisType" 10
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiChassisVersion" "Asus-F22788AA"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiChassisSerial" "CSN12345678901234567"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiChassisAssetTag" "WhiteHouse"

# DMI OEM settings
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxVer" "Extended version info: 1.00.00"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxRev" "Extended revision info: 1A"

# Commented out PIIX3 IDE settings (uncomment if needed)
#run_vboxmanage setextradata vm3 "VBoxInternal/Devices/piix3ide/0/Config/PrimaryMaster/ModelNumber" "Hitachi HTS543232A8A384"
#run_vboxmanage setextradata vm3 "VBoxInternal/Devices/piix3ide/0/Config/PrimaryMaster/FirmwareRevision" "ES2OA60W"
#run_vboxmanage setextradata vm3 "VBoxInternal/Devices/piix3ide/0/Config/PrimaryMaster/SerialNumber" "2E3024L1T2V9KA"
#run_vboxmanage setextradata vm3 "VBoxInternal/Devices/piix3ide/0/Config/SecondaryMaster/ModelNumber" "Slimtype DVD A  DS8A8SH"
#run_vboxmanage setextradata vm3 "VBoxInternal/Devices/piix3ide/0/Config/SecondaryMaster/FirmwareRevision" "KAA2"
#run_vboxmanage setextradata vm3 "VBoxInternal/Devices/piix3ide/0/Config/SecondaryMaster/SerialNumber" "ABCDEF0123456789"
#run_vboxmanage setextradata vm3 "VBoxInternal/Devices/piix3ide/0/Config/SecondaryMaster/ATAPIVendorId" "Slimtype"
#run_vboxmanage setextradata vm3 "VBoxInternal/Devices/piix3ide/0/Config/SecondaryMaster/ATAPIProductId" "DVD A  DS8A8SH"
#run_vboxmanage setextradata vm3 "VBoxInternal/Devices/piix3ide/0/Config/SecondaryMaster/ATAPIRevision" "KAA2"

# AHCI settings
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/ahci/0/Config/Port0/ModelNumber" "Hitachi HTS543230AAA384"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/ahci/0/Config/Port0/FirmwareRevision" "ES2OA60W"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/ahci/0/Config/Port0/SerialNumber" "2E3024L1T2V9KA"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/ahci/0/Config/Port1/ModelNumber" "Slimtype DVD A  DS8A8SH"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/ahci/0/Config/Port1/FirmwareRevision" "KAA2"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/ahci/0/Config/Port1/SerialNumber" "ABCDEF0123456789"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIVendorId" "Slimtype"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIProductId" "DVD A  DS8A8SH"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIRevision" "KAA2"

# ACPI settings
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/acpi/0/Config/AcpiOemId" "ASUS"

# Virtualization and hardware settings
run_vboxmanage modifyvm vm3 --paravirtprovider legacy
run_vboxmanage modifyvm vm3 --bioslogoimagepath "$vmscfgdir/vm_splash.bmp"
run_vboxmanage modifyvm vm3 --hwvirtex on
run_vboxmanage modifyvm vm3 --vtxvpid on
run_vboxmanage modifyvm vm3 --vtxux on
run_vboxmanage modifyvm vm3 --apic on
run_vboxmanage modifyvm vm3 --pae on
run_vboxmanage modifyvm vm3 --longmode on
run_vboxmanage modifyvm vm3 --hpet on
run_vboxmanage modifyvm vm3 --nestedpaging on
run_vboxmanage modifyvm vm3 --largepages on

# Optional ICH9 chipset (enable in config file if needed)
if [ "$enable_chipset_ich9" = "true" ]; then
    run_vboxmanage modifyvm vm3 --chipset ich9
fi

# Custom BIOS and ACPI files
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/acpi/0/Config/DsdtFilePath" "$vmscfgdir/ACPI-DSDT.bin"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/acpi/0/Config/SsdtFilePath" "$vmscfgdir/ACPI-SSDT.bin"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/vga/0/Config/BiosRom" "$vmscfgdir/vgabios386.bin"
run_vboxmanage setextradata vm3 "VBoxInternal/Devices/pcbios/0/Config/BiosRom" "$vmscfgdir/pcbios386.bin"

# GUI settings
run_vboxmanage setextradata global GUI/SuppressMessages all
run_vboxmanage setextradata global GUI/ShowMiniToolBar false
run_vboxmanage setextradata vm3 GUI/DefaultCloseAction PowerOff
run_vboxmanage modifyvm vm3 --biosbootmenu menuonly

# Boot order settings
# Options: none | floppy | dvd | disk | net
run_vboxmanage modifyvm vm3 --boot1 "$boot1"
run_vboxmanage modifyvm vm3 --boot2 none
run_vboxmanage modifyvm vm3 --boot3 none
run_vboxmanage modifyvm vm3 --boot4 none

# Input settings
run_vboxmanage setextradata global GUI/Input/MachineShortcuts FullscreenMode=None
run_vboxmanage setextradata global GUI/Input/MachineShortcuts SeamlessMode=None
run_vboxmanage setextradata global GUI/Input/HostKeyCombination 123
run_vboxmanage setextradata global GUI/Input/AutoCapture false

echo "VirtualBox VM configuration completed successfully!"
echo "VM Name: vm3"
echo "Memory: $vm_memory MB"
echo "Graphics Controller: $vm_vga"
echo "VRAM: $vga_vram MB"
echo "Boot Order: $boot1"

