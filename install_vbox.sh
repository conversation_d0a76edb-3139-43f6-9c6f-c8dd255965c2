#!/bin/bash

# VirtualBox VM Configuration Script for Linux
# Converted from AutoHotkey script with dynamic host detection

# =============================================================================
# CONFIGURATION SECTION - Edit these values as needed
# =============================================================================

# VM Basic Settings
VM="vm3"                           # VM name (OVA filename will be same: MyVM.ova)
vmFolder="$HOME/VirtualBox VMs"     # VM folder path
vmscfgdir="$HOME/vm-config"         # Custom BIOS/ACPI files directory (optional)

# VM Hardware Settings
vm_memory="4096"                    # Memory in MB
vm_mouse="usbtablet"                # Mouse type: ps2, usb, usbtablet
vm_vga="vmsvga"                     # Graphics controller: vboxvga, vmsvga, vboxsvga
vga_vram="256"                      # Video memory in MB
vm_rdp="off"                        # Enable/disable RDP: on, off
rdp_port="3389"                     # RDP port number
boot1="net"                        # Boot device: none, floppy, dvd, disk, net
enable_chipset_ich9="false"         # Set to "true" to enable ICH9 chipset

# VM Creation Settings (only used if CREATE_VM=true)
CREATE_VM="false"                   # Set to "true" to create VM from scratch
OS_TYPE="Linux_64"                  # OS type for new VM
CPU_COUNT="2"                       # Number of CPU cores
VDI_SIZE="20480"                    # VDI size in MB (20GB)
ISO_PATH="$HOME/Downloads/windows.iso"  # Path to ISO file (optional)

# =============================================================================
# DYNAMIC HOST DETECTION
# =============================================================================

echo "Detecting host system configuration..."

# Get primary network interface name (like eth0, enp0s3, etc.)
if command -v ip >/dev/null 2>&1; then
    # Get the interface used for default route
    bridge_adapter=$(ip route | grep '^default' | head -1 | sed 's/.*dev \([^ ]*\).*/\1/')
    if [ -z "$bridge_adapter" ]; then
        # Fallback: get first non-loopback interface
        bridge_adapter=$(ip link show | grep -E '^[0-9]+: [^l][^o]' | head -1 | sed 's/^[0-9]*: \([^:]*\):.*/\1/')
    fi
elif command -v ifconfig >/dev/null 2>&1; then
    # Fallback for older systems
    bridge_adapter=$(route -n | grep '^0.0.0.0' | head -1 | awk '{print $8}')
    if [ -z "$bridge_adapter" ]; then
        bridge_adapter=$(ifconfig | grep -E '^[a-z]' | grep -v '^lo' | head -1 | cut -d: -f1)
    fi
fi

# Default fallback
if [ -z "$bridge_adapter" ]; then
    bridge_adapter="eth0"
    echo "Warning: Could not detect network interface, using default: $bridge_adapter"
else
    echo "Detected network interface: $bridge_adapter"
fi

# Get MAC address from the physical adapter
if command -v ip >/dev/null 2>&1; then
    MAC=$(ip link show "$bridge_adapter" 2>/dev/null | grep -o 'link/ether [^ ]*' | cut -d' ' -f2 | tr -d ':' | tr '[:lower:]' '[:upper:]')
elif command -v ifconfig >/dev/null 2>&1; then
    MAC=$(ifconfig "$bridge_adapter" 2>/dev/null | grep -o 'ether [^ ]*' | cut -d' ' -f2 | tr -d ':' | tr '[:lower:]' '[:upper:]')
fi

# Fallback if MAC detection fails
if [ -z "$MAC" ] || [ ${#MAC} -ne 12 ]; then
    MAC="080027123456"
    echo "Warning: Could not get MAC from $bridge_adapter, using default: $MAC"
else
    echo "Using MAC address from $bridge_adapter: $MAC"
fi

echo "Network configuration: $bridge_adapter (MAC: $MAC)"
echo "VM configuration: $VM (${vm_memory}MB RAM, ${vga_vram}MB VRAM)"
echo

# Function to run VBoxManage commands with error checking
run_vboxmanage() {
    echo "Running: VBoxManage $*"
    VBoxManage "$@" 2>/dev/null
    if [ $? -ne 0 ]; then
        echo "Warning: Command failed: VBoxManage $*"
    fi
}

# =============================================================================
# VM CREATION (if CREATE_VM=true)
# =============================================================================

if [ "$CREATE_VM" = "true" ]; then
    echo "Creating new VM: $VM"
    echo "=================="

    # Check if VM already exists
    if VBoxManage list vms | grep -q "\"$VM\""; then
        echo "Error: VM '$VM' already exists!"
        echo "Remove it first with: VBoxManage unregistervm '$VM' --delete"
        exit 1
    fi

    # Set machine folder
    run_vboxmanage setproperty machinefolder "$vmFolder"

    # Create the VM
    run_vboxmanage createvm --name "$VM" --ostype "$OS_TYPE" --register

    # Basic VM settings
    run_vboxmanage modifyvm "$VM" --memory "$vm_memory"
    run_vboxmanage modifyvm "$VM" --cpus "$CPU_COUNT"
    run_vboxmanage modifyvm "$VM" --vram "$vga_vram"

    # CPU settings
    run_vboxmanage modifyvm "$VM" --pae on
    run_vboxmanage modifyvm "$VM" --longmode on
    run_vboxmanage modifyvm "$VM" --x2apic on
    run_vboxmanage modifyvm "$VM" --largepages on

    # Hardware virtualization
    run_vboxmanage modifyvm "$VM" --hwvirtex on
    run_vboxmanage modifyvm "$VM" --nestedpaging on
    run_vboxmanage modifyvm "$VM" --vtxvpid on
    run_vboxmanage modifyvm "$VM" --vtxux on

    # Other hardware settings
    run_vboxmanage modifyvm "$VM" --hpet on
    run_vboxmanage modifyvm "$VM" --paravirtprovider legacy
    run_vboxmanage modifyvm "$VM" --apic on

    # Graphics and peripherals
    run_vboxmanage modifyvm "$VM" --graphicscontroller "$vm_vga"
    run_vboxmanage modifyvm "$VM" --mouse "$vm_mouse"

    # BIOS settings
    run_vboxmanage modifyvm "$VM" --ioapic on
    run_vboxmanage modifyvm "$VM" --biosbootmenu menuonly

    # Optional ICH9 chipset
    if [ "$enable_chipset_ich9" = "true" ]; then
        run_vboxmanage modifyvm "$VM" --chipset ich9
    fi

    # Create SATA storage controller
    run_vboxmanage storagectl "$VM" --name "SATA" --add sata --controller IntelAhci --portcount 2 --hostiocache on --bootable on

    # Create and attach hard disk
    VDI_PATH="$vmFolder/$VM/${VM}.vdi"
    echo "Creating VDI file: $VDI_PATH (${VDI_SIZE}MB)"
    mkdir -p "$(dirname "$VDI_PATH")"
    run_vboxmanage createhd --filename "$VDI_PATH" --size "$VDI_SIZE" --format VDI
    run_vboxmanage storageattach "$VM" --storagectl "SATA" --port 0 --device 0 --type hdd --medium "$VDI_PATH"

    # Attach ISO if it exists
    if [ -f "$ISO_PATH" ]; then
        echo "Attaching ISO: $ISO_PATH"
        run_vboxmanage storageattach "$VM" --storagectl "SATA" --port 1 --device 0 --type dvddrive --medium "$ISO_PATH"
    else
        echo "ISO not found, creating empty DVD drive"
        run_vboxmanage storageattach "$VM" --storagectl "SATA" --port 1 --device 0 --type dvddrive --medium emptydrive
    fi

    echo "VM creation completed. Now applying configuration..."
    echo
else
    echo "Configuring existing VM: $VM"
    echo "============================"

    # Check if VM exists
    if ! VBoxManage list vms | grep -q "\"$VM\""; then
        echo "Error: VM '$VM' does not exist!"
        echo "Set CREATE_VM=\"true\" to create it, or check the VM name."
        exit 1
    fi

    # Set machine folder
    run_vboxmanage setproperty machinefolder "$vmFolder"
fi

echo

# =============================================================================
# VM CONFIGURATION (applies to both new and existing VMs)
# =============================================================================

# Network configuration
run_vboxmanage modifyvm "$VM" --nic1 bridged
run_vboxmanage modifyvm "$VM" --bridgeadapter1 "$bridge_adapter"

# Basic VM settings
run_vboxmanage modifyvm "$VM" --macaddress1 "$MAC"
run_vboxmanage modifyvm "$VM" --memory "$vm_memory"
run_vboxmanage modifyvm "$VM" --mouse "$vm_mouse"
run_vboxmanage modifyvm "$VM" --graphicscontroller "$vm_vga"
run_vboxmanage modifyvm "$VM" --vram "$vga_vram"
run_vboxmanage modifyvm "$VM" --vrde "$vm_rdp"
run_vboxmanage modifyvm "$VM" --vrdeport "$rdp_port"

# CPU and virtualization settings
run_vboxmanage setextradata "$VM" "VBoxInternal/CPUM/EnableHVP" 0
run_vboxmanage setextradata "$VM" "VBoxInternal/TM/TSCMode" RealTSCOffset

# DMI BIOS settings
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVendor" "Asus"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVersion" "MB52.88Z.0088.B05.0904162222"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseDate" "08/10/13"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMajor" "5"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMinor" "9"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMajor" "1"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMinor" "0"

# DMI System settings
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemVendor" "Asus"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemProduct" "Asus5"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemVersion" "1.0"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemSerial" "CSN12345678901234567"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemSKU" "FM550EA#ACB"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemFamily" "Ultrabook"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemUuid" "B5FA3000-9403-81E0-3ADA-F46D045CB676"

# DMI Board settings
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardVendor" "Asus"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardProduct" "Aus-F22788AA"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardVersion" "3.0"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardSerial" "BSN12345678901234567"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardAssetTag" "Base Board Asset Tag#"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardLocInChass" "Board Loc In"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardBoardType" 10

# DMI Chassis settings
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisVendor" "Asus Inc."
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisType" 10
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisVersion" "Asus-F22788AA"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisSerial" "CSN12345678901234567"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisAssetTag" "WhiteHouse"

# DMI OEM settings
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxVer" "Extended version info: 1.00.00"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxRev" "Extended revision info: 1A"

# AHCI settings
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/ahci/0/Config/Port0/ModelNumber" "Hitachi HTS543230AAA384"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/ahci/0/Config/Port0/FirmwareRevision" "ES2OA60W"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/ahci/0/Config/Port0/SerialNumber" "2E3024L1T2V9KA"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/ahci/0/Config/Port1/ModelNumber" "Slimtype DVD A  DS8A8SH"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/ahci/0/Config/Port1/FirmwareRevision" "KAA2"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/ahci/0/Config/Port1/SerialNumber" "ABCDEF0123456789"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIVendorId" "Slimtype"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIProductId" "DVD A  DS8A8SH"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIRevision" "KAA2"

# ACPI settings
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/acpi/0/Config/AcpiOemId" "ASUS"

# Virtualization and hardware settings
run_vboxmanage modifyvm "$VM" --paravirtprovider legacy
run_vboxmanage modifyvm "$VM" --bioslogoimagepath "$vmscfgdir/vm_splash.bmp"
run_vboxmanage modifyvm "$VM" --hwvirtex on
run_vboxmanage modifyvm "$VM" --vtxvpid on
run_vboxmanage modifyvm "$VM" --vtxux on
run_vboxmanage modifyvm "$VM" --apic on
run_vboxmanage modifyvm "$VM" --pae on
run_vboxmanage modifyvm "$VM" --longmode on
run_vboxmanage modifyvm "$VM" --hpet on
run_vboxmanage modifyvm "$VM" --nestedpaging on
run_vboxmanage modifyvm "$VM" --largepages on
run_vboxmanage modifyvm "$VM" --chipset ich9


# Custom BIOS and ACPI files
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/acpi/0/Config/DsdtFilePath" "$vmscfgdir/ACPI-DSDT.bin"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/acpi/0/Config/SsdtFilePath" "$vmscfgdir/ACPI-SSDT.bin"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/vga/0/Config/BiosRom" "$vmscfgdir/vgabios386.bin"
run_vboxmanage setextradata "$VM" "VBoxInternal/Devices/pcbios/0/Config/BiosRom" "$vmscfgdir/pcbios386.bin"

# GUI settings
run_vboxmanage setextradata global GUI/SuppressMessages all
run_vboxmanage setextradata global GUI/ShowMiniToolBar false
run_vboxmanage setextradata "$VM" GUI/DefaultCloseAction PowerOff
run_vboxmanage modifyvm "$VM" --biosbootmenu menuonly

# Boot order settings
# Options: none | floppy | dvd | disk | net
run_vboxmanage modifyvm "$VM" --boot1 "$boot1"
run_vboxmanage modifyvm "$VM" --boot2 none
run_vboxmanage modifyvm "$VM" --boot3 none
run_vboxmanage modifyvm "$VM" --boot4 none

# Input settings
run_vboxmanage setextradata global GUI/Input/MachineShortcuts FullscreenMode=None
run_vboxmanage setextradata global GUI/Input/MachineShortcuts SeamlessMode=None
run_vboxmanage setextradata global GUI/Input/HostKeyCombination 123
run_vboxmanage setextradata global GUI/Input/AutoCapture false

echo ""
if [ "$CREATE_VM" = "true" ]; then
    echo "VirtualBox VM creation and configuration completed successfully!"
    echo "============================================================="
    echo "VM Name: $VM"
    echo "OS Type: $OS_TYPE"
    echo "CPUs: $CPU_COUNT"
    echo "Memory: $vm_memory MB"
    echo "VRAM: $vga_vram MB"
    echo "Graphics Controller: $vm_vga"
    echo "Network: $bridge_adapter (MAC: $MAC)"
    echo "Hard Disk: $vmFolder/$VM/${VM}.vdi (${VDI_SIZE}MB)"
    if [ -f "$ISO_PATH" ]; then
        echo "DVD: $ISO_PATH"
    else
        echo "DVD: Empty drive"
    fi
    echo "Boot Order: $boot1"
    echo ""
    echo "You can now start the VM with:"
    echo "  VBoxManage startvm \"$VM\""
    echo "  VBoxManage startvm \"$VM\" --type headless"
else
    echo "VirtualBox VM configuration completed successfully!"
    echo "=================================================="
    echo "VM Name: $VM"
    echo "Memory: $vm_memory MB"
    echo "VRAM: $vga_vram MB"
    echo "Graphics Controller: $vm_vga"
    echo "Network: $bridge_adapter (MAC: $MAC)"
    echo "Boot Order: $boot1"
fi
echo ""
echo "Hardware spoofing applied: ASUS system with Hitachi HDD + Slimtype DVD"

