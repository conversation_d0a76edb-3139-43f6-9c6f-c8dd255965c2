This repo is about running virtualbox on debian 13
- it will have 2 scripts, 1st InstallVbox script will be used to create and configure vm with required settings
- 2nd RunVbox scrip will run the vbox vm in fullscreen mode with custom hardware settings got from host vm
- disable all boot, splash, graphics, error and info messages during launch.
- clone nic mac, hardware, cpu, bios, dmi info of host to guest virtual machine
- 
