#!/bin/bash

# VirtualBox Setup Validation Script
# Run this script to check if your system is ready for the VM configuration

echo "=== VirtualBox Setup Validation ==="
echo

# Check if VBoxManage is available
echo "1. Checking VBoxManage availability..."
if command -v VBoxManage &> /dev/null; then
    echo "   ✓ VBoxManage found: $(which VBoxManage)"
    VBOX_VERSION=$(VBoxManage --version 2>/dev/null)
    echo "   ✓ VirtualBox version: $VBOX_VERSION"
else
    echo "   ✗ VBoxManage not found. Please install VirtualBox."
    exit 1
fi

echo

# Check if user is in vboxusers group (Linux specific)
echo "2. Checking user permissions..."
if groups | grep -q vboxusers; then
    echo "   ✓ User is in vboxusers group"
else
    echo "   ⚠ User is not in vboxusers group. You may need to run:"
    echo "     sudo usermod -a -G vboxusers $USER"
    echo "     Then log out and log back in."
fi

echo

# Check available network interfaces
echo "3. Checking network interfaces..."
echo "   Available bridged interfaces:"
VBoxManage list bridgedifs | grep "^Name:" | head -5
echo "   Note: Update 'bridge_adapter' in vm_config.conf with one of these names"

echo

# Check if configuration file exists
echo "4. Checking configuration file..."
if [ -f "vm_config.conf" ]; then
    echo "   ✓ vm_config.conf found"
    
    # Source the config and validate key settings
    source vm_config.conf
    
    echo "   Current configuration:"
    echo "     VM Folder: $vmFolder"
    echo "     VM Name: $VM"
    echo "     OVA File: $vm/$vm.ova"
    echo "     Memory: ${vm_memory}MB"
    echo "     Bridge Adapter: $bridge_adapter"
    
    # Check if VM folder exists
    if [ -d "$vmFolder" ]; then
        echo "   ✓ VM folder exists: $vmFolder"
    else
        echo "   ⚠ VM folder not found: $vmFolder"
        echo "     Please create the folder or update vmFolder in vm_config.conf"
    fi
    
    # Check if OVA file exists
    if [ -f "$vmFolder/$vm.ova" ]; then
        echo "   ✓ OVA file found: $vmFolder/$vm.ova"
    else
        echo "   ⚠ OVA file not found: $vmFolder/$vm.ova"
        echo "     Please place your OVA file in the correct location or update the config"
    fi
    
    # Check if custom config directory exists (if specified)
    if [ "$vmscfgdir" != "/path/to/vm/config" ] && [ -n "$vmscfgdir" ]; then
        if [ -d "$vmscfgdir" ]; then
            echo "   ✓ VM config directory exists: $vmscfgdir"
        else
            echo "   ⚠ VM config directory not found: $vmscfgdir"
            echo "     Custom BIOS files will not be loaded"
        fi
    fi
    
else
    echo "   ✗ vm_config.conf not found"
    echo "     Please create and configure vm_config.conf before running the script"
    exit 1
fi

echo

# Check if VM already exists
echo "5. Checking if VM already exists..."
if VBoxManage list vms | grep -q "\"$VM\""; then
    echo "   ⚠ VM '$VM' already exists"
    echo "     The script will modify the existing VM or may fail"
    echo "     Consider removing the existing VM first:"
    echo "     VBoxManage unregistervm '$VM' --delete"
else
    echo "   ✓ VM '$VM' does not exist - ready for import"
fi

echo

# Final summary
echo "=== Validation Summary ==="
echo "If all checks passed with ✓, you can run:"
echo "  ./install_vbox.sh"
echo
echo "If there are warnings (⚠) or errors (✗), please address them first."
echo "See README.md for detailed instructions."
