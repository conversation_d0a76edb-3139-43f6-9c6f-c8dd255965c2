#!/bin/bash

# VM Creation Validation Script
# Run this before creating the VM to check if everything is ready

echo "=== VirtualBox VM Creation Validation ==="
echo

# Check if VBoxManage is available
echo "1. Checking VirtualBox installation..."
if command -v VBoxManage &> /dev/null; then
    echo "   ✓ VBoxManage found: $(which VBoxManage)"
    VBOX_VERSION=$(VBoxManage --version 2>/dev/null)
    echo "   ✓ VirtualBox version: $VBOX_VERSION"
else
    echo "   ✗ VBoxManage not found. Please install VirtualBox."
    exit 1
fi

echo

# Load configuration
CONFIG_FILE="vm_creation_config.conf"
if [ -f "$CONFIG_FILE" ]; then
    echo "2. Loading configuration from $CONFIG_FILE..."
    source "$CONFIG_FILE"
    echo "   ✓ Configuration loaded"
else
    echo "2. ✗ Configuration file $CONFIG_FILE not found"
    echo "   Please create and configure the file before proceeding."
    exit 1
fi

echo

# Check if VM already exists
echo "3. Checking if VM already exists..."
if VBoxManage list vms | grep -q "\"$VM_NAME\""; then
    echo "   ⚠ VM '$VM_NAME' already exists"
    echo "     The script will fail if you try to create it again."
    echo "     To remove existing VM:"
    echo "     VBoxManage unregistervm '$VM_NAME' --delete"
    echo
    read -p "   Do you want to continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "   Aborted by user."
        exit 1
    fi
else
    echo "   ✓ VM '$VM_NAME' does not exist - ready for creation"
fi

echo

# Check storage paths
echo "4. Checking storage configuration..."

# Check VDI path
VDI_DIR=$(dirname "$VDI_PATH")
if [ -d "$VDI_DIR" ]; then
    echo "   ✓ VDI directory exists: $VDI_DIR"
elif [ "$VDI_PATH" != "/path/to/your/windows.vdi" ]; then
    echo "   ⚠ VDI directory does not exist: $VDI_DIR"
    echo "     The script will attempt to create it."
else
    echo "   ✗ VDI path not configured: $VDI_PATH"
    echo "     Please update VDI_PATH in $CONFIG_FILE"
fi

if [ -f "$VDI_PATH" ]; then
    echo "   ✓ VDI file already exists: $VDI_PATH"
    VDI_SIZE_ACTUAL=$(VBoxManage showhdinfo "$VDI_PATH" 2>/dev/null | grep "Logical size" | awk '{print $3}')
    if [ -n "$VDI_SIZE_ACTUAL" ]; then
        echo "     Current size: $VDI_SIZE_ACTUAL MB"
    fi
else
    echo "   ⚠ VDI file will be created: $VDI_PATH (${VDI_SIZE}MB)"
fi

# Check ISO path
if [ -f "$ISO_PATH" ]; then
    echo "   ✓ ISO file found: $ISO_PATH"
    ISO_SIZE=$(du -h "$ISO_PATH" | cut -f1)
    echo "     Size: $ISO_SIZE"
elif [ "$ISO_PATH" != "/path/to/your/windows_iso_file.iso" ]; then
    echo "   ⚠ ISO file not found: $ISO_PATH"
    echo "     VM will be created with empty DVD drive"
else
    echo "   ✗ ISO path not configured: $ISO_PATH"
    echo "     Please update ISO_PATH in $CONFIG_FILE"
fi

# Check BIOS logo
if [ -f "$BIOS_LOGO_PATH" ]; then
    echo "   ✓ BIOS logo found: $BIOS_LOGO_PATH"
elif [ "$BIOS_LOGO_PATH" != "/path/to/your/vm_splash.bmp" ]; then
    echo "   ⚠ BIOS logo not found: $BIOS_LOGO_PATH"
    echo "     VM will be created without custom BIOS logo"
else
    echo "   ⚠ BIOS logo path not configured (optional)"
fi

echo

# Check network configuration
echo "5. Checking network configuration..."
echo "   Available bridged interfaces:"
VBoxManage list bridgedifs | grep "^Name:" | head -5
echo
echo "   Configured bridge adapter: $BRIDGE_ADAPTER"
if VBoxManage list bridgedifs | grep -q "$BRIDGE_ADAPTER"; then
    echo "   ✓ Bridge adapter found"
else
    echo "   ⚠ Bridge adapter not found in available interfaces"
    echo "     Please update BRIDGE_ADAPTER in $CONFIG_FILE"
fi

echo

# Final summary
echo "=== Validation Summary ==="
echo "VM Name: $VM_NAME"
echo "UUID: $VM_UUID"
echo "RAM: ${RAM_SIZE}MB"
echo "CPUs: $CPU_COUNT"
echo "VRAM: ${VRAM_SIZE}MB"
echo "VDI: $VDI_PATH"
echo "ISO: $ISO_PATH"
echo "Network: $BRIDGE_ADAPTER"
echo
echo "If validation passed, you can create the VM with:"
echo "  ./create_vm_from_xml.sh"
