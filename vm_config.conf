# VirtualBox VM Configuration File
# Edit these variables according to your setup

# VM Paths and Names
vmFolder="$HOME/VirtualBox VMs"    # Default VirtualBox VM folder
vm="MyVM"                          # Name of your OVA file (without .ova extension)
VM="vm3"                           # Name for the imported VM in VirtualBox
vmscfgdir="$HOME/vm-config"        # Path to custom BIOS/ACPI files (optional)

# Network Settings
MAC="080027123456"                 # MAC address (change last 6 digits for uniqueness)
bridge_adapter="eth0"              # Change to your network interface (run 'ip link show' to see available)

# VM Hardware Settings
vm_memory="4096"                   # Memory in MB (4GB recommended)
vm_mouse="usbtablet"               # Mouse type: ps2, usb, usbtablet (usbtablet recommended)
vm_vga="vmsvga"                    # Graphics controller: vboxvga, vmsvga, vboxsvga
vga_vram="256"                     # Video memory in MB (256MB recommended for modern OSes)

# Remote Desktop Settings
vm_rdp="off"                       # Enable/disable RDP: on, off
rdp_port="3389"                    # RDP port number

# Boot Settings
boot1="disk"                       # Boot device: none, floppy, dvd, disk, net

# Additional Settings
enable_chipset_ich9="false"        # Set to "true" to enable ICH9 chipset (recommended for newer OSes)

# Common network interface names by distribution:
# Ubuntu/Debian: eth0, enp0s3, wlan0
# CentOS/RHEL: eth0, ens33, enp0s3
# Arch Linux: eth0, enp0s3, wlp2s0
# To find yours: ip link show
