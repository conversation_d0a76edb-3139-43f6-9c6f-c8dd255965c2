# VirtualBox VM Creation Configuration
# Edit these variables to match your system and requirements

# Basic VM Settings
VM_NAME="vm3"
VM_UUID="8581b929-4ccb-4a9f-bba4-1a9a3f2ff77d"  # Leave as-is or generate new UUID
OS_TYPE="Linux_64"
RAM_SIZE="4096"        # Memory in MB
CPU_COUNT="2"          # Number of CPU cores
VRAM_SIZE="16"         # Video memory in MB

# Network Settings
MAC_ADDRESS="E0D55E8749A3"  # MAC address from XML
BRIDGE_ADAPTER="Intel(R) Ethernet Connection (7) I219-V"  # Update to match your network adapter

# Storage Paths (IMPORTANT: Update these paths for your system)
VDI_PATH="/path/to/your/windows.vdi"           # Path where VDI file will be created/located
ISO_PATH="/path/to/your/windows_iso_file.iso"  # Path to your Windows ISO file
BIOS_LOGO_PATH="/path/to/your/vm_splash.bmp"   # Optional: Custom BIOS splash screen

# VDI Settings (only used if VDI file doesn't exist)
VDI_SIZE="20480"       # Size in MB (20GB default)

# Remote Desktop Settings
VRDE_PORT="5389"       # Remote desktop port

# Linux Path Examples:
# VDI_PATH="$HOME/VirtualBox VMs/vm3/windows.vdi"
# ISO_PATH="$HOME/Downloads/windows_10_iot_enterprise_ltsc_2021_x64.iso"
# BIOS_LOGO_PATH="$HOME/vm-config/vm_splash.bmp"

# Windows Path Examples (if running on Windows with WSL/Git Bash):
# VDI_PATH="C:/VirtualBox VMs/vm3/windows.vdi"
# ISO_PATH="E:/ISO & DiskImages/Windows/en-us_windows_10_iot_enterprise_ltsc_2021_x64_dvd_257ad90f.iso"
# BIOS_LOGO_PATH="C:/Apps/vm3/bios/vm_splash.bmp"

# Network Adapter Examples:
# Linux: "eth0", "enp0s3", "wlan0", "br0"
# Windows: "Intel(R) Ethernet Connection", "Realtek PCIe GbE Family Controller"
# To find your adapter: VBoxManage list bridgedifs
